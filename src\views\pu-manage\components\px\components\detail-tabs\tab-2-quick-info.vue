<template>
  <div class="tab2-quick-info">
    <a-textarea v-model="showText" :rows="4" :autoSize="{ minRows: 10, maxRows: 10 }" style="resize: none;" />
    <div class="quick-info-box flex">
      <a-button class="mt4" @click="handleSave">保存</a-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Tab2QuickInfo',
  data () {
    return {
      showText: '谭@世岐111，又名老四，【字号：世蛸】，【履历：北京大学，博士，国防科技研究院1，工程师】，【简介：这是一个简介】，葬后山，生子谭国瑚2，\n' +
        '配刘@大力，生于二〇〇六年，【履历：青蛙大学，超本，国防，干掉小日子】，【简介：这是一个很小的简介】，嗣子谭邦'
    }
  },
  // props: {
  //   fastText: {
  //     type: String,
  //     default: ''
  //   }
  // },
  // watch: {
  //   fastText (val) {
  //     this.showText = val
  //   }
  // }
  methods: {
    handleSave () {
      this.$message.info('该功能暂未实现')
    }
  }
}
</script>
<style lang="less" scoped>
.tab2-quick-info {
  padding: 10px 20px;
  background-color:#fff;
}
</style>
