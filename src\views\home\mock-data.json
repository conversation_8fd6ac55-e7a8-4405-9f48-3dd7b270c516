{"msg": 1, "msbox": null, "data": [{"id": 15775, "name": "我的家族", "sort": 1, "isExample": 0, "projectList": []}, {"id": 29509, "name": "这是一个分组", "sort": 2, "isExample": 0, "projectList": [{"id": "yjEYWhDelfoi8", "type": 1, "name": "江都堂董氏江夏支谱", "showName": "江都堂董氏江夏支谱", "ancestor": "益三公", "firstName": "董", "hall": "江都塘", "province": "湖南省", "city": "永州市", "district": "新田县", "description": "", "wxQrCode": "https://cdn.baijiayoupu.com/api/images/qr/project/wechat/76277.png", "appQrCode": "https://cdn.baijiayoupu.com/api/images/qr/project/app/76277.png", "h5Preview": "https://cdn.baijiayoupu.com/api/images/qr/firstName/h5/董.png", "isExample": 0, "isTeam": 0, "isManage": 0, "permission": null, "groupId": 29509, "sort": 1, "adminId": 466784, "adminName": "150***590", "adminPhone": "150********590", "memberLimit": 0, "memberCount": 23409, "mappingMode": null, "clanBuyLimit": 4, "projectNumber": null, "level": {"id": 8, "name": "专享版", "clanLimit": 1, "clanMemberLimit": 0, "projectLimit": 1, "fileLimit": 300, "printPlanLimit": 5, "printSampleLimit": 3, "printFreeCount": 0, "printPayType": 1, "printPrice": 100, "printFreeQuota": 0, "discount": 0, "commissionRatio": 0, "previewPrice": 0, "printPeoplePrice": 80, "previewPeoplePrice": 0, "isAgent": 0, "viewCpgBookCount": 0, "projectBuySwitch": true, "clanBuySwitch": true, "clanArticleLimit": 100, "printSampleSwitch": true, "printDownloadSwitch": true, "printPdfSwitch": false, "printPublishSwitch": true, "reelCreateLimit": 3, "reelGenerateLimit": 3, "reelGenerateSwitch": true, "reelDownloadSwitch": true, "reelHdgenerateSwitch": false, "reelHdgeneratePrice": 80, "managerLimit": 2, "collaboratorLimit": 5, "collectorLimit": 5, "expireTime": "长期"}}]}, {"id": -1, "name": "优秀范例", "sort": 0, "isExample": 1, "projectList": [{"id": "9118", "type": 0, "name": "莲山谱牒范例", "showName": "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t \t\t\t\t\t\t\t\t\t\t\t\t莲山谱牒范例", "ancestor": "聚元", "firstName": "杨", "hall": "", "province": "广东省", "city": "广州市", "district": "番禺区", "description": "莲山谱牒家族范例", "wxQrCode": "https://cdn.baijiayoupu.com/api/images/qr/project/wechat/9118.png", "appQrCode": "https://cdn.baijiayoupu.com/api/images/qr/project/app/9118.png", "h5Preview": "https://cdn.baijiayoupu.com/api/images/qr/firstName/h5/杨.png", "isExample": 1, "isTeam": 0, "isManage": 0, "permission": null, "groupId": 21309, "sort": 3, "adminId": 7924, "adminName": "厚德载物", "adminPhone": "158********908", "memberLimit": 0, "memberCount": 54, "mappingMode": null, "clanBuyLimit": 0, "projectNumber": "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t \t\t\t\t\t\t\t\t\t\t\t\t", "level": {"id": 8, "name": "专享版", "clanLimit": 1, "clanMemberLimit": 0, "projectLimit": 1, "fileLimit": 300, "printPlanLimit": 5, "printSampleLimit": 3, "printFreeCount": 0, "printPayType": 1, "printPrice": 100, "printFreeQuota": 0, "discount": 0, "commissionRatio": 0, "previewPrice": 0, "printPeoplePrice": 80, "previewPeoplePrice": 0, "isAgent": 0, "viewCpgBookCount": 0, "projectBuySwitch": true, "clanBuySwitch": true, "clanArticleLimit": 100, "printSampleSwitch": true, "printDownloadSwitch": true, "printPdfSwitch": false, "printPublishSwitch": true, "reelCreateLimit": 3, "reelGenerateLimit": 3, "reelGenerateSwitch": true, "reelDownloadSwitch": true, "reelHdgenerateSwitch": false, "reelHdgeneratePrice": 80, "managerLimit": 2, "collaboratorLimit": 5, "collectorLimit": 5, "expireTime": "长期"}}]}]}