import storage from 'store'
import { addClan, groupClanList } from '@/api/family'

const family = {
  state: {
    hasLoadedGroup: !!storage.get('groupClan')?.length, // 如果localStorage中有数据，则认为已加载
    groups: storage.get('groups') || [],
    groupClan: storage.get('groupClan') || [],
    currentSelectedGroupId: storage.get('SET_CURRENT_GROUP_ID') || '',
    currentSelectedClanId: storage.get('SET_CURRENT_CLAN_ID') || ''
  },
  mutations: {
    SET_GROUPS: (state, groups) => {
      const groupList = groups.group_list || []
      const list = []
      state.hasLoadedGroup = true
      state.groupClan = groupList
      storage.set('groupClan', groupList)
      groupList.forEach(item => {
        list.push({
          id: item.id,
          group_name: item.group_name,
          sort_order: item.sort_order
        })
      })
      state.groups = list
      storage.set('groups', list)
      if (!state.currentSelectedGroupId && list.length) {
        const existGroupId = localStorage.getItem('SET_CURRENT_GROUP_ID')
        const groupObj = groupList.find(item => `${item.id}` === existGroupId)
        const groupId = groupObj ? groupObj.id : groupList[0].id
        state.currentSelectedGroupId = groupId
        storage.set('SET_CURRENT_GROUP_ID', groupId)
        const clanList = groupObj ? groupObj.clan_list : groupList[0].clan_list
        if (clanList?.length > 0) {
          const existClanId = localStorage.getItem('SET_CURRENT_CLAN_ID')
          const clanObj = clanList.find(item => `${item.id}` === existClanId)
          const clanId = clanObj ? clanObj.id : clanList[0].id
          state.currentSelectedClanId = clanId
          storage.set('SET_CURRENT_CLAN_ID', clanId)
        }
      }
    },
    SET_CURRENT_GROUP_ID: (state, id) => {
      state.currentSelectedGroupId = id
      storage.set('SET_CURRENT_GROUP_ID', id)
    },
    SET_CURRENT_CLAN_ID: (state, id) => {
      state.currentSelectedClanId = id
      storage.set('SET_CURRENT_CLAN_ID', id)
    }
  },
  actions: {
    SetCurrentGroupId ({ commit }, payload) {
      commit('SET_CURRENT_GROUP_ID', payload)
      storage.set('SET_CURRENT_GROUP_ID', payload)
    },
    GetCurrentGroup ({ commit, state }) {
      return new Promise((resolve, reject) => {
          try {
            const group = state.groupClan.find(item => item.id === state.currentSelectedGroupId)
            resolve(group)
          } catch (e) {
            reject(e)
          }
        })
    },
    SetCurrentClanId ({ commit }, payload) {
      commit('SET_CURRENT_CLAN_ID', payload)
    },
    GetCurrentClan ({ state }) {
      return new Promise((resolve, reject) => {
          try {
            const group = state.groupClan.find(item => item.id === state.currentSelectedGroupId)
            const clan = group.clan_list.find(item => item.id === state.currentSelectedClanId)
            resolve(clan)
          } catch (e) {
            reject(e)
          }
        })
    },
     AddClan ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addClan(payload)
          .then(json => {
            console.log('result', json)
            resolve()
          })
          .catch((e) => {
            reject(e)
          })
        })
      },
     async GroupClanList ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        groupClanList(payload)
          .then(json => {
            commit('SET_GROUPS', json.data)
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
        })
     }
    }
}
export default family
