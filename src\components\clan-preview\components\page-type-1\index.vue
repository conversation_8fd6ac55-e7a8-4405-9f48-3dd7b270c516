<template>
  <div class="page preview-page p2 even">
    <div class="content content-0">
      <div class="head">
        <div class="column name">
          <clan-mark-img type="down" class-str="mark mark-top" />
          <div v-for="(item, index) in getValue('head.name')" :key="index">{{ item }}</div>
          <clan-mark-img type="up" class-str="mark mark-top" />
        </div>
        <div class="column volume">
          <span v-for="(item, index) in getValue('head.volume')" :key="index">{{ item }}</span>
        </div>
        <div class="column title">
          <div>
            <div v-for="(item, index) in getValue('head.title')" :key="index">{{ item }}</div>
          </div>
          <div>
            <div v-for="(item, index) in getValue('head.other')" :key="index">{{ item }}</div>
          </div>
        </div>
        <div class="page-index">{{ getValue( 'pageIndex', false) + 1 }}</div>
      </div>
      <div>
        <div class="body index" :class="getValue('firstPage', false) ? 'first-page' : '' ">
          <div class="title" v-if="getValue('firstPage', false)">
            <span v-for="str in getValue('head.title')" :key="str">{{ str }}</span>
          </div>
          <div class="column" v-for="(column, idx) in getValue('columns', false)" :key="idx">
            <template v-for="(item, indexItem) in column.itemData">
              <div v-if="item.type === 1" :key="indexItem">
                <div class="generation">{{ item.text || '' }}</div>
              </div>
              <div v-else :key="indexItem">
                <div class="item">
                  <div class="item-name">{{ item.text || '' }}</div>
                  <div class="item-page">{{ item.selfPage2 || '' }}</div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { get } from 'lodash'
import ClanMarkImg from '@/components/clan-mark'

export default {
  name: 'PageType1',
  components: { ClanMarkImg },
  props: {
    page: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    getValue (field, transform = true) {
      return transform ? get(this.page, field, '').split('') : get(this.page, field, '')
    }
  }
}
</script>
<style lang='less' scoped>
.page {
  margin: 0 auto;
  width: 448px;
  height: 706px;
  background-color: #fff;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.preview-page{
  position:relative;
  box-sizing:border-box;
  background-size:cover;
  transform-style:preserve-3d;
  width:448px;
  height:666px;
  background-color:rgb(255, 255, 255);
  box-shadow:rgba(0, 0, 0, 0.2) 0px 0px 20px;
  font-family:makefont, 宋体;
  page-break-after:avoid;

  .content {
    height: 622px;
    border: 2px solid #333;
    display: flex;
    //margin: 40px 40px 0;
    overflow: hidden;
    .head {
      flex-shrink: 0;
      width: 42px;
      box-sizing: border-box;
      padding: 2px;
      height: 100%;
      display: flex;
      flex-direction: column;
      border-right: 2px solid #333;
      font-size: 18px;
      .column {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 3px;
        padding: 10px 0;
      }
      .name {
        font-size: 20px;
        padding: 0;
        flex-shrink: 0;
        overflow: hidden;
        .mark {
          display: block;
          width: 100%;
          height: auto;
        }
        .mark-top {
          transform: translateY(-1px);
        }
        .mark-bottom {
          transform: translateY(2px);
        }
      }
      .volume {
        height: 80px;
        justify-content: center;
        flex-shrink: 0;
      }
      .title {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
      }
    }
    .body {
      flex-grow: 1;
      width: 408px;
      height: 100%;
      box-sizing: border-box;
    }
    .body.index {
      display: flex;
      flex-direction: row-reverse;
      padding: 10px 0;
      font-size: 16px;
      line-height: 1;
      .title {
        flex-shrink: 0;
        width: 50px;
        border-left: 2px solid #333;
        font-size: 20px;
        font-weight: 700;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      .column {
        flex-grow: 1;
        box-sizing: border-box;
        flex-shrink: 0;
        border-left: 2px solid;
        padding: 0 15px;
        width: 33%;
        .generation {
          background-color: #333;
          color: #fff;
          text-align: center;
          padding: 5px 0;
          margin-bottom: 10px;
        }
        .item {
          padding: 0 3px 10px;
          display: flex;
          justify-content: space-between;

          .item-name {
            white-space: nowrap;
          }
          .item-page {
            flex-shrink: 0;
          }
        }
      }
    }
    .body.index.first-page {
      .column{
        width: calc(33.33333% - 16.66667px);
      }
    }
  }
  .content.content-0 {
    flex-direction: row-reverse;
    .head {
      border-right: none;
      border-left: 2px solid #333;
    }
  }
}
</style>
