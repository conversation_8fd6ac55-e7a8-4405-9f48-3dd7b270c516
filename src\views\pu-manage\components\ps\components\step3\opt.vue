<template>
  <div class="flex flex-direction-column first-column text-center opt ">
    <div class="mt8 c-pointer c-pointer-hover" @click="showBaseInfo">
      <template v-if="showBase">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarzhankai-xia" />
      </template>
      <template v-else>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarshouqi" />
      </template>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_SAVE)">
      <span class="save-bg">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarbaocun" />
      </span>
      <div>保存</div>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_ADD_PW)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarjia" />
      <div>谱文</div>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_ADD_SX)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarjia" />
      <div>世系</div>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_ADD_YL)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarjia" />
      <div>源流</div>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_ADD_ZJ)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarjia" />
      <div>章节</div>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_MOVE)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbaryidong" />
      <div>移动</div>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_DELETE)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1" />
      <div>删除</div>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_RETURN)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarfanhui" />
      <div>返回</div>
    </div>
    <div class="mt16 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_OUTPUT_SETTING)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarPrint" />
      <div>输出<br/>设置</div>
    </div>
    <div class="mt16 mb20 c-pointer c-pointer-hover" @click="handleClick(actionTypes.PS_RIGHT_OPTION_COPY)">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarfuzhi" />
      <div>复制</div>
    </div>
  </div>
</template>
<script>
import * as actionTypes from '@/store/modules/ps-action-type'

export default {
  name: 'RightQuickOpt',
  data () {
    return {
      showBase: true,
      actionTypes
    }
  },
  methods: {
    showBaseInfo () {
      this.showBase = !this.showBase
      this.$emit('toggle', this.showBase)
    },
    handleClick (type) {
      this.$emit('click', type)
    },
    handleFenFang () {}
  }
}
</script>
<style lang='less' scoped>
.opt {
  width: 60px;
  overflow-y:auto;
  height: calc(100vh - 100px);
  background-color: #fff;
  i { font-size: 20px}
  .save-bg {
    width: 36px;
    height: 36px;
    background-image: linear-gradient(45deg, #40a9ff 10%, #40a9ff 70%);
    border-radius: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    i {
      color: #fff;
      font-size: 20px;
    }
  }
}
</style>
