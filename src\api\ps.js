import request from '@/utils/request'

const dynamicPre = ':clanId'
const dynamicBookPre = ':bookId'
const pwApi = {
  saveClanBook: '/web/family/:clanId/clanBookPrint/save',
  saveCoverConfig: '/web/family/:clanId/:bookId/saveCoverConfig',
  deleteClanBook: '/web/family/:clanId/:bookId/deleted',
  getClanBookPrintPlan: '/web/family/:clanId/getClanBookPrintPlan',
  getBookDetail: '/web/family/:clanId/:bookId/bookPrintPlan/detail',
  getBookPrintContent: '/web/family/:clanId/:bookId/book-print/content',
  saveBookPrintContent: '/web/family/:clanId/:bookId/book-print/content-save'
}

/**
 * 新增/保存谱书打印计划
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} [parameter.id] - 谱书id,为0则新增
 * @param {string} parameter.plan_name - 谱书名称
 * @param {string} [parameter.volume] - 卷号
 * @param {string} parameter.header - 页眉名称
 * @param {string} [parameter.hall] - 郡望堂号
 * @param {string} [parameter.editor] - 编者
 * @param {string} [parameter.edit_date] - 修编日期
 * @param {number} [parameter.first_page] - 起始页
 * @returns {Promise<object>}
 */
export async function saveClanBook (parameter) {
  return request({
    url: pwApi.saveClanBook.replace(dynamicPre, parameter.clan_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 【谱书】保存封面配置
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.book_id - 谱书ID (用于URL)
 * @param {number} [parameter.show_cover] - 是否显示封面(0:不显示,1:显示)
 * @param {number} [parameter.type] - 封面类型(1:标准型,2:简约型,3:传统型)
 * @param {string} [parameter.title] - 封面主标题
 * @param {string} [parameter.sub_title] - 封面副标题
 * @returns {Promise<object>}
 */
export async function saveCoverConfig (parameter) {
  return request({
    url: pwApi.saveCoverConfig.replace(dynamicPre, parameter.clan_id).replace(dynamicBookPre, parameter.book_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 【谱书】通过id删除谱书打印计划数据
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.book_id - 谱书ID (用于URL)
 * @returns {Promise<object>}
 */
export async function deleteClanBook (parameter) {
  return request({
    url: pwApi.deleteClanBook.replace(dynamicPre, parameter.clan_id).replace(dynamicBookPre, parameter.book_id),
    method: 'get',
    data: parameter
  })
}

/**
 * 获取谱书打印计划列表
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @returns {Promise<object>}
 */
export async function getClanBookPrintPlan (parameter) {
  return request({
    url: pwApi.getClanBookPrintPlan.replace(dynamicPre, parameter.clan_id),
    method: 'get',
    data: parameter
  })
}

/**
 * 【谱书】谱书打印计划详情
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.book_id - 谱书ID (用于URL)
 * @returns {Promise<object>}
 */
export async function getBookDetail (parameter) {
  return request({
    url: pwApi.getBookDetail.replace(dynamicPre, parameter.clan_id).replace(dynamicBookPre, parameter.book_id),
    method: 'get',
    data: parameter
  })
}

/**
 * 获取谱书打印内容
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.book_id - 谱书ID (用于URL)
 * @returns {Promise<object>}
 */
export async function getBookPrintDetail (parameter) {
  return request({
    url: pwApi.getBookPrintContent.replace(dynamicPre, parameter.clan_id).replace(dynamicBookPre, parameter.book_id),
    method: 'get',
    data: parameter
  })
}

/**
 * 保存谱书打印内容
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.book_id - 谱书ID (用于URL)
 * @param {Array<object>} [parameter.contents] - 需要添加的内容
 * @returns {Promise<object>}
 */
export async function saveBookPrintContent (parameter) {
  return request({
    url: pwApi.saveBookPrintContent.replace(dynamicPre, parameter.clan_id).replace(dynamicBookPre, parameter.book_id),
    method: 'post',
    data: parameter
  })
}
