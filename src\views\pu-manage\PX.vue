<template>
  <a-spin size="large" :spinning="loading">
    <div class="pu-xi-container">
      <!-- 世系图菜单 -->
      <page-top v-if="currentSelectedPedigreeId" :selectedNode="currentSelectedNode" />
      <div class="flex flex-direction-row">
        <!-- 左侧谱系分支菜单 -->
        <left-content />
        <div class="tree-container">
          <!-- 统一滚动容器 -->
          <div class="unified-scroll-container">
            <!-- 世系展示bar -->
            <generation
              v-if="currentSelectedPedigreeId"
              class="generation-bar-fixed"
              :list="generationList"
              :selected="currentSelectedGeneration" />
            <!-- 世系树形图内容 -->
            <div class="tree-content">
              <clan-tree
                :show-line="true"
                :nodes="treeData"
                :default-expanded-keys="expandedKeys"
                :defaultCheckedKeys.sync="expandedKeys"
                :selected-key="currentSelectedKey"
                @click="handleSelectedNode"
                @wifeClick="handleWifeSelected"
                @addBrother="handleAddBrother"
                @addChild="handleAddChild" />
              <a-empty v-show="treeData?.length === 0" />
            </div>
          </div>
        </div>
        <!-- 右侧详情菜单 -->
        <right-content
          v-if="currentSelectedPedigreeId"
          :selected-node-id="currentSelectedKey"
          :selectedNode="currentSelectedNode"
          :fatherId="currentSelectedNodeFatherId"
          :father="currentSelectedNodeFatherNode"
          @deleteNode="handleDeleteNode"
          @updateTreeSelection="handleUpdateTreeSelection"
          @handleSelectedNode="handleSelectedNodeFromRightContent"
          @updateTreeNodeData="handleUpdateTreeNodeData" />
      </div>
    </div>
  </a-spin>
</template>
<script>
import Top from './components/px/top'
import { mapState, mapActions } from 'vuex'
import Generation from '@/views/pu-manage/components/px/generation'
import TupuTree from '@/views/pu-manage/components/px/tupu-tree'
import LeftContent from '@/views/pu-manage/components/px/left-content'
import RightContent from '@/views/pu-manage/components/px/right-content'
import ClanTree from '@/components/clan-tree/Tree'
import ClanPreview from '@/views/pu-manage/components/px/clan-preview'
import { getSpecificLevelIdList } from '@/utils/getLevelIdsUtls'
import { findNodeById } from '@/utils/findNodeUtil'

function AddNode (data) {
  const { generation, rankingText = '' } = data
  this.id = new Date().getTime()
  this.fullName = '添加节点'
  this.generation = generation
  this.rankingText = rankingText
}

function transformData (data) {
  data.forEach(item => {
    item.title = item.full_name || ''
    item.key = item.id
    item.writeMode = 2
    if (item.children && item.children.length > 0) {
      transformData(item.children)
    } else {
      item.children = []
    }
  })
}

export default {
  components: {
    RightContent,
    LeftContent,
    TupuTree,
    Generation,
    ClanTree,
    ClanPreview,
    pageTop: Top
  },
  data () {
    return {
      treeData: [],
      loading: false,
      expandedKeys: [],
      generationList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // 默认显示10个世代
      currentSelectedKey: '',
      currentSelectedNode: null,
      currentSelectedGeneration: -999,
      currentSelectedNodeFatherId: '', // 当前选中节点的父节点Id
      currentSelectedNodeFatherNode: null // 当前选中节点的父节点
    }
  },
  computed: {
    ...mapState({
      pedigreeMemberListLoading: state => state.px.pedigreeMemberListLoading,
      currentPedigreeMemberList: state => state.px.currentPedigreeMemberList,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  watch: {
    pedigreeMemberListLoading: {
      handler (val) {
        this.loading = val
      },
      immediate: true
    },
    currentPedigreeMemberList: {
      handler (val, oldVal) {
        if (val) {
          const data = [val]
          transformData(data)
          const idList = getSpecificLevelIdList(data)
          this.treeData = data

          // 如果是首次加载或者没有旧数据，使用默认展开状态
          // 如果是刷新操作且有旧数据，保持当前展开状态
          if (!oldVal || this.expandedKeys.length === 0) {
            // 默认展开前三个世代
            this.expandedKeys = this.getDefaultExpandedKeys(data, 3)
          }
          // 如果有旧数据且当前有展开状态，保持不变（expandedKeys不重置）
          if (val.generation) {
            this.currentSelectedGeneration = val.generation
            // 计算树中的最小和最大世代
            const minGeneration = this.getMinGeneration(data)
            const maxGeneration = this.getMaxGeneration(data)
            // 从始祖世代开始，最少显示10个世代，然后根据实际数据预留3个世代
            const startGeneration = minGeneration
            const minEndGeneration = startGeneration + 9 // 最少10个世代
            const dataBasedEndGeneration = maxGeneration + 3 // 基于数据的世代+3
            const endGeneration = Math.max(minEndGeneration, dataBasedEndGeneration)
            this.generationList = Array(endGeneration - startGeneration + 1).fill(0).map((_, index) => index + startGeneration)
          }
          if (idList?.length > 0) {
            const node = findNodeById(data, idList[0])
            if (node) {
              this.currentSelectedKey = node.id
              this.currentSelectedNode = node
              this.currentSelectedNodeFatherId = node.father_id || 0
              if (node.father_id) {
                this.currentSelectedNodeFatherNode = findNodeById(data, this.currentSelectedNodeFatherId)
              }
            }
          }
        } else {
          this.treeData = []
          this.generationList = []
          this.currentSelectedGeneration = -999
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions(['DeletedMember', 'GetNodeById', 'RefreshPedigreeMemberList']),
    getItemClass (treeKey) {
      if (`${treeKey}` === `${this.currentSelectedKey}`) {
        return 'text selected'
      }
      return 'text'
    },
    handleSelectedNode (key, node) {
      this.currentSelectedKey = key
      this.currentSelectedNode = node
      this.currentSelectedGeneration = node.generation
      // 重新计算世代列表，确保包含足够的世代用于添加子世代
      const minGeneration = this.getMinGeneration(this.treeData)
      const maxGeneration = this.getMaxGeneration(this.treeData)
      const startGeneration = minGeneration
      const minEndGeneration = startGeneration + 9 // 最少10个世代
      const dataBasedEndGeneration = Math.max(maxGeneration + 3, node.generation + 3) // 基于数据的世代+3
      const endGeneration = Math.max(minEndGeneration, dataBasedEndGeneration)
      this.generationList = Array(endGeneration - startGeneration + 1).fill(0).map((_, index) => index + startGeneration)
      this.currentSelectedNodeFatherId = node.father_id || -1
      if (node.father_id) {
        this.currentSelectedNodeFatherNode = findNodeById(this.treeData, this.currentSelectedNodeFatherId)
      }
    },
    handleAddBrother (key, node) {
      this.currentSelectedKey = key
      this.currentSelectedGeneration = node.generation
    },
    handleAddChild (key, node) {
    },
    handleDeleteNode () {
      const {
        DeletedMember,
        RefreshPedigreeMemberList,
        GetNodeById,
        currentSelectedPedigreeId,
        currentSelectedNode
      } = this
      const params = {
        'clan_id': this.currentSelectedClanId,
        'pedigree_id': currentSelectedPedigreeId,
        'member_id': this.currentSelectedKey
      }
      const that = this
      this.$confirm({
        title: '',
        content: `确定删除「${currentSelectedNode.full_name}」吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const result = await DeletedMember(params)
          if (result?.code === 0) {
            if (currentSelectedNode.father_id) {
              const newNode = await GetNodeById(currentSelectedNode.father_id)
              that.currentSelectedKey = newNode.id
              that.currentSelectedNode = newNode
            }
            that.$message.success('操作成功')
            // 保存当前展开状态
            const currentExpandedKeys = [...that.expandedKeys]
            // 使用被删除成员的generation作为展开中心来刷新
            const generation = currentSelectedNode.generation || 3
            try {
              await RefreshPedigreeMemberList(generation)
              // 恢复展开状态
              that.expandedKeys = currentExpandedKeys
            } catch (refreshError) {
              console.error('刷新数据失败:', refreshError)
              that.$message.warning('删除成功，但刷新数据失败，请手动刷新页面')
            }
          } else {
            that.$message.error(result.message || '删除失败')
          }
        }
      })
    },
    onContextMenuClick (treeKey, menuKey) {
      console.log(`treeKey: ${treeKey}, menuKey: ${menuKey}`)
    },
    handleTreeItemSelect (selectedKeys, generation) {
      this.currentSelectedKey = `${selectedKeys}`
      this.currentSelectedGeneration = generation
      console.log(JSON.parse(JSON.stringify(this.treeData)))
      AddNode({ generation })
    },
    handleSelectedNodeFromRightContent (key, node) {
      // 处理从右侧内容区域选择节点的事件
      // 这个方法主要用于配偶选择时的处理，不需要更新左侧树的选中状态
      console.log('从右侧选择节点:', node)
    },
    handleUpdateTreeSelection (node) {
      // 处理从DetailTabsHeader选择配偶时，同步更新世系树的选中状态
      if (node.husband_id) {
        // 选择的是配偶，在世系树中选中该配偶
        this.currentSelectedKey = node.id // 设置为配偶的ID，而不是主支成员的ID

        // 查找主支成员节点（用于设置其他相关信息）
        const mainMemberId = node.husband_id
        const mainMemberNode = findNodeById(this.treeData, mainMemberId)
        if (mainMemberNode) {
          this.currentSelectedNode = mainMemberNode // 保持主支成员作为选中节点
          this.currentSelectedGeneration = mainMemberNode.generation
          this.currentSelectedNodeFatherId = mainMemberNode.father_id || 0

          // 查找父节点
          if (mainMemberNode.father_id) {
            this.currentSelectedNodeFatherNode = findNodeById(this.treeData, mainMemberNode.father_id)
          }
        }
      } else {
        // 选择的是主支成员
        this.currentSelectedKey = node.id
        this.currentSelectedNode = node
        this.currentSelectedGeneration = node.generation
        this.currentSelectedNodeFatherId = node.father_id || 0

        // 查找父节点
        if (node.father_id) {
          this.currentSelectedNodeFatherNode = findNodeById(this.treeData, node.father_id)
        }
      }
    },
    handleWifeSelected (key, wifeNode) {
      // 更新当前选中的节点信息为配偶
      this.currentSelectedKey = key
      this.currentSelectedNode = wifeNode
      this.currentSelectedGeneration = wifeNode.generation
      // 设置父节点信息（配偶的父节点是丈夫）
      this.currentSelectedNodeFatherId = wifeNode.husband_id
      if (wifeNode.husband_id) {
        this.currentSelectedNodeFatherNode = findNodeById(this.treeData, wifeNode.husband_id)
      }
    },
    getMaxGeneration (treeData) {
      // 递归计算树中的最大世代
      let maxGeneration = 0

      const traverse = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return

        nodes.forEach(node => {
          if (node.generation && node.generation > maxGeneration) {
            maxGeneration = node.generation
          }
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        })
      }

      traverse(treeData)
      return maxGeneration
    },
    getMinGeneration (treeData) {
      // 递归计算树中的最小世代（始祖世代）
      let minGeneration = Infinity

      const traverse = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return

        nodes.forEach(node => {
          if (node.generation && node.generation < minGeneration) {
            minGeneration = node.generation
          }
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        })
      }

      traverse(treeData)
      return minGeneration === Infinity ? 1 : minGeneration
    },
    getDefaultExpandedKeys (treeData, maxGenerations = 3) {
      // 获取前N个世代的所有节点ID，用于默认展开
      const expandedKeys = []
      const minGeneration = this.getMinGeneration(treeData)
      const maxExpandGeneration = minGeneration + maxGenerations - 1

      const traverse = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return

        nodes.forEach(node => {
          // 如果节点的世代在展开范围内，添加到expandedKeys
          if (node.generation && node.generation <= maxExpandGeneration) {
            expandedKeys.push(node.id)
          }

          // 继续遍历子节点
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        })
      }

      traverse(treeData)
      return expandedKeys
    },

    handleUpdateTreeNodeData (updateInfo) {
      // 处理世系树节点数据更新
      const { nodeId, updatedData } = updateInfo

      // 在世系树数据中找到对应节点并更新
      const updateNodeInTree = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return false

        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i]
          if (node.id === nodeId) {
            // 找到目标节点，更新数据
            Object.keys(updatedData).forEach(key => {
              if (key !== 'clan_id' && key !== 'pedigree_id' && key !== 'id') {
                // 映射字段名
                let nodeKey = key
                if (key === 'rankingText') {
                  nodeKey = 'ranking_text'
                }
                node[nodeKey] = updatedData[key]
              }
            })

            // 特殊处理：如果更新了姓名相关字段，需要更新显示名称
            if (updatedData.first_name || updatedData.name) {
              const firstName = updatedData.first_name || node.first_name || ''
              const name = updatedData.name || node.name || ''
              node.full_name = firstName + name
            }

            return true
          }

          // 递归查找子节点
          if (node.children && updateNodeInTree(node.children)) {
            return true
          }
        }
        return false
      }

      // 更新树数据
      if (updateNodeInTree(this.treeData)) {
        console.log('世系树节点数据更新成功:', nodeId, updatedData)
        // 触发视图更新
        this.$forceUpdate()
      } else {
        console.warn('未找到要更新的节点:', nodeId)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.pu-xi-container {
  background-color: white;
  height: 99vh;

  .cus-tree {
    margin-left: 30px;
  }

  /deep/ .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: #fff;
    cursor: default;
  }

  /deep/ .ant-tree li .ant-tree-node-content-wrapper:hover {
    cursor: default;
    background-color: #fff;
  }

  /deep/ .anticon {
    font-size: 24px;
  }

  /deep/ .ant-tree-child-tree {
    margin-left: 20px;
  }

  /deep/ .text {
    background-color: #f2f2f2;
    padding: 0 15px;
    margin-right: 4px;
    line-height: 26px;
    cursor: pointer;
    white-space: nowrap;
    border-radius: 2px;
    min-width: 30px;
    height: 26px;
  }

  /deep/ .text.selected {
    background-color: #f86e04;
    color: #fff;
  }
}

.tree-container {
  flex: 1;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.unified-scroll-container {
  flex: 1;
  overflow: auto;
  min-height: 0;
  position: relative;
}

.generation-bar-fixed {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #f9f5f1;
}

.tree-content {
  width: fit-content;
  min-width: max-content;
  padding-right: 57px;
  padding-bottom: 20px;
}
</style>
