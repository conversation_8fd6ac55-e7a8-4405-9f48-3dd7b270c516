<template>
  <div class="wrap">
    <div class="item">
      <div class="img">
        <div class="inner">
          <img :src="inner2_1" />
          <div class="info">
            <div class="title" style="font-size: 70px;">
              <div v-for="str in data?.inner_title" :key="str">{{ str }}</div>
            </div>
            <div class="sub-title">
              <div v-for="str in data?.inner_sub_title" :key="str">{{ str }}</div>
            </div>
            <div class="volume">
              <div class="volume-item">
                <div>
                  <div v-for="str in data?.inner_edit_date" :key="str">{{ str }}</div>
                </div>
              </div>
              <div class="volume-item">
                <div>
                  <div v-for="str in data?.inner_hall" :key="str">{{ str }}</div>
                </div>
                <div class="space"></div>
                <div>
                  <div v-for="str in data?.inner_volume" :key="str">{{ str }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="name">扉页 正面</div>
    </div>
    <div class="item">
      <div class="img">
        <div class="inner">
          <img :src="inner2_2" />
          <div class="info">
            <div class="editor">
              <div class="name">{{ data?.plan_name }}</div>
              <div>主编：{{ data?.editor }}</div>
              <div>制作单位：莲山谱牒文化研究中心</div>
              <div>电话：192xxxxxxxx，180xxxxxxxx</div>
              <div>网址：www.xxxx.com</div>
              <div>地址：xxxx</div>
            </div>
          </div>
        </div>
      </div>
      <div class="name">扉页 背面</div>
    </div>
  </div>
</template>
<script>
import inner2_1 from '@/assets/prints/inner2-1.png'
import inner2_2 from '@/assets/prints/inner2-2.png'
export default {
  name: 'Inner2Preview',
  data () {
    return {
      inner2_1,
      inner2_2
    }
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  }
}
</script>
<style lang="less" scoped>
.wrap{
  display:flex;
  flex-wrap:wrap;
  .item {
    width: 380px;
    height: 600px;
    margin-left: 20px;
    .img {
      height: 540px;
      border: 2px solid #ccc;
      .inner {
        width: 760px;
        height: 1080px;
        position: relative;
        transform: scale(.5);
        transform-origin: 0 0;
        img {
          border: none;
          max-width: 99%;
        }
        .info {
          .title{
            position: absolute;
            left: 345px;
            top: 0;
            width: 70px;
            height: 1080px;
            text-align: center;
            font-size: 70px;
            line-height: 1.4;
            display: flex;
            flex-direction: column;
            justify-content: center;
          }
          .sub-title {
            position: absolute;
            right: 190px;
            top: 190px;
            width: 20px;
            height: 743px;
            text-align: center;
            font-size: 20px;
            line-height: 1.3;
          }
          .volume {
            position: absolute;
            left: 190px;
            bottom: 190px;
            font-size: 20px;
            line-height: 1.3;
            display: flex;
            .volume-item {
              width: 36px;
            }
          }
          .space {
            height: 20px;
          }
          .editor {
            position: absolute;
            bottom: 60px;
            left: 60px;
            font-size: 20px;
            line-height: 1.4;
            letter-spacing: 1px;
            .name  {
              font-size: 30px;
              font-weight: 700;
              margin-bottom: 10px;
            }
          }
        }
      }
    }
    .name {
      font-size: 20px;
    }
  }
}
</style>
