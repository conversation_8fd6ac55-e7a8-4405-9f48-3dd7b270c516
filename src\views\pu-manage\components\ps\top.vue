<template>
  <div class="px-header">
    <div class="inner">
      <div class="demo" @click="()=>showFamilyBaseInfo=true">
        <span class="tag tag-fanli">管理</span>
        <span class="name line-word">{{ clan?.name }}</span>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarchaxun"></i>
      </div>
      <a-divider type="vertical" style="height: 26px; margin: 0 32px; color: #f86e04;"/>
      <span class="f16" style="font-weight:bold">{{ currentSelectedBook?.plan_name }}</span>
      <i class="cus-edit-toolbar-icon icon-edit-toolbaryoujiantou2 ml14 mr14" style="color:#c0c4cc" />
      <span v-if="step === 1"> 封面、封底、扉页选择 </span>
      <span v-if="step === 2">  编辑内容 </span>
      <span v-if="step === 3">  设置版式 </span>
      <div class="flex-grow-1  flex flex-direction-row justify-content-end">
      </div>
    </div>
    <family-info-component v-if="showFamilyBaseInfo" @close="showFamilyBaseInfo=false"/>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import FamilyInfoComponent from '@/views/components/family-info'

export default {
  name: 'PsHeader',
  components: {
    FamilyInfoComponent
  },
  data () {
    return {
      showPreview: false,
      showFamilyBaseInfo: false,
      clan: {}
    }
  },
  props: {
    selectedNode: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    ...mapState({
      step: state => state.ps.step,
      currentSelectedBook: state => state.ps.currentSelectedBook,
      currentSelectedGroupId: state => state.family.currentSelectedGroupId
    })
  },
  async mounted () {
    if (!this.currentSelectedGroupId) {
      return
    }
    const result = await this.GetCurrentClan()
    this.clan = result
  },
  methods: {
    ...mapActions(['GetCurrentClan']),
    handleToggle (type) {
      this.$message.info('该功能暂未实现')
      if (type === 'open') {
        this.$emit('open')
      } else {
        this.$emit('close')
      }
    },
    handleSearch () {
      this.$message.info('该功能暂未实现')
    },
    handleMoreFunc () {
      this.$message.info('该功能暂未实现')
    },
    handleViewSetting () {
      this.$message.info('该功能暂未实现')
    },
    handleSave () {
      this.$message.info('该功能暂未实现')
    },
    handleMobilePreview () {
      this.$message.info('该功能暂未实现')
    }
  }
}
</script>
<style scoped lang='less'>
.px-header{
  position:relative;
  height: 60px;
  width: 100%;
  z-index:1;

  .inner{
    height: 60px;
    left: 80px;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    background-color: #fff;

    .demo {
      width: 263px;
      height: 52px;
      background:  #f0f2f5 url(~@/assets/nav_level_3.png) no-repeat 50%;
      background-size: cover;
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 15px;
      padding-right: 10px;
      box-sizing: border-box;
      margin-left: 5px;
      border-radius: 2px;
      overflow: hidden;
      cursor: pointer;
      z-index: 1;
      .tag{
        position: absolute;
        left: 0;
        top: 0;
        width: 80px;
        height: 16px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        z-index: 10;
        transform: rotate(-45deg);
        transform-origin: 42% 267%;
        font-size: 12px
      }
      .tag-fanli {
        background: #b91212;
      }
      .line-word {
        font-size:24px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .left {
    flex-shrink: 0;

    .line{
      display: inline-block;
      height: 22px;
      border-left: 1px solid #ccc;
      margin-left: 30px
    }
    .search {
      position: relative;
      z-index: 0;
      width: 280px;
      height: 36px
    }
  }
}
.clan-preview-drawer {
  /deep/ .ant-drawer-wrapper-body {
    overflow:hidden;
    .ant-drawer-body {
      padding: 0;
    }
  }
}
</style>
