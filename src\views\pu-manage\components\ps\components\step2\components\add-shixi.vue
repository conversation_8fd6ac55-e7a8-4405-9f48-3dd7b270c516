<template>
  <a-modal
    class="add-shixi-modal-wrap"
    v-model="visible"
    title="添加世系"
    width="700px"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <div class="content">
      <div class="left">
        <div class="pic">
          <img src="../../../../../../../assets/prints/pushu_edit_add_img.png" />
          <div class="zhimu-txt" :class="form.getFieldValue('title') ?'':'tips'">{{ form.getFieldValue('title') ? form.getFieldValue('title') : '这里是支目标题' }}</div>
          <div class="pumei-txt" :class="form.getFieldValue('sub_title') ?'':'tips'">{{ form.getFieldValue('sub_title') ? form.getFieldValue('sub_title') : '这里是谱眉标题' }}</div>
        </div>
      </div>
      <div class="right">
        <a-form :form="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" @submit="handleSubmit">
          <a-form-item label="支目标题" class="form-wrap">
            <a-input
              placeholder="请输入支目标题"
              :maxLength="100"
              v-decorator="['title', { rules: [{ required: false, message: '请输入支目标题' }], normalize: (value) => value && value.trim()}]"
            />
          </a-form-item>
          <a-form-item label="谱眉标题" class="form-wrap">
            <a-input
              placeholder="请输入谱眉标题"
              :maxLength="100"
              v-decorator="['sub_title', { rules: [{ required: false, message: '请输入谱眉标题' }], normalize: (value) => value && value.trim()}]"
            />
          </a-form-item>
          <a-form-item label="支派后裔" class="form-wrap">
            <a-select
              placeholder="请选择支派后裔"
              v-decorator="['clan_id', {rules: [{ required: false, message: '请选择支派后裔' }]}]"
            >
              <a-select-option :value="1">全部后代</a-select-option>
              <a-select-option :value="2">指定结束任务</a-select-option>
              <a-select-option :value="3">指定结束世系</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="起始人物" class="form-wrap">
            <a-input
              suffix=">"
              readOnly
              placeholder="请选择世系节点"
              @click="showAddClanNode=true"
              v-decorator="['clan_node', {rules: [{ required: false, message: '请选择世系节点' }]}]"
            />
          </a-form-item>
          <a-form-item :wrapper-col="{ span: 24, offset: 0 }" class="form-btn">
            <a-button type="default" class="mr10" @click="handleFenFang">按分房添加</a-button>
            <a-button type="primary" :loading="confirmLoading" html-type="submit">保存</a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
    <step3-add-clan-node v-if="showAddClanNode" @close="showAddClanNode=false" @confirm="handleSelectedClanNode" />
  </a-modal>
</template>
<script>
import Step3AddClanNode from '@/views/pu-manage/components/ps/components/step2/components/add-clan'
export default {
  name: 'Step3AddShiXi',
  components: { Step3AddClanNode },
  data () {
    return {
      visible: true,
      confirmLoading: false,
      form: this.$form.createForm(this),
      showAddClanNode: false
    }
  },
  methods: {
    handleOk () {
      this.$emit('close')
    },
    handleFenFang () {
      this.$emit('fenFang')
      this.$nextTick(() => this.$emit('close'))
    },
    handleSelectedClanNode (node) {
      console.log('selected node', node)
    },
    handleSubmit (e) {
      e.preventDefault()
      this.form.validateFields(async (err, values) => {
        if (!err) {
          this.$message.info('保存功能暂未实现')
          this.$emit('close')
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
.add-shixi-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
  .content {
    display:flex;
    .left {
      .pic {
        position: relative;
        flex-shrink: 0;
        width: 225px;
        height: 308px;
        font-size: 16px;
        color: #f86e04;
        writing-mode: vertical-lr;
        letter-spacing: 2px;
        .tips { color: #ccc;}
        .zhimu-txt {
          position: absolute;
          right: 42px;
          top: 12px;
        }
        .pumei-txt {
          position: absolute;
          right: 7px;
          top: 92px;
        }
      }
    }
    .right{
      flex: 1;
      .form-btn {
        /deep/ .ant-form-item-control-wrapper{
          text-align:center;
        }
      }
    }
  }
}
</style>
