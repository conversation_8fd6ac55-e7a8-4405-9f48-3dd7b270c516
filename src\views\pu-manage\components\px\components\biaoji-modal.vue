<template>
  <a-modal
    title="设置标记"
    :visible="visible"
    :width="500"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="保存"
    cancelText="取消"
  >
    <a-form
      ref="form"
      :form="form"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="标记类型" prop="remark_type">
        <a-select v-decorator="['remark_type', { rules: [{ required: false }] }]">
          <a-select-option value="默认">默认</a-select-option>
          <a-select-option value="待完善">待完善</a-select-option>
          <a-select-option value="待确认">待确认</a-select-option>
          <a-select-option value="待纠正">待纠正</a-select-option>
          <a-select-option value="重要">重要</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="设置对象" prop="name">
        <a-input disabled placeholder="" v-decorator="['name', { rules: [{ required: false }] }]" />
      </a-form-item>
      <a-form-item label="备注" prop="remark">
        <a-textarea placeholder="请输入" :maxLength="30" v-decorator="['remark']" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
export default {
  name: 'BiaoJiModal',
  data () {
    return {
      visible: true,
      confirmLoading: false,
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      item: {},
      tagType: {},
      currentData: {},
      selectedType: 0
    }
  },
  props: {
    selectedNode: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    selectedNode: {
      handler (val) {
        console.log(val)
        this.form.getFieldDecorator('remark_type', { initialValue: '默认' })
        this.form.getFieldDecorator('name', { initialValue: val.name })
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleOk () {
      this.form.validateFields(async (err, values) => {
        if (!err) {
          this.$message.info('该功能暂未实现')
          this.$emit('close')
        }
      })
    },
    handleCancel () {
    this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
</style>
