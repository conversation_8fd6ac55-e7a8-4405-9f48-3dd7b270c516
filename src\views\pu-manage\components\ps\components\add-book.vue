<template>
  <a-modal
    class="add-book-modal-wrap"
    v-model="visible"
    :title="id ? '编辑谱书':'创建谱书'"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <a-form :form="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }" @submit="handleSubmit">
      <a-form-item label="谱书名称">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['plan_name', { rules: [{ required: true, message: '请输入谱书名称!' }] }]" />
      </a-form-item>
      <a-form-item label="卷号">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['volume', { rules: [{ required: false, message: '请输入卷号!' }] }]" />
      </a-form-item>
      <a-form-item label="页眉名称">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['header', { rules: [{ required: true, message: '请输入页眉名称!' }] }]" />
      </a-form-item>
      <a-form-item label="郡望堂号">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['hall', { rules: [{ required: false, message: '请输入郡望堂号!' }] }]" />
      </a-form-item>
      <a-form-item label="编者">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['editor', { rules: [{ required: false, message: '请输入编者!' }] }]" />
      </a-form-item>
      <a-form-item label="编修日期">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['edit_date', { rules: [{ required: false, message: '请输入编修日期!' }] }]" />
      </a-form-item>
      <a-form-item label="起始页码">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['first_page', { rules: [{ required: false, message: '请输入起始页码!' }] }]" />
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" html-type="submit">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
export default {
  name: 'FamilyEditComponent',
  data () {
    return {
      submitting: false,
      visible: true,
      initValue: [],
      formLayout: 'horizontal',
      form: this.$form.createForm(this, { name: 'xxx' })
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId
    })
  },
  props: {
    id: {
      type: Number,
      default: 0
    },
    item: {
      type: Object,
      default: () => {}
    }
  },
  mounted () {
    this.getData()
  },
  methods: {
    ...mapActions(['SaveClanBook']),
    getData () {
      if (this.item) {
        const { plan_name, volume, header, hall, editor, edit_date, first_page } = this.item
        this.form.getFieldDecorator('plan_name', { initialValue: plan_name })
        this.form.getFieldDecorator('volume', { initialValue: volume })
        this.form.getFieldDecorator('header', { initialValue: header })
        this.form.getFieldDecorator('hall', { initialValue: hall })
        this.form.getFieldDecorator('editor', { initialValue: editor })
        this.form.getFieldDecorator('edit_date', { initialValue: edit_date })
        this.form.getFieldDecorator('first_page', { initialValue: first_page })
      }
    },
    handleSubmit (e) {
      e.preventDefault()
      const { SaveClanBook, currentSelectedClanId, item } = this
      this.form.validateFields(async (err, values) => {
        if (!err) {
          const params = { ...values }
          if (item) {
            params.id = item.id
            params.clan_id = item.clan_id
          } else {
            params.clan_id = currentSelectedClanId
          }
          this.submitting = true
          await SaveClanBook(params)
          this.submitting = false
          this.$emit('refresh')
          this.$emit('close')
        }
      })
    },
    handleOk (e) {
      this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
.add-book-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
}
</style>
