<template>
  <div class="cus-edit-tool-bar">
    <div class="tool-bar">
      <div class="section flex-row first-section">
        <div class="column icon-group">
          <div title="撤销" class="tool-item c-pointer-hover" @click="handleClick()">
            <div class="item">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarchexiao_o"></span>
              <span>撤销</span>
            </div>
          </div>
          <div title="重做" class="tool-item c-pointer-hover" @click="handleClick()">
            <div class="item">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarzhongzuo"></span>
              <span>重做</span>
            </div>
          </div>
        </div>
        <div class="column icon-group c-pointer-hover" @click="handleSave">
          <div title="保存" class="tool-item">
            <div class="item">
              <span class="f28 cus-edit-toolbar-icon icon-edit-toolbarbaocun"></span>
            </div>
          </div>
          <div class="text"><span class="txt">保存</span></div>
        </div>
        <div class="column icon-group c-pointer-hover">
          <div title="删除" class="tool-item">
            <div class="item">
              <span
                class="f28 cus-edit-toolbar-icon icon-edit-toolbarshanchu"
                @click="handleClick(action.TOOL_BAR_ACTION_DELETE)"></span>
            </div>
          </div>
          <div class="text"><span class="txt">删除</span></div>
        </div>
      </div>
      <div class="section">
        <div class="row">
          <div class="combox">
            <div class="item">
              <div id="components-dropdown-demo-placement">
                <a-dropdown placement="bottomCenter" @change="handleClick()">
                  <div>宋体</div>
                  <a-menu slot="overlay">
                    <a-menu-item>
                      宋体
                    </a-menu-item>
                    <a-menu-item>
                      宋体2
                    </a-menu-item>
                    <a-menu-item>
                      宋体3
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </div>
            </div>
            <div class="item">
              12px
            </div>
          </div>
        </div>
        <div class="row">
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarjiacu"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarxieti"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarxiahuaxian"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarshanchuxian"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarFont-color"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarjianxiaozihao"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarzengjiazihao"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="row">
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarduiqi-zuoduiqi"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarwenzifangxiang"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarzijianju"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarhanggao"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon  icon-edit-toolbarshouhangsuojin"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon  icon-edit-toolbara-zi1"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarcharuwenben"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon  icon-edit-toolbarfanti"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarjianti"></span>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarzuoduiqi"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarjuzhongduiqi1"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbaryouduiqi1"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarquanbuliangduanduiqi"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarfensanduiqi"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarshangduiqi"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarjuzhongduiqimiddle"></span>
            </div>
          </div>
          <div class="el-tooltip tool-item c-pointer-hover" @click="handleClick()">
            <div class="item disabled">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarxiaduiqi"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="section flex-row">
        <div class="column icon-group">
          <div title="保存" class="tool-item c-pointer-hover">
            <div class="item">
              <span class="f28 cus-edit-toolbar-icon icon-edit-toolbarfuwenbenkuang"></span>
            </div>
          </div>
          <div class="text">

            <a-dropdown placement="bottomCenter">
              <div>文本框<a-icon type="caret-down" /></div>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a
                    href="javascript:;"
                    class=" c-pointer-hover"
                    @click="handleClick(action.TOOL_BAR_ACTION_HORIZONTAL_INPUT)">水平文本框</a>
                </a-menu-item>
                <a-menu-item>
                  <a
                    href="javascript:;"
                    class=" c-pointer-hover"
                    @click="handleClick(action.TOOL_BAR_ACTION_VERTICAL_INPUT)">竖直文本框</a>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </div>
        </div>
        <div class="column icon-group">
          <div title="图片" class="tool-item c-pointer-hover">
            <div class="item">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarchexiao_o"></span>
              <span @click="handleClick('add-img', 'http://localhost:8000/img/photo.05cc0248.png')">图片</span>
            </div>
          </div>
          <div title="层级" class="tool-item c-pointer-hover" @click="handleClick()">
            <div class="item">
              <span class="cus-edit-toolbar-icon icon-edit-toolbarzhongzuo"></span>
              <span>层级</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import cloneDeep from 'lodash.clonedeep'
import { v4 as uuidv4 } from 'uuid'
import * as action from '@/components/Editor/components/toolbarAction'
import * as initDefaultData from '@/components/Editor/components/initDefaultData'
import * as types from '@/components/Editor/components/types'
import { EventBus } from '@/utils/eventBus'
import { ToolbarEvents } from '@/constant/event-types'

export default {
  name: 'ToolBar',
  computed: {
    ...mapState({
      activeEditItemId: state => state.pw.activeEditItemId
    })
  },
  data () {
    return {
      action
    }
  },
  methods: {
    ...mapActions(['UpdateActiveEditItemId', 'DeleteTextBox', 'UpdateActionType', 'InsertTextBox']),
    handleClick (type, payload) {
      const { DeleteTextBox, UpdateActionType, InsertTextBox } = this
      switch (type) {
        case action.TOOL_BAR_ACTION_HORIZONTAL_INPUT: {
          const data = cloneDeep(initDefaultData.TextBoxData)
          data.items[0].id = uuidv4()
          InsertTextBox(data.items[0])
          // this.$store.dispatch('pw/InsertTextBox', data)
          break
        }
        case action.TOOL_BAR_ACTION_VERTICAL_INPUT: {
          const verticalData = cloneDeep(initDefaultData.TextBoxData)
          verticalData.items[0].id = uuidv4()
          verticalData.items[0].type = types.TextBoxVertical
          InsertTextBox(verticalData.items[0])
          // this.$store.dispatch('pw/InsertTextBox', verticalData)
          break
        }
        case action.TOOL_BAR_ACTION_DELETE:
          DeleteTextBox()
          // this.$store.dispatch('pw/DeleteTextBox')
          break
        default:
          this.$message.info('该功能正在完善中')
          UpdateActionType(type, payload)
          // this.$store.dispatch('pw/UpdateActionType', type, payload)
          break
      }
    },
    handleSave () {
      EventBus.$emit(ToolbarEvents.SAVE)
    }
  }
}
</script>
<style lang='less' scoped>
.flex-row {
  flex-direction: row !important
}

.first-section {
  position: sticky !important;
  left: 0;
  top: var(--top-tab-height);
  background-color: #fff;
  z-index: 10
}

.cus-edit-tool-bar {
  flex-shrink: 0;
  position: relative;
  height: 80px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
  overflow-y: hidden;
  background-color: #fff
}

.tool-bar {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;

  .section {
    flex-shrink: 0;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding: 10px 0;
    margin: 0 15px;
    box-sizing: border-box;

    &:after {
      content: "";
      position: absolute;
      background: #eaeef1;
      top: 50%;
      transform: translateY(-50%);
      left: -15px;
      height: 80%;
      width: 1px
    }

    .row {
      display: flex
    }

    combox {
      display: flex;
      border: 1px solid #cacaca;
      width: 100%;

      .item {
        flex: 1;
        border-right: 1px solid #cacaca
      }

      .item:last-child {
        border-right: none
      }
    }

    .column {
      flex-direction: column
    }

    .icon-group {
      display: flex;
      justify-content: space-between;

      .item {
        cursor: pointer;
        margin-right: 5px;
        display: flex;
        align-items: center
      }

      .item.disabled {
        opacity: .3;
        pointer-events: none
      }

      .item.large {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center
      }

      .active {
        background-color: rgba(0, 0, 0, .08)
      }
    }

    .icon-group.active {
      background-color: rgba(0, 0, 0, .08)
    }

    .combox {
      display: flex;
      border: 1px solid #cacaca;
      width: 100%;

      .item {
        flex: 1;
        border-right: 1px solid #cacaca
      }

      .item:last-child {
        border-right: none
      }
    }
  }
}

.top-box {
  flex-shrink: 0;
  height: 80px;
  padding-left: 30px;
  background-color: #fff;
  box-shadow: 0 8px 6px 0 rgba(0, 0, 0, .04);

  .row {
    padding: 15px 0 5px;
    font-size: 16px
  }

  .tabs {
    padding-right: 30px;
    color: #666;
    display: flex;
    justify-content: space-between;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
  }
}
</style>
