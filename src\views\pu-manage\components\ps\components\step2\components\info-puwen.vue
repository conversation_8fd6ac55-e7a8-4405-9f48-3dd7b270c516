<template>
  <div class="info-puwen flex">
    <div class="content-1 flex">
      <div class="mt8">
        <i class="cus-edit-toolbar-icon icon-edit-toolbaryuandian"/>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarwenzhang" />
      </div>
      <div class="flex-1 mt8 content">
        <div class="item">
          <span>谱文标题：第二个谱文标题1</span>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-pointer-hover"/>
        </div>
        <div class="item">
          <span>引用谱文：第二个谱文标题1</span>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarlianjie c-pointer-hover"/>
        </div>
      </div>
      <div class="remove mt8">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'InfoPuWen'
}
</script>
<style lang='less' scoped>
.info-puwen {
  padding-left: 30px;
  .content-1 {
    position:relative;
    width: 100%;
    border: 1px dashed #fff;
    &:hover {
      background: linear-gradient(135deg, #fff7f0, #fff);
      border-color: red;
      .remove {
        .cus-edit-toolbar-icon {
          visibility:visible;
        }
      }
    }
    &:before {
      content: '';
      position:absolute;
      height: calc(100% + 3px);
      top: 0;
      left: -8px;
      border-left: 1px solid #ccc;
    }
    &:after {
      content: "";
      position: absolute;
      height: 0;
      width: 8px;
      left: -8px;
      top: 20px;
      border-top: 1px solid #ccc;
    }
    .content {
      .item {
        height:40px;
      }
    }
    .remove {
      width: 50px;
      text-align:center;
      .cus-edit-toolbar-icon {
        visibility: hidden;
      }
    }
  }
}
</style>
