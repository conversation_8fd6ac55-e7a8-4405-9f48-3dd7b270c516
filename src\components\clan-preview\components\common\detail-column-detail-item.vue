<template>
  <div :type="getValue('type')">
    <div :class="'item type'+getValue('type')" :style="{ left: getValue('x') + 'px' }">
      <div class="text">{{ getValue('text') }}</div>
      <div class="fathers"></div>
    </div>
  </div>
</template>
<script>
import { get } from 'lodash'
export default {
  name: 'DetailColumnDetailItem',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    getValue (field) {
      return get(this.item, field, '')
    }
  }
}
</script>
<style lang="less" scoped>
.item {
  position: absolute;
  top: 10px;
  writing-mode: vertical-rl;
  font-size: 0;
  white-space: nowrap;
  height: 100%;
  display: flex;
  letter-spacing: 1px;

  .text{
    display: inline-block;
    font-size: 16px;
    height: auto;
  }
  .fathers {
    position: absolute;
    bottom: 100%;
    left: -8px;
    padding-bottom: 20px;
    font-size: 10px;
    writing-mode: horizontal-tb;
  }
}
.type2 {
  white-space: normal;
  height: 100%;
  display: flex;
  justify-content: center;
  top: 0;
  .text {
    letter-spacing: 0;
    max-height: 6em;
    padding: 3px;
    border: 1px solid #333;
    font-size: 12px;
    line-height: 14px
  }
}
.type3 {
  width: 18px;
  height: 100%;
  display: flex;
  align-items: center;
  .text {
    font-size: 18px;
    width: 18px;
    font-weight: 700
  }
  .type4 {
    width: 22px;
    line-height: 22px
  }
  .type5 {
    font-weight: 700;
    width: 22px;
    line-height: 22px
  }
  .type6 {
    height: 100%;
    width: 22px;
    line-height: 22px
  }
}
.type4 {
  width: 22px;
  line-height: 22px;
}
.type5 {
  font-weight: 700;
  width: 22px;
  line-height: 22px;
}
.type6 {
  height: 100%;
  width: 22px;
  line-height: 22px;
}
</style>
