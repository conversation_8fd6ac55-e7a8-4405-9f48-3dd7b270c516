<template>
  <div class="title">
    <div class="left">
      <span>{{ title }}</span>
      <i title="编辑" v-if="isEdit" class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-999 c-pointer-hover" style="font-size:16px;"/>
    </div>
  </div>
</template>
<script>
export default {
  name: 'BaseHeadTitle',
  data () {
    return {
    }
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  methods: {
  }
}
</script>
<style lang="less" scoped>
@import '~@/assets/theme.less';
.title{
  padding: 30px 0;
  display: flex;
  align-items: center;
  font-size: 16px;
  .left {
    position: relative;
    padding-left: 14px;
    font-size: 20px;
    color: #33271f;
    display: inline-flex;
    align-items: center;
    .txt {
    }
  }
  .left:before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 22px;
    background-color: #f76d02;
  }
  .right {
    display: inline-flex;
    align-items: center;
    margin-left: 50px;
    .item {
      margin-right: 30px;
      cursor: pointer;
      .txt{
        color: #666;
      }
      .txt:hover{
        color: @primary-color;
      }
    }
  }
}
</style>
