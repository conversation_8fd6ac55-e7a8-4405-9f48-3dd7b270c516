import request from '@/utils/request'

const dynamicPre = ':clanId'
const pwApi = {
  addCategory: '/web/family/:clanId/addCategory',
  deletedCategory: '/web/family/:clanId/deletedCategory',
  updateCategory: '/web/family/:clanId/updateCategory',
  addClanArticle: '/web/family/:clanId/addClanArticle',
  updateClanArticle: '/web/family/:clanId/updateClanArticle',
  getCategoryList: '/web/family/:clanId/getCategoryList',
  getClanArticleList: '/web/family/:clanId/getClanArticleList',
  getClanPrintConfigList: '/web/family/getClanPrintConfigList'
 }

/**
 * 新增谱文类目
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {string} parameter.name - 类目名称
 * @param {number} [parameter.type] - 添加类型，默认0
 * @returns {Promise<object>}
 */
 export async function addCategory (parameter) {
   return request({
     url: pwApi.addCategory.replace(dynamicPre, parameter.clan_id),
     method: 'post',
     data: parameter
   })
 }

/**
 * 删除谱文类目
 * @param {object} parameter - 请求体
 * @param {number} parameter.category_id - 需要删除的类目Id
 * @returns {Promise<object>}
 */
 export async function deletedCategory (parameter) {
   // 注意：此处URL替换用的是category_id，可能与接口定义不符，遵循原代码
   return request({
     url: pwApi.deletedCategory.replace(dynamicPre, parameter.category_id),
     method: 'post',
     data: parameter
   })
 }

/**
 * 修改谱文类目名称
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.category_id - 需要修改的类目ID
 * @param {string} parameter.name - 新的类目名称
 * @returns {Promise<object>}
 */
 export async function updateCategory (parameter) {
   return request({
     url: pwApi.updateCategory.replace(dynamicPre, parameter.clan_id),
     method: 'post',
     data: parameter
   })
 }

/**
 * 新增谱文
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {string} parameter.name - 谱文标题
 * @param {number} parameter.category_id - 分类ID
 * @param {string} parameter.category_name - 类目名称
 * @param {number} parameter.print_id - 打印模式id
 * @param {'横向左开'|'竖向右开'|'竖向左开'} parameter.layout - 谱文布局
 * @returns {Promise<object>}
 */
 export async function addClanArticle (parameter) {
   return request({
     url: pwApi.addClanArticle.replace(dynamicPre, parameter.clan_id),
     method: 'post',
     data: parameter
   })
 }

/**
 * 修改谱文
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.id - 谱文id
 * @param {string} [parameter.name] - 需要修改的谱文标题
 * @param {string} [parameter.pages] - 页面内容，json序列化格式
 * @returns {Promise<object>}
 */
 export async function updateClanArticle (parameter) {
   return request({
     url: pwApi.updateClanArticle.replace(dynamicPre, parameter.clan_id),
     method: 'post',
     data: parameter
   })
 }

/**
 * 获取自己的谱文类目列表
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @returns {Promise<object>}
 */
 export async function getCategoryList (parameter) {
   return request({
     url: pwApi.getCategoryList.replace(dynamicPre, parameter.clan_id),
     method: 'get',
     data: parameter
   })
 }

/**
 * 获取谱文列表（以类目分组形式）
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @returns {Promise<object>}
 */
 export async function getClanArticleList (parameter) {
   return request({
     url: pwApi.getClanArticleList.replace(dynamicPre, parameter.clan_id),
     method: 'get',
     data: parameter
   })
 }

/**
 * 获取谱文打印配置
 * @param {object} [parameter] - 请求参数 (此接口似乎不需要参数)
 * @returns {Promise<object>}
 */
 export async function getClanPrintConfigList (parameter) {
   return request({
     url: pwApi.getClanPrintConfigList,
     method: 'get',
     data: parameter
   })
 }
