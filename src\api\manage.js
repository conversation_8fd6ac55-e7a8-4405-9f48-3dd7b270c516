import request from '@/utils/request'

const api = {
  user: '/user',
  role: '/role',
  service: '/service',
  permission: '/permission',
  permissionNoPager: '/permission/no-pager',
  orgTree: '/org/tree'
}

export default api

/**
 * 获取用户列表
 * @param {object} [parameter] - 查询参数，例如分页、筛选
 * @returns {Promise<object>}
 */
export function getUserList (parameter) {
  return request({
    url: api.user,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取角色列表
 * @param {object} [parameter] - 查询参数
 * @returns {Promise<object>}
 */
export function getRoleList (parameter) {
  return request({
    url: api.role,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取服务列表
 * @param {object} [parameter] - 查询参数
 * @returns {Promise<object>}
 */
export function getServiceList (parameter) {
  return request({
    url: api.service,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取权限列表
 * @param {object} [parameter] - 查询参数
 * @returns {Promise<object>}
 */
export function getPermissions (parameter) {
  return request({
    url: api.permissionNoPager,
    method: 'get',
    params: parameter
  })
}

/**
 * 获取组织结构树
 * @param {object} [parameter] - 查询参数
 * @returns {Promise<object>}
 */
export function getOrgTree (parameter) {
  return request({
    url: api.orgTree,
    method: 'get',
    params: parameter
  })
}

/**
 * 保存服务 (新增或更新)
 * @param {object} parameter - 服务实体
 * @param {number} parameter.id - ID，为0时新增，非0时更新
 * @returns {Promise<object>}
 */
export function saveService (parameter) {
  return request({
    url: api.service,
    method: parameter.id === 0 ? 'post' : 'put',
    data: parameter
  })
}

/**
 * 保存子项 (新增或更新)
 * @param {object} sub - 子项实体
 * @param {number} sub.id - ID，为0时新增，非0时更新
 * @returns {Promise<object>}
 */
export function saveSub (sub) {
  return request({
    url: '/sub',
    method: sub.id === 0 ? 'post' : 'put',
    data: sub
  })
}
