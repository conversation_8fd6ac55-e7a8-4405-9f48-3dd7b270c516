<template>
  <div data-v-c040dcfa="" class="container">
    <div data-v-c040dcfa="" class="wrapper">
      <div data-v-1ce89d73="" data-v-c040dcfa="" class="pw-container">
        <div data-v-1ce89d73="" class="pw-body">
          <tool-bar></tool-bar>
          <div data-v-1ce89d73="" class="pw-content">
            <div data-v-1ce89d73="" class="content-left">
              <div data-v-1ce89d73="" class="pw-center">
                <div data-v-1ce89d73="" class="pw-main">
                  <div
                    data-v-1ce89d73=""
                    class="page"
                    style="transform: scale(1); height: 1036px; width: 712px; padding: 114px 57px 94px;">
                    <div data-v-1ce89d73="" class="pag-main">
                      <div data-v-1ce89d73="" class="page-head" style="width: 56px;">
                        <div data-v-1ce89d73="" class="title">
                          <div data-v-1ce89d73="" class="clan-title txt"> 江都堂董氏江夏支谱 </div><img
                            data-v-1ce89d73=""
                            src="../../assets/header-mark.c8ec6717.svg">
                          <div data-v-1ce89d73="" class="tips txt">
                            <div data-v-1ce89d73="" class="txt-in" style="transform: translateX(-50%) scale(1);"> test
                            </div>
                          </div>
                          <div data-v-1ce89d73="" class="page-no txt"> 〇一 </div>
                        </div>
                      </div>
                      <div data-v-1ce89d73="" class="page-content" style="height: 828px; width: 542px; padding: 0px;">
                        <div data-v-1ce89d73="" class="item-container">
                          <div
                            data-v-49241710=""
                            data-v-1ce89d73=""
                            class="draggable resizable vdr "
                            style="transform: translate(8px, 9px) rotate(0deg) scale(1, 1); z-index: 1; user-select: auto; width: 58px; height: 810px;"
                            data-is-check="false"
                            data-is-snap="true"><!---->
                            <div
                              data-v-49241710=""
                              class="handle handle-tl"
                              style="width: 8px; height: 8px; top: -5px; left: -5px; cursor: nw-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-tm"
                              style="width: 8px; height: 8px; top: -5px; left: calc(50% - 4px); cursor: n-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-tr"
                              style="width: 8px; height: 8px; top: -5px; right: -5px; cursor: ne-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-mr"
                              style="width: 8px; height: 8px; top: calc(50% - 4px); right: -5px; cursor: e-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-br"
                              style="width: 8px; height: 8px; right: -5px; bottom: -5px; cursor: se-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-bm"
                              style="width: 8px; height: 8px; right: calc(50% - 4px); bottom: -5px; cursor: s-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-bl"
                              style="width: 8px; height: 8px; left: -5px; bottom: -5px; cursor: sw-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-ml"
                              style="width: 8px; height: 8px; top: calc(50% - 4px); left: -5px; cursor: w-resize; display: none;">
                            </div>
                            <div data-v-49241710="" class="com-item-box" style="padding: 0px;">
                              <div
                                data-v-2bb504ce=""
                                data-v-739aab25=""
                                class="text-container"
                                state=""
                                data-v-49241710=""
                                style="align-items: center; flex-direction: column;">
                                <div
                                  data-v-2bb504ce=""
                                  contenteditable="true"
                                  class="edit-text drag-cancel placeholder"
                                  style="text-align: center; writing-mode: vertical-lr; font-weight: bold; font-size: 29px; font-family: 中文标点, makefont-yjEYWhDelfoi8, 宋体; line-height: 200%; letter-spacing: 3px; color: rgb(0, 0, 0);">
                                  请输入标题</div>
                              </div>
                            </div>
                          </div>
                          <div
                            data-v-49241710=""
                            data-v-1ce89d73=""
                            class="draggable resizable vdr "
                            style="transform: translate(66px, 9px) rotate(0deg) scale(1, 1); z-index: 0; user-select: auto; width: 468px; height: 810px;"
                            data-is-check="false"
                            data-is-snap="true"><!---->
                            <div
                              data-v-49241710=""
                              class="handle handle-tl"
                              style="width: 8px; height: 8px; top: -5px; left: -5px; cursor: nw-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-tm"
                              style="width: 8px; height: 8px; top: -5px; left: calc(50% - 4px); cursor: n-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-tr"
                              style="width: 8px; height: 8px; top: -5px; right: -5px; cursor: ne-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-mr"
                              style="width: 8px; height: 8px; top: calc(50% - 4px); right: -5px; cursor: e-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-br"
                              style="width: 8px; height: 8px; right: -5px; bottom: -5px; cursor: se-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-bm"
                              style="width: 8px; height: 8px; right: calc(50% - 4px); bottom: -5px; cursor: s-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-bl"
                              style="width: 8px; height: 8px; left: -5px; bottom: -5px; cursor: sw-resize; display: none;">
                            </div>
                            <div
                              data-v-49241710=""
                              class="handle handle-ml"
                              style="width: 8px; height: 8px; top: calc(50% - 4px); left: -5px; cursor: w-resize; display: none;">
                            </div>
                            <div data-v-49241710="" class="com-item-box" style="padding: 0px;">
                              <div
                                data-v-2bb504ce=""
                                data-v-739aab25=""
                                class="vdr active text-container"
                                state=""
                                data-v-49241710=""
                                style="align-items: flex-start; flex-direction: column;">
                                <div
                                  data-v-2bb504ce=""
                                  contenteditable="true"
                                  class="edit-text drag-cancel"
                                  style="text-align: justify; writing-mode: vertical-lr; font-size: 24px; font-family: 中文标点, makefont-yjEYWhDelfoi8, 宋体; line-height: 150%; text-align-last: left; letter-spacing: 3px; color: rgb(0, 0, 0);">
                                  范德萨发撒打发士大夫阿斯顿发生</div>
                              </div>
                            </div>
                          </div><span data-v-1ce89d73="" class="ref-line v-line" style="display: none;"></span><span
                            data-v-1ce89d73=""
                            class="ref-line v-line"
                            style="display: none;"></span><span
                              data-v-1ce89d73=""
                              class="ref-line v-line"
                              style="display: none;"></span><span
                                data-v-1ce89d73=""
                                class="ref-line h-line"
                                style="display: none;"></span><span
                                  data-v-1ce89d73=""
                                  class="ref-line h-line"
                                  style="display: none;"></span><span
                                    data-v-1ce89d73=""
                                    class="ref-line h-line"
                                    style="display: none;"></span><!----><!---->
                        </div>
                      </div>
                    </div>
                  </div>
                  <div data-v-1ce89d73="" class="paging-tools"><button
                    data-v-1ce89d73=""
                    disabled="disabled"
                    type="button"
                    class="el-button el-button--default el-button--small is-disabled"><!----><!----><span>上一页</span></button><button
                      data-v-1ce89d73=""
                      disabled="disabled"
                      type="button"
                      class="el-button el-button--default el-button--small is-disabled"><!----><!----><span>下一页</span></button>
                  </div>
                </div>
              </div>
              <div data-v-1ce89d73="" class="pw-right">
                <div data-v-d44e5bba="" data-v-1ce89d73="" class="pw-right-all">
                  <div data-v-d44e5bba="" class="pw-right-bar">
                    <div data-v-d44e5bba="" class="title">功能</div>
                    <div data-v-d44e5bba="" class="item">
                      <div data-v-2fda8f4e="" data-v-d44e5bba="" class="h-svg-icon-box"><!----><span
                        data-v-2fda8f4e=""
                        class="h-icon-yeshu"
                        style="font-size: 21px; height: 21px; width: 21px;"></span></div><span
                          data-v-d44e5bba=""
                          class="txt">页面</span>
                    </div>
                    <div data-v-d44e5bba="" class="item">
                      <div data-v-2fda8f4e="" data-v-d44e5bba="" class="h-svg-icon-box"><!----><span
                        data-v-2fda8f4e=""
                        class="h-icon-moban"
                        style="font-size: 21px; height: 21px; width: 21px;"></span></div><span
                          data-v-d44e5bba=""
                          class="txt">模板</span>
                    </div>
                    <div data-v-d44e5bba="" class="item">
                      <div data-v-2fda8f4e="" data-v-d44e5bba="" class="h-svg-icon-box"><!----><span
                        data-v-2fda8f4e=""
                        class="h-icon-sucai"
                        style="font-size: 21px; height: 21px; width: 21px;"></span></div><span
                          data-v-d44e5bba=""
                          class="txt">素材</span>
                    </div>
                    <div data-v-d44e5bba="" class="item on">
                      <div data-v-2fda8f4e="" data-v-d44e5bba="" class="h-svg-icon-box"><span
                        data-v-2fda8f4e=""
                        class="svg-icon"
                        style=""><svg
                          data-v-2fda8f4e=""
                          aria-hidden="true"
                          class="pw-icon"
                          style="height: 21px; width: 21px;">
                          <use data-v-2fda8f4e="" xlink:href="#h-icon-shuxing"></use>
                        </svg></span><!----></div><span data-v-d44e5bba="" class="txt">属性</span>
                    </div>
                    <div data-v-d44e5bba="" class="item">
                      <div data-v-2fda8f4e="" data-v-d44e5bba="" class="h-svg-icon-box"><!----><span
                        data-v-2fda8f4e=""
                        class="h-icon-yuansu"
                        style="font-size: 21px; height: 21px; width: 21px;"></span></div><span
                          data-v-d44e5bba=""
                          class="txt">元素</span>
                    </div><a
                      data-v-d44e5bba=""
                      href="https://www.baijiayoupu.com/school/6506.html"
                      target="_blank"
                      class="item">
                      <div data-v-2fda8f4e="" data-v-d44e5bba="" class="h-svg-icon-box"><span
                        data-v-2fda8f4e=""
                        class="svg-icon"><svg
                          data-v-2fda8f4e=""
                          aria-hidden="true"
                          class="pw-icon"
                          style="height: 21px; width: 21px;">
                          <use data-v-2fda8f4e="" xlink:href="#h-icon-jiaocheng"></use>
                        </svg></span><!----></div><span data-v-d44e5bba="" class="txt">教程</span>
                    </a><!---->
                  </div>
                </div>
              </div>
              <div data-v-bc15e642="" data-v-1ce89d73="" class="pw-status-bar status-bar">
                <div data-v-bc15e642="" class="flex flex-center left-bar">
                  <div data-v-bc15e642="" class="item">字数：15</div>
                </div>
                <div data-v-bc15e642="" class="el-pagination el-pagination--small"><button
                                                                                     type="button"
                                                                                     disabled="disabled"
                                                                                     class="btn-prev"><span>上一页</span></button>
                  <ul class="el-pager">
                    <li class="number active">1</li><!----><!----><!---->
                  </ul><button type="button" disabled="disabled" class="btn-next"><span>下一页</span></button><span
                    class="el-pagination__jump">前往<div
                    class="el-input el-input--small el-pagination__editor is-in-pagination"><!----><input
                    type="number"
                    autocomplete="off"
                    min="1"
                    max="1"
                    class="el-input__inner"><!----><!----><!----><!----></div>页</span>
                </div>
                <div data-v-bc15e642="" class="flex flex-center right-bar">
                  <div data-v-bc15e642="" class="item">
                    <div data-v-ec8e2dd2="" data-v-bc15e642="" class="h-svg-icon-box"><span
                      data-v-ec8e2dd2=""
                      class="svg-icon"><svg
                        data-v-ec8e2dd2=""
                        aria-hidden="true"
                        class="pw-icon"
                        style="height: 21px; width: 21px;">
                        <use data-v-ec8e2dd2="" xlink:href="#h-icon-zuijiashitu"></use>
                      </svg></span><!----></div><span data-v-bc15e642="" class="txt">最佳视图</span>
                  </div>
                  <div data-v-bc15e642="" class="item">
                    <div data-v-ec8e2dd2="" data-v-bc15e642="" class="h-svg-icon-box"><span
                      data-v-ec8e2dd2=""
                      class="svg-icon"><svg
                        data-v-ec8e2dd2=""
                        aria-hidden="true"
                        class="pw-icon"
                        style="height: 18px; width: 18px;">
                        <use data-v-ec8e2dd2="" xlink:href="#h-icon-quanpingshitu"></use>
                      </svg></span><!----></div><span data-v-bc15e642="" class="txt">全屏视图</span>
                  </div>
                  <div data-v-bc15e642="" class="scale"><span data-v-bc15e642="" class="percent"> 100% </span>
                    <div data-v-ec8e2dd2="" data-v-bc15e642="" class="h-svg-icon-box"><!----><span
                      data-v-ec8e2dd2=""
                      class="h-icon-jianhao1"
                      style="font-size: 18px; height: 18px; width: 18px;"></span></div>
                    <div data-v-bc15e642="" class="flex1 slider-box">
                      <div
                        data-v-bc15e642=""
                        role="slider"
                        aria-valuemin="2.5"
                        aria-valuemax="100"
                        aria-orientation="horizontal"
                        class="el-slider"
                        size="mini"
                        aria-valuetext="25"
                        aria-label="slider between 2.5 and 100"><!---->
                        <div class="el-slider__runway">
                          <div class="el-slider__bar" style="left: 0%; width: 23.0769%;"></div>
                          <div tabindex="0" class="el-slider__button-wrapper" style="left: 23.0769%;">
                            <div class="el-tooltip el-slider__button" aria-describedby="el-tooltip-1808" tabindex="0">
                            </div>
                          </div><!----><!---->
                        </div>
                      </div>
                    </div>
                    <div data-v-ec8e2dd2="" data-v-bc15e642="" class="h-svg-icon-box"><!----><span
                      data-v-ec8e2dd2=""
                      class="h-icon-jiahao1"
                      style="font-size: 18px; height: 18px; width: 18px;"></span></div>
                  </div>
                </div>
              </div>
            </div>
            <div data-v-1ce89d73="" class="content-right"><!----></div>
          </div>
        </div>
        <div data-v-902f7874="" data-v-1ce89d73="">
          <div data-v-6f603704="" data-v-902f7874="" class="el-dialog__wrapper" style="display: none;">
            <div role="dialog" aria-modal="true" aria-label="裁剪图片" class="el-dialog" style="margin-top: 15vh;">
              <div class="el-dialog__header"><span class="el-dialog__title">裁剪图片</span><button
                type="button"
                aria-label="Close"
                class="el-dialog__headerbtn"><i
                  class="el-dialog__close el-icon el-icon-close"></i></button></div><!---->
              <div class="el-dialog__footer"><span data-v-6f603704="" class="dialog-footer"><button
                data-v-6f603704=""
                type="button"
                class="el-button el-button--default el-button--small"><!----><!----><span>取
                  消</span></button><button
                data-v-6f603704=""
                type="button"
                class="el-button el-button--primary el-button--small"><!----><!----><span>确 定</span></button></span>
              </div>
            </div>
          </div>
          <ul data-v-3da5e112="" data-v-902f7874="" class="vue-contextmenu-listWrapper vue-contextmenu">
            <li data-v-3da5e112="" class="context-menu-list">
              <div data-v-3da5e112="">
                <div data-v-3da5e112="" class="no-child-btn btn-wrapper-simple"><i
                  data-v-3da5e112=""
                  class="nav-icon-fontawe"></i><span data-v-3da5e112="" class="nav-name-right"></span></div>
              </div>
            </li>
          </ul>
        </div>
        <div data-v-3d1e030b="" data-v-1ce89d73="" class="auto-paging">
          <div data-v-3d1e030b="" class="textContainer">
            <div data-v-3d1e030b=""></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ToolBar from '@/components/Editor/components/toolbar'
export default {
  name: 'Exception403',
  components: {
    ToolBar
  },
  methods: {
    toHome () {
      this.$router.push({ path: '/' })
    }
  }
}
</script>
<style lang="less" scoped>
[data-v-05cce657] .el-dialog__body {
  padding-bottom: 20px
}

[data-v-05cce657] .el-dialog__footer {
  padding-top: 30px;
  border-top: 1px dashed #e5e5e5
}

[data-v-e89adbf8] .el-form .el-input {
  width: 360px
}

[data-v-e89adbf8] .el-dialog__body {
  padding-bottom: 20px
}

[data-v-e89adbf8] .el-dialog__footer {
  padding-top: 30px;
  border-top: 1px dashed #e5e5e5
}

.tools[data-v-eeac83fe] {
  flex-shrink: 0;
  position: relative;
  height: 80px;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 8px 6px 0 rgba(0, 0, 0, .04);
  z-index: 2;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 15px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.tools.pointer-events[data-v-eeac83fe] {
  pointer-events: none
}

.tools .add[data-v-eeac83fe] {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer
}

.tools .add .txt[data-v-eeac83fe] {
  padding-top: 5px
}

.tools .add:hover .txt[data-v-eeac83fe] {
  color: #ff7926
}

.tools .icon-box[data-v-eeac83fe] {
  display: flex;
  flex-wrap: wrap
}

.tools .icon-box .item[data-v-eeac83fe] {
  height: 30px;
  width: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer
}

.tools .icon-box .item[data-v-eeac83fe]:hover {
  background: #ebeef5
}

.catalog-li[data-v-1e542682] {
  width: 100%
}

.catalog-li .catalog-item[data-v-1e542682] {
  margin: 0
}

.catalog-li .catalog-item .title[data-v-1e542682] {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between
}

.catalog-li .catalog-item .title:hover>.op[data-v-1e542682] {
  display: flex
}

.catalog-li .catalog-item .title>.content[data-v-1e542682] {
  flex: 1
}

.catalog-li .catalog-item .title>.content.active .icon[data-v-1e542682],
.catalog-li .catalog-item .title>.content.active[data-v-1e542682] {
  color: #f86e04
}

.catalog-li .catalog-item .title>.op[data-v-1e542682] {
  display: none
}

.catalog-li .catalog-item .title>.op i[data-v-1e542682]:hover {
  color: #f86e04
}

.catalog-li .catalog-item .add-cell[data-v-1e542682] {
  margin: 0 10px;
  padding: 10px
}

.catalog-li .catalog-item .add-cell.add-puwen[data-v-1e542682] {
  background-color: #f5f5f5
}

.catalog-li .catalog-item .add-cell .btn[data-v-1e542682] {
  border: 1px dashed #f86e04;
  border-radius: 2px;
  width: 110px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff7926;
  cursor: pointer
}

.catalog-li .catalog-item .add-cell .btn .txt[data-v-1e542682] {
  margin-left: 10px
}

.catalog-li .catalog-item.catalog[data-v-1e542682] {
  font-size: 16px;
  cursor: pointer
}

.catalog-li .catalog-item.catalog .icon[data-v-1e542682] {
  font-size: 18px
}

.catalog-li .catalog-item.article[data-v-1e542682] {
  font-size: 14px;
  margin: 0 10px;
  background-color: #f5f5f5
}

.catalog-li .catalog-item.article .current[data-v-1e542682] {
  color: #f86e04
}

.catalog-li .catalog-item.article .publish-status[data-v-1e542682] {
  color: #b2b2b2
}

.catalog-li .catalog-item.article .publish-status.publish[data-v-1e542682] {
  color: purple
}

.edit-input[data-v-84e0bf1c] {
  display: inline-block;
  padding: 0 10px;
  min-width: 60px;
  height: 26px;
  line-height: 26px;
  box-sizing: border-box;
  background-color: #ebeef5;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  color: grey
}

.edit-input.on[data-v-84e0bf1c] {
  background-color: #fdf1e7;
  color: #f86e04;
  box-shadow: inset 0 0 0 1px #f86e04
}

.edit-input .inp[data-v-84e0bf1c] {
  border-radius: 2px;
  background-color: #fff;
  box-sizing: border-box;
  padding: 0 5px;
  width: 100px
}

.edit-input .icon[data-v-84e0bf1c] {
  color: #ff7926;
  margin-left: 5px;
  transition: all .3s
}

.edit-input .icon[data-v-84e0bf1c]:hover {
  transform: scale(1.2) translateY(-5%)
}

.title[data-v-6cbf505c] {
  padding: 10px 0
}

.list[data-v-6cbf505c] {
  display: flex;
  flex-wrap: wrap
}

.list .disabled[data-v-6cbf505c] {
  opacity: .4;
  pointer-events: none
}

.list .item[data-v-6cbf505c] {
  margin-right: 10px;
  margin-bottom: 10px
}

.row[data-v-0428944b] {
  display: flex;
  align-items: center
}

.pagin[data-v-0428944b] {
  margin-top: 10px;
  text-align: right
}

.catalog[data-v-0428944b] {
  color: #f86e04;
  border: 1px solid #f86e04;
  text-align: center;
  margin-right: 10px;
  padding: 0 5px;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: keep-all
}

.m-l-5[data-v-0428944b] {
  margin-left: 5px
}

.m-l-10[data-v-0428944b] {
  margin-left: 10px
}

[data-v-0428944b] .el-avatar img {
  width: 100%
}

.catalog[data-v-4a403703] {
  display: flex;
  flex-direction: column;
  height: 0;
  flex: 1
}

.catalog .tabs[data-v-4a403703] {
  flex-shrink: 0;
  display: flex;
  background: #f1f3f6;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.catalog .tabs .item[data-v-4a403703] {
  flex: 1;
  height: 47px;
  line-height: 47px;
  text-align: center
}

.catalog .tabs .item.active[data-v-4a403703] {
  background: #fff
}

.catalog .catalog-list[data-v-4a403703] {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden
}

.catalog .catalog-list .el-scrollbar[data-v-4a403703] {
  height: 100%
}

.catalog .catalog-list .el-scrollbar .add-catalog[data-v-4a403703] {
  padding: 10px
}

.catalog .catalog-list .el-scrollbar .add-catalog .btn[data-v-4a403703] {
  border: 1px dashed #f86e04;
  border-radius: 2px;
  width: 110px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff7926;
  cursor: pointer
}

.catalog .catalog-list .el-scrollbar .add-catalog .btn .txt[data-v-4a403703] {
  margin-left: 10px
}

.catalog .catalog-footer[data-v-4a403703] {
  width: 100%;
  height: 78px;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 10px 10px 0 0;
  background: #f9f5f1
}

.catalog .catalog-footer .row[data-v-4a403703] {
  font-size: 16px;
  line-height: 34px;
  color: #666
}

.catalog .catalog-footer .row .on[data-v-4a403703] {
  color: #f86e04
}

.recycle-bin[data-v-4a403703] {
  display: flex;
  align-items: center;
  color: #f86e04 !important;
  cursor: pointer
}

.recycle-bin .el-icon-delete[data-v-4a403703] {
  font-size: 18px;
  margin-right: 5px
}

.h-svg-icon-box[data-v-2fda8f4e] {
  display: inline-flex
}

.svg-icon[data-v-2fda8f4e] {
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.tool-item[data-v-e27a1336] {
  cursor: pointer
}

.tool-item .item[data-v-e27a1336] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-e27a1336],
.tool-item .item[data-v-e27a1336]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-e27a1336] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-e27a1336] {
  word-break: keep-all
}

.tool-title[data-v-e27a1336] {
  border-radius: 0
}

.tool-title .title[data-v-e27a1336] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-e27a1336] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-e27a1336] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-e27a1336]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-e27a1336] {
  margin-left: 4px
}

.dropdown-list li[data-v-e27a1336]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-e27a1336] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-e27a1336] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-e27a1336] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-e27a1336] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-e27a1336] {
  margin-top: 3px
}

.dropdown-list .item[data-v-e27a1336] {
  padding: 8px
}

.tool-item[data-v-d864f666] {
  cursor: pointer
}

.tool-item .item[data-v-d864f666] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-d864f666],
.tool-item .item[data-v-d864f666]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-d864f666] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-d864f666] {
  word-break: keep-all
}

.tool-title[data-v-d864f666] {
  border-radius: 0
}

.tool-title .title[data-v-d864f666] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-d864f666] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-d864f666] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-d864f666]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-d864f666] {
  margin-left: 4px
}

.dropdown-list li[data-v-d864f666]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-d864f666] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-d864f666] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-d864f666] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-d864f666] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-d864f666] {
  margin-top: 3px
}

li[data-v-53faed55],
ul[data-v-53faed55] {
  list-style: none;
  margin: 0;
  padding: 0
}

.predefine[data-v-53faed55] {
  position: relative;
  padding: 8px
}

.predefine .title[data-v-53faed55] {
  padding: 5px 0;
  cursor: default
}

.predefine .color-list[data-v-53faed55] {
  display: flex;
  justify-content: space-between
}

.predefine .color-list-2[data-v-53faed55] {
  margin-top: 6px
}

.predefine .color-item[data-v-53faed55] {
  cursor: pointer;
  height: 15px;
  width: 15px;
  transition: all .3s ease
}

.predefine .color-item[data-v-53faed55]:hover {
  box-shadow: 0 0 5px rgba(0, 0, 0, .4);
  transform: scale(1.3)
}

.predefine .color-item.white[data-v-53faed55] {
  border: 1px solid #e2e6ed;
  box-sizing: border-box
}

.sv-panel[data-v-aba1b996] {
  height: 120px;
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid #d6d6d6;
  position: relative;
  width: 100%;
  background: linear-gradient(0deg, #000, transparent)
}

.sv-panel .black[data-v-aba1b996],
.sv-panel .white[data-v-aba1b996] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0
}

.sv-panel .white[data-v-aba1b996] {
  background: linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))
}

.sv-panel .black[data-v-aba1b996] {
  background: linear-gradient(0deg, #000, transparent)
}

.sv-panel .thumb[data-v-aba1b996] {
  position: absolute
}

.sv-panel .thumb>div[data-v-aba1b996] {
  cursor: head;
  width: 4px;
  height: 4px;
  box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, .3), 0 0 1px 2px rgba(0, 0, 0, .4);
  border-radius: 50%;
  transform: translate(-2px, -2px)
}

.hue[data-v-4c2c4fb2] {
  position: relative;
  margin-top: 8px
}

.hue .bar[data-v-4c2c4fb2] {
  height: 12px;
  background: linear-gradient(90deg, red 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, red)
}

.hue .thumb[data-v-4c2c4fb2] {
  position: absolute;
  cursor: pointer;
  box-sizing: border-box;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  border-radius: 1px;
  background: #fff;
  border: 1px solid #f0f0f0;
  box-shadow: 0 0 2px rgba(0, 0, 0, .6);
  z-index: 1
}

.alpha-slider[data-v-0e9de8f0] {
  margin-top: 8px;
  position: relative;
  box-sizing: border-box;
  height: 12px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==)
}

.alpha-slider .bar[data-v-0e9de8f0] {
  position: relative;
  height: 100%
}

.alpha-slider .thumb[data-v-0e9de8f0] {
  position: absolute;
  cursor: pointer;
  box-sizing: border-box;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  border-radius: 1px;
  background: #fff;
  border: 1px solid #f0f0f0;
  box-shadow: 0 0 2px rgba(0, 0, 0, .6);
  z-index: 1
}

.panel[data-v-43bda5f2] {
  color: rgba(0, 0, 0, .8);
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e3e3e3;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .12);
  position: absolute;
  z-index: 99
}

.color-box[data-v-43bda5f2] {
  padding: 8px;
  width: 200px
}

.color-box .bar-box[data-v-43bda5f2] {
  display: flex
}

.color-box .bar-box .bar[data-v-43bda5f2] {
  width: 170px
}

.color-box .bar-box .color[data-v-43bda5f2] {
  flex: 1;
  margin-left: 5px;
  margin-top: 8px
}

.color-box .display[data-v-43bda5f2] {
  display: flex;
  justify-content: space-between;
  margin-top: 8px
}

.color-box .display input[data-v-43bda5f2] {
  padding: 4px;
  font-size: 11px;
  box-shadow: inset 0 0 0 1px #ccc;
  width: 100%;
  box-sizing: border-box
}

.color-box .display .hex[data-v-43bda5f2],
.color-box .display .rgba[data-v-43bda5f2] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center
}

.color-box .display .hex span[data-v-43bda5f2],
.color-box .display .rgba span[data-v-43bda5f2] {
  font-size: 11px;
  color: #222
}

.color-box .display .hex[data-v-43bda5f2] {
  width: 65px
}

.color-box .display .rgba[data-v-43bda5f2] {
  width: 30px
}

.color-picker[data-v-38b498f2] {
  width: 200px
}

.color[data-v-38b498f2] {
  position: relative
}

.color[data-v-38b498f2]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.color .row[data-v-38b498f2] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.color .row .title[data-v-38b498f2] {
  margin-left: 4px
}

.tool-item[data-v-7b4b48b5] {
  cursor: pointer
}

.tool-item .item[data-v-7b4b48b5] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-7b4b48b5],
.tool-item .item[data-v-7b4b48b5]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-7b4b48b5] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-7b4b48b5] {
  word-break: keep-all
}

.tool-title[data-v-7b4b48b5] {
  border-radius: 0
}

.tool-title .title[data-v-7b4b48b5] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-7b4b48b5] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-7b4b48b5] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-7b4b48b5]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-7b4b48b5] {
  margin-left: 4px
}

.dropdown-list li[data-v-7b4b48b5]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-7b4b48b5] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-7b4b48b5] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-7b4b48b5] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-7b4b48b5] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-7b4b48b5] {
  margin-top: 3px
}

.line-style[data-v-7b4b48b5] {
  width: 100px
}

.line-style .line[data-v-7b4b48b5] {
  border: none;
  background: none;
  width: 100%;
  margin: 8px 0
}

.line-width-item[data-v-7b4b48b5] {
  width: 50px
}

.tool-item[data-v-e1b2e8ec] {
  cursor: pointer
}

.tool-item .item[data-v-e1b2e8ec] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-e1b2e8ec],
.tool-item .item[data-v-e1b2e8ec]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-e1b2e8ec] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-e1b2e8ec] {
  word-break: keep-all
}

.tool-title[data-v-e1b2e8ec] {
  border-radius: 0
}

.tool-title .title[data-v-e1b2e8ec] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-e1b2e8ec] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-e1b2e8ec] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-e1b2e8ec]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-e1b2e8ec] {
  margin-left: 4px
}

.dropdown-list li[data-v-e1b2e8ec]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-e1b2e8ec] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-e1b2e8ec] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-e1b2e8ec] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-e1b2e8ec] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-e1b2e8ec] {
  margin-top: 3px
}

.item[data-v-e1b2e8ec] {
  flex-direction: column
}

.tool-item[data-v-49e72aac] {
  cursor: pointer
}

.tool-item .item[data-v-49e72aac] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-49e72aac],
.tool-item .item[data-v-49e72aac]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-49e72aac] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-49e72aac] {
  word-break: keep-all
}

.tool-title[data-v-49e72aac] {
  border-radius: 0
}

.tool-title .title[data-v-49e72aac] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-49e72aac] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-49e72aac] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-49e72aac]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-49e72aac] {
  margin-left: 4px
}

.dropdown-list li[data-v-49e72aac]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-49e72aac] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-49e72aac] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-49e72aac] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-49e72aac] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-49e72aac] {
  margin-top: 3px
}

.tool-item[data-v-f34c85de] {
  cursor: pointer
}

.tool-item .item[data-v-f34c85de] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-f34c85de],
.tool-item .item[data-v-f34c85de]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-f34c85de] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-f34c85de] {
  word-break: keep-all
}

.tool-title[data-v-f34c85de] {
  border-radius: 0
}

.tool-title .title[data-v-f34c85de] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-f34c85de] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-f34c85de] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-f34c85de]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-f34c85de] {
  margin-left: 4px
}

.dropdown-list li[data-v-f34c85de]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-f34c85de] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-f34c85de] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-f34c85de] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-f34c85de] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-f34c85de] {
  margin-top: 3px
}

.auto-paging[data-v-3d1e030b] {
  position: absolute;
  visibility: hidden;
  box-sizing: border-box;
  border: 1px solid #000;
  background: red;
  z-index: 1000
}

.textContainer[data-v-3d1e030b] {
  height: 100%;
  width: 100%;
  display: flex
}

.tool-item[data-v-0a720a03] {
  cursor: pointer
}

.tool-item .item[data-v-0a720a03] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0a720a03],
.tool-item .item[data-v-0a720a03]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0a720a03] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0a720a03] {
  word-break: keep-all
}

.tool-title[data-v-0a720a03] {
  border-radius: 0
}

.tool-title .title[data-v-0a720a03] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0a720a03] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0a720a03] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0a720a03]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0a720a03] {
  margin-left: 4px
}

.dropdown-list li[data-v-0a720a03]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0a720a03] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0a720a03] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0a720a03] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0a720a03] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0a720a03] {
  margin-top: 3px
}

.item[data-v-0a720a03] {
  flex-direction: column
}

.tool-item[data-v-1134a084] {
  cursor: pointer
}

.tool-item .item[data-v-1134a084] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-1134a084],
.tool-item .item[data-v-1134a084]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-1134a084] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-1134a084] {
  word-break: keep-all
}

.tool-title[data-v-1134a084] {
  border-radius: 0
}

.tool-title .title[data-v-1134a084] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-1134a084] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-1134a084] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-1134a084]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-1134a084] {
  margin-left: 4px
}

.dropdown-list li[data-v-1134a084]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-1134a084] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-1134a084] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-1134a084] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-1134a084] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-1134a084] {
  margin-top: 3px
}

.dropdown-list .disabled[data-v-1134a084] {
  pointer-events: none;
  opacity: .3
}

.tool-item[data-v-2027d200] {
  cursor: pointer
}

.tool-item .item[data-v-2027d200] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-2027d200],
.tool-item .item[data-v-2027d200]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-2027d200] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-2027d200] {
  word-break: keep-all
}

.tool-title[data-v-2027d200] {
  border-radius: 0
}

.tool-title .title[data-v-2027d200] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-2027d200] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-2027d200] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-2027d200]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-2027d200] {
  margin-left: 4px
}

.dropdown-list li[data-v-2027d200]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-2027d200] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-2027d200] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-2027d200] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-2027d200] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-2027d200] {
  margin-top: 3px
}

.item[data-v-2027d200] {
  flex-direction: column
}

.tool-item[data-v-658bc200] {
  cursor: pointer
}

.tool-item .item[data-v-658bc200] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-658bc200],
.tool-item .item[data-v-658bc200]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-658bc200] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-658bc200] {
  word-break: keep-all
}

.tool-title[data-v-658bc200] {
  border-radius: 0
}

.tool-title .title[data-v-658bc200] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-658bc200] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-658bc200] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-658bc200]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-658bc200] {
  margin-left: 4px
}

.dropdown-list li[data-v-658bc200]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-658bc200] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-658bc200] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-658bc200] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-658bc200] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-658bc200] {
  margin-top: 3px
}

.item[data-v-658bc200] {
  flex-direction: column
}

.orc_container .header[data-v-658bc200] {
  display: flex;
  height: 300px;
  margin-bottom: 20px;
  justify-content: space-between
}

.orc_container .header .img[data-v-658bc200] {
  padding: 10px;
  background-color: hsla(0, 0%, 87.1%, .8705882352941177);
  border: 1px solid hsla(0, 0%, 87.1%, .8705882352941177);
  width: 40%;
  display: flex;
  justify-content: center;
  align-items: center
}

.orc_container .header .img img[data-v-658bc200] {
  max-width: 100%;
  max-height: 100%
}

.orc_container .header .btn[data-v-658bc200] {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column
}

.orc_container .header .btn .el-button[data-v-658bc200] {
  margin: 0;
  margin-bottom: 10px
}

.orc_container .header .result[data-v-658bc200] {
  width: 40%
}

.orc_container .words[data-v-658bc200] {
  position: relative
}

.orc_container .words .word[data-v-658bc200] {
  position: absolute;
  border: 1px solid #000;
  box-sizing: border-box
}

.orc_container .tools[data-v-658bc200] {
  display: flex;
  height: 100px
}

.orc_container[data-v-658bc200] .h-slider .el-slider__runway {
  width: 2px !important
}

.orc_container[data-v-658bc200] .h-slider .el-slider__bar {
  width: 2px !important;
  background-color: #f86e04 !important
}

.orc_container[data-v-658bc200] .h-slider .el-slider__button-wrapper {
  left: -17px
}

.orc_container[data-v-658bc200] .h-slider .el-slider__button {
  height: 10px;
  width: 10px;
  box-sizing: border-box;
  border: 0;
  box-shadow: 0 0 3px 1px hsla(0, 0%, 42%, .17)
}

.orc_container[data-v-658bc200] .v-slider .el-slider__runway {
  height: 2px !important;
  width: 100px
}

.orc_container[data-v-658bc200] .v-slider .el-slider__bar {
  height: 2px !important;
  background-color: #f86e04 !important
}

.orc_container[data-v-658bc200] .v-slider .el-slider__button-wrapper {
  top: -17px
}

.orc_container[data-v-658bc200] .v-slider .el-slider__button {
  height: 10px;
  width: 10px;
  box-sizing: border-box;
  border: 0;
  box-shadow: 0 0 3px 1px hsla(0, 0%, 42%, .17)
}

.setting-section[data-v-3093231e] {
  display: flex;
  height: 36px;
  margin-top: 26px
}

.setting-section .setting-section-left[data-v-3093231e] {
  width: 60px
}

.setting-section .setting-name[data-v-3093231e] {
  margin-right: 5px;
  line-height: 36px
}

.setting-section .el-input-number[data-v-3093231e] {
  width: 80px
}

.setting-section .unit[data-v-3093231e] {
  margin-left: 2px
}

.setting-section .ml38[data-v-3093231e] {
  margin-left: 38px
}

.tool-item[data-v-0d9796fe] {
  cursor: pointer
}

.tool-item .item[data-v-0d9796fe] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0d9796fe],
.tool-item .item[data-v-0d9796fe]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0d9796fe] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0d9796fe] {
  word-break: keep-all
}

.tool-title[data-v-0d9796fe] {
  border-radius: 0
}

.tool-title .title[data-v-0d9796fe] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0d9796fe] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0d9796fe] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0d9796fe]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0d9796fe] {
  margin-left: 4px
}

.dropdown-list li[data-v-0d9796fe]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0d9796fe] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0d9796fe] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0d9796fe] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0d9796fe] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0d9796fe] {
  margin-top: 3px
}

.tool-item[data-v-0cd29738] {
  cursor: pointer
}

.tool-item .item[data-v-0cd29738] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0cd29738],
.tool-item .item[data-v-0cd29738]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0cd29738] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0cd29738] {
  word-break: keep-all
}

.tool-title[data-v-0cd29738] {
  border-radius: 0
}

.tool-title .title[data-v-0cd29738] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0cd29738] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0cd29738] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0cd29738]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0cd29738] {
  margin-left: 4px
}

.dropdown-list li[data-v-0cd29738]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0cd29738] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0cd29738] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0cd29738] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0cd29738] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0cd29738] {
  margin-top: 3px
}

.item[data-v-0cd29738] {
  flex-direction: column
}

.tool-item[data-v-50dae217] {
  cursor: pointer
}

.tool-item .item[data-v-50dae217] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-50dae217],
.tool-item .item[data-v-50dae217]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-50dae217] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-50dae217] {
  word-break: keep-all
}

.tool-title[data-v-50dae217] {
  border-radius: 0
}

.tool-title .title[data-v-50dae217] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-50dae217] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-50dae217] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-50dae217]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-50dae217] {
  margin-left: 4px
}

.dropdown-list li[data-v-50dae217]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-50dae217] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-50dae217] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-50dae217] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-50dae217] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-50dae217] {
  margin-top: 3px
}

.item[data-v-50dae217] {
  flex-direction: column
}

.tool-item[data-v-02439280] {
  cursor: pointer
}

.tool-item .item[data-v-02439280] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-02439280],
.tool-item .item[data-v-02439280]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-02439280] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-02439280] {
  word-break: keep-all
}

.tool-title[data-v-02439280] {
  border-radius: 0
}

.tool-title .title[data-v-02439280] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-02439280] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-02439280] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-02439280]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-02439280] {
  margin-left: 4px
}

.dropdown-list li[data-v-02439280]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-02439280] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-02439280] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-02439280] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-02439280] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-02439280] {
  margin-top: 3px
}

.item .icon[data-v-02439280] {
  width: 16px;
  height: 16px;
  margin-right: 5px
}

.tool-item[data-v-0d499a54] {
  cursor: pointer
}

.tool-item .item[data-v-0d499a54] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0d499a54],
.tool-item .item[data-v-0d499a54]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0d499a54] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0d499a54] {
  word-break: keep-all
}

.tool-title[data-v-0d499a54] {
  border-radius: 0
}

.tool-title .title[data-v-0d499a54] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0d499a54] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0d499a54] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0d499a54]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0d499a54] {
  margin-left: 4px
}

.dropdown-list li[data-v-0d499a54]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0d499a54] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0d499a54] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0d499a54] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0d499a54] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0d499a54] {
  margin-top: 3px
}

.textbox[data-v-0d499a54] {
  flex-direction: column;
  justify-content: center
}

.textbox .text[data-v-0d499a54],
.textbox[data-v-0d499a54] {
  display: flex;
  align-items: center
}

.tool-item[data-v-64c85647] {
  cursor: pointer
}

.tool-item .item[data-v-64c85647] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-64c85647],
.tool-item .item[data-v-64c85647]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-64c85647] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-64c85647] {
  word-break: keep-all
}

.tool-title[data-v-64c85647] {
  border-radius: 0
}

.tool-title .title[data-v-64c85647] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-64c85647] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-64c85647] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-64c85647]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-64c85647] {
  margin-left: 4px
}

.dropdown-list li[data-v-64c85647]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-64c85647] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-64c85647] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-64c85647] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-64c85647] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-64c85647] {
  margin-top: 3px
}

.item[data-v-64c85647] {
  flex-direction: column
}

.tool-item[data-v-68cb8ad0] {
  cursor: pointer
}

.tool-item .item[data-v-68cb8ad0] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-68cb8ad0],
.tool-item .item[data-v-68cb8ad0]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-68cb8ad0] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-68cb8ad0] {
  word-break: keep-all
}

.tool-title[data-v-68cb8ad0] {
  border-radius: 0
}

.tool-title .title[data-v-68cb8ad0] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-68cb8ad0] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-68cb8ad0] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-68cb8ad0]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-68cb8ad0] {
  margin-left: 4px
}

.dropdown-list li[data-v-68cb8ad0]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-68cb8ad0] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-68cb8ad0] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-68cb8ad0] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-68cb8ad0] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-68cb8ad0] {
  margin-top: 3px
}

.table-box[data-v-68cb8ad0] {
  padding: 8px
}

.table-box .row[data-v-68cb8ad0] {
  padding: 8px 0;
  cursor: pointer
}

table[data-v-68cb8ad0] {
  width: 140px;
  cursor: default
}

table td div[data-v-68cb8ad0] {
  height: 12px;
  width: 12px;
  border: 1px solid #ccc
}

table td div.selected[data-v-68cb8ad0] {
  background: rgba(255, 121, 38, .6)
}

.setting-section-right[data-v-68cb8ad0] {
  display: flex;
  align-items: center
}

.tool-item[data-v-0fd1815a] {
  cursor: pointer
}

.tool-item .item[data-v-0fd1815a] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0fd1815a],
.tool-item .item[data-v-0fd1815a]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0fd1815a] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0fd1815a] {
  word-break: keep-all
}

.tool-title[data-v-0fd1815a] {
  border-radius: 0
}

.tool-title .title[data-v-0fd1815a] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0fd1815a] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0fd1815a] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0fd1815a]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0fd1815a] {
  margin-left: 4px
}

.dropdown-list li[data-v-0fd1815a]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0fd1815a] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0fd1815a] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0fd1815a] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0fd1815a] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0fd1815a] {
  margin-top: 3px
}

.textbox[data-v-0fd1815a] {
  flex-direction: column;
  justify-content: center
}

.textbox .text[data-v-0fd1815a],
.textbox[data-v-0fd1815a] {
  display: flex;
  align-items: center
}

.tool-item[data-v-1877ddb3] {
  cursor: pointer
}

.tool-item .item[data-v-1877ddb3] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-1877ddb3],
.tool-item .item[data-v-1877ddb3]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-1877ddb3] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-1877ddb3] {
  word-break: keep-all
}

.tool-title[data-v-1877ddb3] {
  border-radius: 0
}

.tool-title .title[data-v-1877ddb3] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-1877ddb3] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-1877ddb3] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-1877ddb3]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-1877ddb3] {
  margin-left: 4px
}

.dropdown-list li[data-v-1877ddb3]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-1877ddb3] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-1877ddb3] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-1877ddb3] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-1877ddb3] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-1877ddb3] {
  margin-top: 3px
}

.tool-item {
  cursor: pointer
}

.tool-item .item {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active,
.tool-item .item:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt {
  word-break: keep-all
}

.tool-title {
  border-radius: 0
}

.tool-title .title {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips {
  max-width: 120px;
  line-height: 150%
}

.picker-item {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title {
  margin-left: 4px
}

.dropdown-list li:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked {
  position: absolute;
  left: 12px
}

.text-style-select-tool {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom] {
  margin-top: 3px
}

.select[data-v-1a61a581] {
  justify-content: center
}

.text[data-v-1a61a581] {
  margin-right: 10px;
  min-width: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.dropdown-list[data-v-1a61a581] {
  max-height: 500px;
  overflow-x: hidden
}

.dropdown-list .inp-box[data-v-1a61a581] {
  position: relative;
  display: flex;
  align-items: center;
  width: 142px;
  box-sizing: border-box;
  height: 35px;
  padding: 0 10px 0 32px
}

.dropdown-list .inp-box .inp[data-v-1a61a581] {
  width: 100%
}

.dropdown-list .inp-box .inp[data-v-1a61a581]::-webkit-input-placeholder {
  color: #999
}

.dropdown-list .inp-box .inp[data-v-1a61a581]::-moz-placeholder {
  color: #999
}

.dropdown-list .inp-box .placeholder[data-v-1a61a581] {
  width: 100%;
  background-color: #fff;
  color: #999
}

.dropdown-list .inp-box .checked[data-v-1a61a581] {
  position: absolute;
  left: 12px
}

.dropdown-list .inp-box .icon[data-v-1a61a581]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.tool-item[data-v-5fbb675f] {
  cursor: pointer
}

.tool-item .item[data-v-5fbb675f] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-5fbb675f],
.tool-item .item[data-v-5fbb675f]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-5fbb675f] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-5fbb675f] {
  word-break: keep-all
}

.tool-title[data-v-5fbb675f] {
  border-radius: 0
}

.tool-title .title[data-v-5fbb675f] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-5fbb675f] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-5fbb675f] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-5fbb675f]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-5fbb675f] {
  margin-left: 4px
}

.dropdown-list li[data-v-5fbb675f]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-5fbb675f] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-5fbb675f] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-5fbb675f] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-5fbb675f] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-5fbb675f] {
  margin-top: 3px
}

.tool-item[data-v-46d8869c] {
  cursor: pointer
}

.tool-item .item[data-v-46d8869c] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-46d8869c],
.tool-item .item[data-v-46d8869c]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-46d8869c] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-46d8869c] {
  word-break: keep-all
}

.tool-title[data-v-46d8869c] {
  border-radius: 0
}

.tool-title .title[data-v-46d8869c] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-46d8869c] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-46d8869c] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-46d8869c]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-46d8869c] {
  margin-left: 4px
}

.dropdown-list li[data-v-46d8869c]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-46d8869c] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-46d8869c] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-46d8869c] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-46d8869c] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-46d8869c] {
  margin-top: 3px
}

.tool-item[data-v-4d7ab355] {
  cursor: pointer
}

.tool-item .item[data-v-4d7ab355] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-4d7ab355],
.tool-item .item[data-v-4d7ab355]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-4d7ab355] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-4d7ab355] {
  word-break: keep-all
}

.tool-title[data-v-4d7ab355] {
  border-radius: 0
}

.tool-title .title[data-v-4d7ab355] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-4d7ab355] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-4d7ab355] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-4d7ab355]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-4d7ab355] {
  margin-left: 4px
}

.dropdown-list li[data-v-4d7ab355]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-4d7ab355] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-4d7ab355] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-4d7ab355] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-4d7ab355] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-4d7ab355] {
  margin-top: 3px
}

.tool-bar[data-v-59157854] {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.tool-bar.disabled[data-v-59157854] {
  pointer-events: none;
  opacity: .3
}

.tool-bar .section[data-v-59157854] {
  flex-shrink: 0;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 10px 0;
  margin: 0 15px;
  box-sizing: border-box
}

.tool-bar .section .row[data-v-59157854] {
  display: flex
}

.tool-bar .section[data-v-59157854]:after {
  content: "";
  position: absolute;
  background: #eaeef1;
  top: 50%;
  transform: translateY(-50%);
  left: -15px;
  height: 80%;
  width: 1px
}

.tool-bar .section .combox[data-v-59157854] {
  display: flex;
  border: 1px solid #cacaca;
  width: 100%
}

.tool-bar .section .combox .item[data-v-59157854] {
  flex: 1;
  border-right: 1px solid #cacaca
}

.tool-bar .section .combox .item[data-v-59157854]:last-child {
  border-right: none
}

.tool-bar .section .column[data-v-59157854] {
  flex-direction: column
}

.tool-bar .section .icon-group[data-v-59157854] {
  display: flex;
  justify-content: space-between
}

.tool-bar .section .icon-group .item[data-v-59157854] {
  cursor: pointer;
  margin-right: 5px;
  display: flex;
  align-items: center
}

.tool-bar .section .icon-group .item.disabled[data-v-59157854] {
  opacity: .3;
  pointer-events: none
}

.tool-bar .section .icon-group .item.large[data-v-59157854] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center
}

.tool-bar .section .icon-group .active[data-v-59157854] {
  background-color: rgba(0, 0, 0, .08)
}

.flex-row[data-v-59157854] {
  flex-direction: row !important
}

.first-section[data-v-59157854] {
  position: sticky !important;
  left: 0;
  top: var(--top-tab-height);
  background-color: #fff;
  z-index: 10
}

.top-box[data-v-7b7dd3a5] {
  flex-shrink: 0;
  height: 80px;
  padding-left: 30px;
  background-color: #fff;
  box-shadow: 0 8px 6px 0 rgba(0, 0, 0, .04)
}

.top-box .row[data-v-7b7dd3a5] {
  padding: 15px 0 5px;
  font-size: 16px
}

.top-box .tabs[data-v-7b7dd3a5] {
  padding-right: 30px;
  color: #666;
  display: flex;
  justify-content: space-between;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.top-box .tabs .item[data-v-7b7dd3a5] {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding-bottom: 2px;
  cursor: pointer
}

.top-box .tabs .item.on[data-v-7b7dd3a5] {
  background-color: #f2f2f2;
  border-radius: 2px;
  color: #f86e04;
  border-bottom: 2px solid #f86e04;
  padding-bottom: 0
}

.h-select-box[data-v-173d4ca8] {
  position: relative;
  width: 360px
}

.cell[data-v-173d4ca8] {
  width: 100%;
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  border-radius: 2px;
  padding: 0 10px 0 15px;
  display: inline-flex;
  align-items: center
}

.cell .inp[data-v-173d4ca8] {
  flex: 1;
  cursor: pointer;
  color: #606266
}

.cell .arrows[data-v-173d4ca8] {
  flex-shrink: 0;
  color: #c0c4cc;
  transition: all .3s
}

.cell .arrows.on[data-v-173d4ca8] {
  transform: rotate(-180deg)
}

.pull-down[data-v-173d4ca8] {
  width: 360px;
  padding-top: 10px;
  line-height: 0
}

.pull-down .item[data-v-173d4ca8] {
  display: inline-block;
  padding: 0 10px;
  min-width: 60px;
  height: 26px;
  line-height: 26px;
  box-sizing: border-box;
  background-color: #ebeef5;
  border-radius: 4px;
  margin: 0 0 10px 10px;
  text-align: center;
  cursor: pointer;
  color: grey
}

.pull-down .item .inp[data-v-173d4ca8] {
  border-radius: 2px;
  background-color: #fff;
  box-sizing: border-box;
  padding: 0 5px;
  width: 100px
}

.pull-down .item .icon[data-v-173d4ca8] {
  color: #ff7926;
  margin-left: 5px;
  transition: all .3s
}

.pull-down .item .icon[data-v-173d4ca8]:hover {
  transform: scale(1.2) translateY(-5%)
}

.pull-down .item.on[data-v-173d4ca8] {
  background-color: #fdf1e7;
  color: #f86e04;
  box-shadow: inset 0 0 0 1px #f86e04
}

.pull-down .add-btn[data-v-173d4ca8] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 88px;
  height: 22px;
  border-radius: 4px;
  border: 1px dashed #ff7926;
  color: #ff7926;
  margin: 0 0 10px 10px;
  cursor: pointer
}

[data-v-38092d62] .el-form .el-input {
  width: 360px
}

[data-v-38092d62] .el-dialog__body {
  padding-bottom: 20px
}

[data-v-38092d62] .el-dialog__footer {
  padding-top: 30px;
  border-top: 1px dashed #e5e5e5
}

.up-pic[data-v-38092d62] {
  box-sizing: border-box;
  width: 110px;
  height: 110px;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer
}

.up-pic .txt[data-v-38092d62] {
  color: grey
}

.h-page[data-v-22662402] {
  height: 100%
}

.list[data-v-22662402] {
  height: calc(100% - 120px);
  overflow-x: hidden
}

.list .section[data-v-22662402] {
  display: flex;
  margin-bottom: 25px
}

.list .section[data-v-22662402]:first-child {
  margin-top: 30px
}

.list .section.on .litimg[data-v-22662402] {
  border: 1px solid #ff7926;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .16)
}

.list .section.on .tool[data-v-22662402] {
  display: flex
}

.list .section .current[data-v-22662402] {
  width: 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  font-size: 16px
}

.list .section .litimg[data-v-22662402] {
  flex-shrink: 0;
  box-sizing: border-box;
  border: 1px solid #dcdfe5;
  overflow: hidden
}

.list .section .tool[data-v-22662402] {
  flex-shrink: 0;
  width: 30px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .16);
  display: none;
  align-items: center;
  flex-direction: column;
  justify-content: space-around;
  margin-left: 5px
}

.list .section .tool .tool-item[data-v-22662402] {
  cursor: pointer
}

.page[data-v-22662402] {
  transform-origin: left top;
  background-color: #fff;
  pointer-events: none;
  background: #fff;
  box-sizing: border-box
}

.page .pag-main[data-v-22662402] {
  display: flex;
  box-sizing: border-box
}

.page .page-head[data-v-22662402] {
  border-right: 1px solid #000;
  box-sizing: border-box;
  padding: 2pt
}

.page .page-head .title[data-v-22662402] {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  position: relative;
  box-sizing: border-box;
  border: 1px dashed #000
}

.page .page-head .title .txt[data-v-22662402] {
  font-size: 32px;
  font-family: å®‹ä½“;
  text-align: center;
  padding: 20px 0;
  width: 32px;
  word-break: break-word
}

.page .page-head .title .block[data-v-22662402] {
  width: 100%;
  height: 20px;
  background: #000
}

.page .page-head .title .page-no[data-v-22662402] {
  font-size: 20px
}

.page .page-head .title .clan-title[data-v-22662402] {
  font-weight: 700
}

.page .page-head .title .tips[data-v-22662402] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center
}

.page .page-content[data-v-22662402] {
  box-sizing: border-box;
  position: relative
}

.page .page-content .item-container[data-v-22662402] {
  box-sizing: border-box;
  position: relative;
  height: 100%;
  width: 100%
}

.tool2[data-v-22662402] {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 -24px 24px 0 hsla(0, 0%, 42%, .05);
  z-index: 10;
  cursor: pointer
}

[data-v-22662402] .vdr {
  border-color: transparent !important
}

.vdr .handle {
  display: none !important
}

.h-page[data-v-e9822bbc] {
  height: 100%
}

.list[data-v-e9822bbc] {
  height: calc(100% - 120px);
  overflow-x: hidden
}

.list .box[data-v-e9822bbc] {
  border-bottom: 1px solid #f1f3f6
}

.list .box[data-v-e9822bbc]:first-child {
  margin-top: 30px
}

.list .box.fold .title-bar[data-v-e9822bbc] {
  color: #ff7926
}

.list .box.fold .title-bar .arrow[data-v-e9822bbc] {
  transform: rotate(-180deg)
}

.list .box .title-bar[data-v-e9822bbc] {
  height: 40px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
  cursor: pointer
}

.list .box .title-bar .left-title[data-v-e9822bbc] {
  flex: 1;
  width: 0
}

.list .box .title-bar .icon[data-v-e9822bbc] {
  transition: all .3s;
  cursor: pointer;
  margin-left: 10px
}

.list .box .sub-box[data-v-e9822bbc] {
  flex-wrap: wrap;
  margin-bottom: 10px;
  display: flex
}

.list .box .sub-box .sub-item[data-v-e9822bbc] {
  margin-left: 6px;
  width: 110px;
  display: inline-flex;
  align-items: center;
  flex-direction: column;
  color: #999;
  cursor: pointer
}

.list .box .sub-box .sub-item.on[data-v-e9822bbc] {
  color: #ff7926
}

.list .box .sub-box .sub-item.on .pic[data-v-e9822bbc]:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 0 2px #ff7926
}

.list .box .sub-box .sub-item .pic[data-v-e9822bbc] {
  position: relative;
  width: 100%;
  height: 150px;
  box-sizing: border-box;
  overflow: hidden
}

.list .box .sub-box .sub-item .pic .img[data-v-e9822bbc] {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover
}

.list .box .sub-box .sub-item .txt[data-v-e9822bbc] {
  line-height: 33px
}

.tool[data-v-e9822bbc] {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -24px 24px 0 hsla(0, 0%, 42%, .05);
  z-index: 10
}

.tool .item[data-v-e9822bbc] {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  cursor: pointer
}

.tool .item[data-v-e9822bbc]:first-child:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #e6e6e6
}

.el-button[data-v-e9822bbc] {
  width: 120px
}

.list[data-v-668887e5] {
  height: calc(100vh - 151px);
  overflow-x: hidden
}

.list .box[data-v-668887e5] {
  border-bottom: 1px solid #f1f3f6
}

.list .box[data-v-668887e5]:first-child {
  margin-top: 30px
}

.list .box.fold .title-bar[data-v-668887e5] {
  color: #ff7926
}

.list .box.fold .title-bar .h-icon-gengduo[data-v-668887e5] {
  transform: rotate(-180deg)
}

.list .box.fold .sub-box[data-v-668887e5] {
  display: flex
}

.list .box .title-bar[data-v-668887e5] {
  height: 40px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
  cursor: pointer
}

.list .box .title-bar .left-title[data-v-668887e5] {
  flex: 1;
  width: 0
}

.list .box .title-bar .icon[data-v-668887e5] {
  transition: all .3s;
  cursor: pointer;
  margin-left: 10px
}

.list .box .sub-box[data-v-668887e5] {
  flex-wrap: wrap;
  margin-bottom: 10px;
  display: none
}

.list .box .sub-box .sub-item[data-v-668887e5] {
  margin-left: 6px;
  width: 110px;
  display: inline-flex;
  align-items: center;
  flex-direction: column;
  color: #999;
  cursor: pointer
}

.list .box .sub-box .sub-item.on[data-v-668887e5] {
  color: #ff7926
}

.list .box .sub-box .sub-item.on .pic[data-v-668887e5]:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 0 2px #ff7926
}

.list .box .sub-box .sub-item .pic[data-v-668887e5] {
  position: relative;
  width: 100px;
  height: 100px;
  box-sizing: border-box;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.list .box .sub-box .sub-item .pic .img[data-v-668887e5] {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover
}

.list .box .sub-box .sub-item .txt[data-v-668887e5] {
  line-height: 33px
}

.list .box .sub-box .up-item .pic[data-v-668887e5] {
  border: 1px dashed #ff7926;
  border-radius: 4px
}

.tool[data-v-668887e5] {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -24px 24px 0 hsla(0, 0%, 42%, .05);
  z-index: 10
}

.tool .item[data-v-668887e5] {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  cursor: pointer
}

.tool .item[data-v-668887e5]:first-child:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #e6e6e6
}

.el-button[data-v-668887e5] {
  width: 120px
}

.h-page[data-v-f508efec] {
  height: 100%
}

.list[data-v-f508efec] {
  padding-top: 20px;
  box-sizing: border-box;
  height: calc(100% - 80px);
  overflow-x: hidden
}

.list .item[data-v-f508efec] {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border-bottom: 1px solid #f1f3f6;
  height: 40px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
  cursor: pointer
}

.list .item .left-title[data-v-f508efec] {
  flex: 1;
  width: 0;
  margin-left: 8px
}

.list .item .icon[data-v-f508efec] {
  cursor: pointer;
  margin-left: 10px
}

.list .item[data-v-f508efec]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.list .item .icon[data-v-f508efec]:hover,
.list .item.on[data-v-f508efec] {
  color: #ff7926
}

.tool[data-v-f508efec] {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -24px 24px 0 hsla(0, 0%, 42%, .05);
  z-index: 10
}

.tool .item[data-v-f508efec] {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  cursor: pointer
}

.tool .item[data-v-f508efec]:first-child:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #e6e6e6
}

.h-line-select[data-v-6292a264] {
  position: relative
}

.line[data-v-6292a264] {
  width: 80%;
  border: none;
  background: none;
  border-top-style: solid
}

.title[data-v-6292a264] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  cursor: pointer
}

.title .left-bar[data-v-6292a264] {
  flex: 1;
  background-color: #f2f2f2;
  border-radius: 4px
}

.title .arrows[data-v-6292a264],
.title .left-bar[data-v-6292a264] {
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.title .arrows[data-v-6292a264] {
  flex-shrink: 0;
  width: 20px;
  transition: all .3s
}

.title .arrows.on[data-v-6292a264] {
  transform: rotate(-180deg)
}

.line-style[data-v-6292a264] {
  width: 100px
}

.line-style .line[data-v-6292a264] {
  border: none;
  background: none;
  width: 100%;
  margin: 8px 0
}

.line-width-item[data-v-6292a264] {
  width: 50px
}

.h-line-select[data-v-23ea0a40] {
  position: relative
}

.line[data-v-23ea0a40] {
  width: 80%;
  border: none;
  background: none;
  border-top-style: solid
}

.title[data-v-23ea0a40] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  cursor: pointer
}

.title .left-bar[data-v-23ea0a40] {
  flex: 1;
  background-color: #f2f2f2;
  border-radius: 4px
}

.title .arrows[data-v-23ea0a40],
.title .left-bar[data-v-23ea0a40] {
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.title .arrows[data-v-23ea0a40] {
  flex-shrink: 0;
  width: 20px;
  transition: all .3s
}

.title .arrows.on[data-v-23ea0a40] {
  transform: rotate(-180deg)
}

.line-style[data-v-23ea0a40] {
  width: 100px
}

.line-style .line[data-v-23ea0a40] {
  border: none;
  background: none;
  width: 100%;
  margin: 8px 0
}

.line-width-item[data-v-23ea0a40] {
  width: 50px
}

.empty-picker[data-v-3bf9a2c0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-3bf9a2c0] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-3bf9a2c0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.color-picker[data-v-3bf9a2c0] {
  position: relative
}

.color-picker .color-row[data-v-3bf9a2c0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-3bf9a2c0] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-3bf9a2c0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

::deep .input-number-box[data-v-209a265c] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-209a265c] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-209a265c] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-209a265c] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-209a265c],
::deep .el-input-number--small .el-input-number__increase[data-v-209a265c] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-209a265c] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-209a265c] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-209a265c] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-209a265c] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-209a265c],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-209a265c] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-209a265c] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-209a265c]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-209a265c] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-209a265c] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-209a265c] {
  border: 0
}

::deep .el-input__suffix[data-v-209a265c] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-209a265c] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-209a265c] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-209a265c] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-209a265c] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-209a265c] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-209a265c] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-209a265c] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-209a265c] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-209a265c] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-209a265c] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-209a265c] {
  position: relative
}

.color-picker .color-row[data-v-209a265c] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-209a265c] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-209a265c] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-209a265c] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-209a265c] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-209a265c] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-209a265c] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-209a265c] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-209a265c] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-209a265c] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-209a265c] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-209a265c] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-209a265c] {
  flex: 1
}

::deep .input-number-box[data-v-4e679bd0] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-4e679bd0] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-4e679bd0] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-4e679bd0] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-4e679bd0],
::deep .el-input-number--small .el-input-number__increase[data-v-4e679bd0] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-4e679bd0] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-4e679bd0] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-4e679bd0] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-4e679bd0] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-4e679bd0],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-4e679bd0] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-4e679bd0] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-4e679bd0]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-4e679bd0] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-4e679bd0] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-4e679bd0] {
  border: 0
}

::deep .el-input__suffix[data-v-4e679bd0] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-4e679bd0] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-4e679bd0] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-4e679bd0] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-4e679bd0] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-4e679bd0] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-4e679bd0] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-4e679bd0] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-4e679bd0] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-4e679bd0] {
  position: relative
}

.color-picker .color-row[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-4e679bd0] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-4e679bd0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-4e679bd0] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-4e679bd0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-4e679bd0] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-4e679bd0] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-4e679bd0] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-4e679bd0] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-4e679bd0] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-4e679bd0] {
  flex: 1
}

::deep .input-number-box[data-v-68919dc0] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-68919dc0] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-68919dc0] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-68919dc0] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-68919dc0],
::deep .el-input-number--small .el-input-number__increase[data-v-68919dc0] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-68919dc0] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-68919dc0] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-68919dc0] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-68919dc0] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-68919dc0],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-68919dc0] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-68919dc0] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-68919dc0]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-68919dc0] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-68919dc0] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-68919dc0] {
  border: 0
}

::deep .el-input__suffix[data-v-68919dc0] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-68919dc0] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-68919dc0] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-68919dc0] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-68919dc0] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-68919dc0] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-68919dc0] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-68919dc0] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-68919dc0] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-68919dc0] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-68919dc0] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-68919dc0] {
  position: relative
}

.color-picker .color-row[data-v-68919dc0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-68919dc0] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-68919dc0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-68919dc0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-68919dc0] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-68919dc0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-68919dc0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-68919dc0] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-68919dc0] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-68919dc0] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-68919dc0] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-68919dc0] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-68919dc0] {
  flex: 1
}

::deep .input-number-box[data-v-7caac130] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-7caac130] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-7caac130] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-7caac130] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-7caac130],
::deep .el-input-number--small .el-input-number__increase[data-v-7caac130] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-7caac130] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-7caac130] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-7caac130] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-7caac130] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-7caac130],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-7caac130] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-7caac130] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-7caac130]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-7caac130] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-7caac130] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-7caac130] {
  border: 0
}

::deep .el-input__suffix[data-v-7caac130] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-7caac130] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-7caac130] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-7caac130] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-7caac130] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-7caac130] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-7caac130] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-7caac130] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-7caac130] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-7caac130] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-7caac130] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-7caac130] {
  position: relative
}

.color-picker .color-row[data-v-7caac130] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-7caac130] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-7caac130] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-7caac130] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-7caac130] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-7caac130] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-7caac130] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-7caac130] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-7caac130] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-7caac130] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-7caac130] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-7caac130] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-7caac130] {
  flex: 1
}

::deep .input-number-box[data-v-0a0658c3] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-0a0658c3] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-0a0658c3] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-0a0658c3] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-0a0658c3],
::deep .el-input-number--small .el-input-number__increase[data-v-0a0658c3] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-0a0658c3] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-0a0658c3] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-0a0658c3] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-0a0658c3] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-0a0658c3],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-0a0658c3] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-0a0658c3] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-0a0658c3]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-0a0658c3] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-0a0658c3] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-0a0658c3] {
  border: 0
}

::deep .el-input__suffix[data-v-0a0658c3] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-0a0658c3] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-0a0658c3] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-0a0658c3] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-0a0658c3] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-0a0658c3] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-0a0658c3] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-0a0658c3] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-0a0658c3] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-0a0658c3] {
  position: relative
}

.color-picker .color-row[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-0a0658c3] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-0a0658c3] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-0a0658c3] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-0a0658c3] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-0a0658c3] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-0a0658c3] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-0a0658c3] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-0a0658c3] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-0a0658c3] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-0a0658c3] {
  flex: 1
}

::deep .input-number-box[data-v-19ddd330] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-19ddd330] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-19ddd330] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-19ddd330] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-19ddd330],
::deep .el-input-number--small .el-input-number__increase[data-v-19ddd330] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-19ddd330] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-19ddd330] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-19ddd330] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-19ddd330] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-19ddd330],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-19ddd330] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-19ddd330] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-19ddd330]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-19ddd330] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-19ddd330] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-19ddd330] {
  border: 0
}

::deep .el-input__suffix[data-v-19ddd330] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-19ddd330] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-19ddd330] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-19ddd330] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-19ddd330] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-19ddd330] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-19ddd330] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-19ddd330] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-19ddd330] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-19ddd330] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-19ddd330] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-19ddd330] {
  position: relative
}

.color-picker .color-row[data-v-19ddd330] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-19ddd330] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-19ddd330] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-19ddd330] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-19ddd330] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-19ddd330] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-19ddd330] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-19ddd330] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-19ddd330] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-19ddd330] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-19ddd330] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-19ddd330] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-19ddd330] {
  flex: 1
}

.pw-right-all[data-v-d44e5bba] {
  flex-shrink: 0;
  display: flex;
  height: 100%
}

.pw-right-box[data-v-d44e5bba] {
  height: 100%
}

.pw-right-bar[data-v-d44e5bba] {
  flex-shrink: 0;
  width: 60px;
  height: 100%;
  border-left: 1px solid #ebeef5;
  color: #5b5b5b;
  background-color: #fff;
  z-index: 2
}

.pw-right-bar .title[data-v-d44e5bba] {
  text-align: center;
  height: 40px;
  line-height: 40px;
  background-color: #f7f7f7
}

.pw-right-bar .item[data-v-d44e5bba] {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 15px 0;
  cursor: pointer;
  margin: 0 4px;
  box-sizing: border-box
}

.pw-right-bar .item[data-v-d44e5bba]:last-child {
  border-top: 1px solid #e6e6e6
}

.pw-right-bar .item.on .txt[data-v-d44e5bba] {
  color: #ff7926
}

.mapped[data-v-d44e5bba] {
  color: #3390ff
}

.mapping-name[data-v-d44e5bba] {
  color: #f86e04
}

.pw-status-bar[data-v-bc15e642] {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
  background-color: #fff;
  height: 40px;
  padding: 4px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  box-shadow: 0 0 24px 0 hsla(0, 0%, 42%, .1);
  text-align: center;
  color: #666;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  transition: all .4s ease
}

.pw-status-bar .left-bar[data-v-bc15e642] {
  display: flex;
  overflow: hidden;
  flex-shrink: 0
}

.pw-status-bar .left-bar .item[data-v-bc15e642] {
  width: 110px;
  min-width: 70px;
  border-right: 1px solid #e6e6e6
}

.pw-status-bar .right-bar[data-v-bc15e642] {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  overflow: hidden
}

.pw-status-bar .right-bar .item[data-v-bc15e642] {
  display: flex;
  align-items: center;
  width: 110px;
  cursor: pointer
}

.pw-status-bar .right-bar .item .txt[data-v-bc15e642] {
  margin-left: 10px
}

.pw-status-bar .right-bar .item[data-v-bc15e642]:hover {
  color: #ff7926
}

.pw-status-bar .scale[data-v-bc15e642] {
  border-left: 1px solid #e6e6e6;
  display: flex;
  align-items: center
}

.pw-status-bar .scale .percent[data-v-bc15e642] {
  padding: 0 10px;
  width: 70px;
  box-sizing: border-box;
  text-align: right
}

.pw-status-bar .scale .slider-box[data-v-bc15e642] {
  width: 150px
}

.pw-status-bar .scale .h-svg-icon-box[data-v-bc15e642] {
  cursor: pointer;
  transition: all .2s
}

.pw-status-bar .scale .h-svg-icon-box[data-v-bc15e642]:hover {
  transform: scale(1.1);
  color: #ff7926
}

.pw-status-bar .scale>div[data-v-bc15e642] {
  margin: 0 2px
}

@media screen and (max-width: 1400px) {
  [data-v-bc15e642] .pw-status-bar .left-bar .item {
    width: 100px !important
  }

  [data-v-bc15e642] .pw-status-bar .right-bar .scale .slider-box {
    width: 120px !important
  }

  [data-v-bc15e642] .el-pagination .el-pagination__jump {
    margin-left: 0
  }
}

.cropper[data-v-6f603704] {
  height: 480px
}

.pointer-events {
  opacity: .3
}

.disabled__el {
  pointer-events: none;
  opacity: .3
}

.disabled__el.no-opacity {
  opacity: 1
}

.el-message.offset {
  right: 250px;
  min-width: 100px;
  left: auto
}

.flex[data-v-1ce89d73] {
  display: flex
}

.flex-row[data-v-1ce89d73] {
  flex-direction: row
}

.flex-column[data-v-1ce89d73] {
  flex-direction: column
}

.flex-center[data-v-1ce89d73] {
  align-items: center
}

.flex1[data-v-1ce89d73] {
  flex: 1
}

.pointer-events[data-v-1ce89d73] {
  pointer-events: none
}

.disabled__el[data-v-1ce89d73] {
  pointer-events: none;
  opacity: .3
}

.disabled__el.no-opacity[data-v-1ce89d73] {
  opacity: 1
}

[data-v-1ce89d73] .slider-box .el-slider__runway {
  height: 2px !important
}

[data-v-1ce89d73] .slider-box .el-slider__bar {
  height: 2px !important;
  background-color: #787878
}

[data-v-1ce89d73] .slider-box .el-slider__button-wrapper {
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px
}

[data-v-1ce89d73] .slider-box .el-slider__button {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  box-shadow: 0 0 3px 1px hsla(0, 0%, 42%, .17)
}

[data-v-1ce89d73] .slider-box .el-slider__button-wrapper .el-tooltip,
[data-v-1ce89d73] .slider-box .el-slider__button-wrapper:after {
  display: block
}

[data-v-1ce89d73] .toggle-left-side .inner {
  box-sizing: content-box
}

.pw-container[data-v-1ce89d73] {
  height: 100vh;
  display: flex;
  background: #fff;
  overflow: hidden;
  position: relative
}

.pw-container .pw-side[data-v-1ce89d73] {
  padding-top: 4px
}

.pw-container .pw-body[data-v-1ce89d73] {
  width: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #ebeef5
}

.pw-container .pw-body .pw-content[data-v-1ce89d73] {
  flex-grow: 1;
  display: flex;
  margin-top: 15px;
  height: calc(100vh - 81px);
  box-sizing: border-box;
  overflow: hidden
}

.pw-container .pw-body .pw-content .content-left[data-v-1ce89d73] {
  flex-grow: 1;
  display: flex;
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 40px;
  position: relative
}

.pw-container .pw-body .pw-content .content-left .content-left-top[data-v-1ce89d73] {
  flex-grow: 1;
  display: flex
}

.pw-container .pw-body .pw-content .content-left .status-bar[data-v-1ce89d73] {
  flex-shrink: 0;
  overflow: hidden
}

.pw-container .pw-body .pw-content .content-right[data-v-1ce89d73] {
  flex-shrink: 0;
  height: 100%;
  padding-left: 10px
}

.pw-container .pw-tool-bar[data-v-1ce89d73] {
  flex-shrink: 0;
  position: relative;
  height: 80px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
  overflow-y: hidden;
  background-color: #fff
}

.pw-container .pw-center[data-v-1ce89d73] {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 0;
  overflow: auto
}

.pw-container .pw-center .pw-main[data-v-1ce89d73] {
  background: #ebeef5;
  overflow: auto
}

.pw-container .pw-center .pw-main.pointer-events[data-v-1ce89d73] {
  opacity: 1
}

.pw-container .pw-center .pw-main[data-v-1ce89d73]::-webkit-scrollbar {
  display: none
}

.pw-container .pw-center .pw-main .paging-tools[data-v-1ce89d73] {
  text-align: center;
  padding: 20px
}

.pw-container .pw-center .pw-main .page[data-v-1ce89d73] {
  margin: 0 auto;
  margin-bottom: 10px;
  box-shadow: 0 7px 21px 0 rgba(0, 0, 0, .12);
  background: #fff;
  transform-origin: 50% 0;
  box-sizing: border-box
}

.pw-container .pw-center .pw-main .page .pag-main[data-v-1ce89d73] {
  display: flex;
  border: 1px solid #000;
  height: 100%
}

.pw-container .pw-center .pw-main .page .page-head[data-v-1ce89d73] {
  border-right: 1px solid #000;
  box-sizing: border-box;
  padding: 2pt
}

.pw-container .pw-center .pw-main .page .page-head .title[data-v-1ce89d73] {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  position: relative;
  box-sizing: border-box;
  border: 1px dashed #000
}

.pw-container .pw-center .pw-main .page .page-head .title .txt[data-v-1ce89d73] {
  font-size: 32px;
  font-family: å®‹ä½“;
  text-align: center;
  padding: 20px 0;
  width: 32px;
  word-break: break-word
}

.pw-container .pw-center .pw-main .page .page-head .title .block[data-v-1ce89d73] {
  width: 100%;
  height: 20px;
  background: #000
}

.pw-container .pw-center .pw-main .page .page-head .title .page-no[data-v-1ce89d73] {
  font-size: 20px
}

.pw-container .pw-center .pw-main .page .page-head .title .clan-title[data-v-1ce89d73] {
  font-weight: 700
}

.pw-container .pw-center .pw-main .page .page-head .title .tips[data-v-1ce89d73] {
  position: relative;
  flex: 1;
  display: flex;
  writing-mode: vertical-rl;
  transform-origin: 50% 0;
  word-break: keep-all;
  height: 0;
  overflow: hidden
}

.pw-container .pw-center .pw-main .page .page-head .title .tips .txt-in[data-v-1ce89d73] {
  position: absolute;
  left: 50%;
  top: 20px;
  transform-origin: center top;
  white-space: nowrap
}

.pw-container .pw-center .pw-main .page .page-content[data-v-1ce89d73] {
  box-sizing: border-box;
  position: relative;
  flex-shrink: 0
}

.pw-container .pw-center .pw-main .page .page-content .item-container[data-v-1ce89d73] {
  box-sizing: border-box;
  position: relative;
  height: 100%;
  width: 100%
}

.pw-container .pw-right[data-v-1ce89d73] {
  flex-shrink: 0
}

.select-box[data-v-1ce89d73] {
  position: absolute;
  z-index: -1;
  border: 1px dashed #aeb5c0 !important
}

.select-box-tools[data-v-1ce89d73] {
  width: 21px;
  position: absolute;
  z-index: 999;
  padding: 2px;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e3e3e3;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .12);
  color: rgba(0, 0, 0, .8);
  right: -30px;
  top: 0
}

.select-box-tools i[data-v-1ce89d73] {
  display: block;
  font-size: 21px;
  margin: 5px 0;
  cursor: pointer
}

.select-box-tools i[data-v-1ce89d73]:hover {
  background: rgba(0, 0, 0, .04)
}

#test-mask-pw[data-v-1ce89d73] {
  position: fixed;
  width: 200px;
  height: 200px;
  right: 400px;
  top: 200px;
  background-color: rgba(255, 0, 0, .5);
  pointer-events: none
}

.lastJustify div:last-child {
  -moz-text-align-last: justify !important;
  text-align-last: justify !important
}

.text-container[data-v-2bb504ce] {
  display: flex;
  height: 100%;
  width: 100%;
  cursor: move;
  position: relative
}

.text-container .line[data-v-2bb504ce] {
  position: absolute;
  width: 100%;
  border-top: 1px solid #000;
  top: 24px
}

.edit-text[data-v-2bb504ce] {
  outline: none;
  min-width: 10px;
  min-height: 10px;
  flex: 1;
  cursor: text
}

.placeholder[data-v-2bb504ce] {
  color: #d7d7d7 !important
}

.svg[data-v-4d6d7ab6] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-4d6d7ab6] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-4d6d7ab6] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-4d6d7ab6] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.svg[data-v-30b67dde] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-30b67dde] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-30b67dde] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-30b67dde] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.svg[data-v-04ce3935] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-04ce3935] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-04ce3935] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-04ce3935] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2;
  left: 50%
}

.svg[data-v-696254ec] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-696254ec] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-696254ec] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-696254ec] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2;
  right: 50%
}

.svg[data-v-ba483f52] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-ba483f52] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-ba483f52] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-ba483f52] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.svg[data-v-6da076a5] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-6da076a5] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-6da076a5] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-6da076a5] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2;
  left: 3%;
  right: 3%
}

.svg[data-v-79e0187c] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-79e0187c] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-79e0187c] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-79e0187c] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.cursor-nwse-resize[data-v-79e0187c] {
  top: 100%
}

.svg[data-v-d3e9a402] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-d3e9a402] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-d3e9a402] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-d3e9a402] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.svg[data-v-6ec3658c] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-6ec3658c] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-6ec3658c] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-6ec3658c] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.svg[data-v-05858f3a] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-05858f3a] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-05858f3a] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-05858f3a] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2;
  right: auto
}

.h-image[data-v-6caf1e4a] {
  height: 100%;
  width: 100%;
  image-rendering: -webkit-optimize-contrast
}

.pw-h-line[data-v-222187b4] {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  cursor: move
}

.svg[data-v-b0c00794] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-b0c00794] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-b0c00794] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-b0c00794] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.h-rectangle[data-v-e601d7ee] {
  height: 100%;
  width: 100%;
  box-sizing: border-box
}

.h-rectangle.img-box[data-v-e601d7ee] {
  overflow: hidden
}

.h-rectangle img[data-v-e601d7ee],
.svg[data-v-e8cb1800] {
  height: 100%;
  width: 100%
}

.svg[data-v-e8cb1800] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible
}

.svg .editor-cursor-shape[data-v-e8cb1800] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-e8cb1800] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-e8cb1800] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.svg[data-v-5bcfd538] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-5bcfd538] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-5bcfd538] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-5bcfd538] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.table[data-v-271bf1b6] {
  position: absolute;
  border: 1px dashed transparent
}

.table.border[data-v-271bf1b6] {
  border: 1px dashed #ff6200
}

.table .tools[data-v-271bf1b6] {
  position: absolute;
  padding: 8px;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e3e3e3;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .12);
  color: rgba(0, 0, 0, .8);
  top: -60px;
  display: flex;
  cursor: pointer
}

.table .tools .item[data-v-271bf1b6] {
  margin: 0 8px;
  display: flex;
  align-items: center
}

.table .cell[data-v-271bf1b6] {
  position: absolute;
  height: 100%;
  width: 100%
}

.table .cell.selected[data-v-271bf1b6] {
  background: hsla(0, 0%, 80%, .6)
}

.table .cell .content[data-v-271bf1b6] {
  height: calc(100% - 6px);
  width: calc(100% - 6px);
  overflow: hidden;
  margin: 3px;
  outline: none;
  cursor: text;
  box-sizing: border-box
}

.table .cell .line[data-v-271bf1b6] {
  position: absolute
}

.table .cell .line .l[data-v-271bf1b6] {
  position: absolute;
  z-index: 90
}

.table .cell .top-line[data-v-271bf1b6] {
  top: 0
}

.table .cell .bottom-line[data-v-271bf1b6] {
  bottom: 0
}

.table .cell .left-line[data-v-271bf1b6] {
  left: 0
}

.table .cell .right-line[data-v-271bf1b6] {
  right: 0
}

.table .cell .bottom-line[data-v-271bf1b6],
.table .cell .top-line[data-v-271bf1b6] {
  width: 100%;
  left: 0;
  right: 0
}

.table .cell .bottom-line .l[data-v-271bf1b6],
.table .cell .top-line .l[data-v-271bf1b6] {
  height: 10px;
  width: 100%;
  cursor: row-resize;
  top: -5px
}

.table .cell .left-line[data-v-271bf1b6],
.table .cell .right-line[data-v-271bf1b6] {
  height: 100%;
  cursor: col-resize;
  top: 0;
  bottom: 0
}

.table .cell .left-line .l[data-v-271bf1b6],
.table .cell .right-line .l[data-v-271bf1b6] {
  height: 100%;
  width: 10px;
  cursor: col-resize;
  left: -5px
}

.text-style-select-tool[data-v-271bf1b6] {
  padding: 0
}

.dropdown-list li[data-v-271bf1b6]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-271bf1b6] {
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item .checked[data-v-271bf1b6] {
  position: absolute;
  left: 12px
}

.border-list[data-v-271bf1b6] {
  display: flex;
  width: 192px;
  flex-wrap: wrap
}

.border-list i[data-v-271bf1b6] {
  margin: 8px;
  cursor: pointer
}

.svg[data-v-51537770] {
  position: absolute;
  left: 0;
  top: 0;
  overflow: visible;
  width: 100%;
  height: 100%
}

.svg .editor-cursor-shape[data-v-51537770] {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  box-sizing: border-box
}

.cursor-nwse-resize[data-v-51537770] {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background-color: #f5a623;
  border: 1px solid #fff;
  cursor: nwse-resize;
  transform: rotate(45deg) translate3d(-80%, 0, 0);
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .32);
  z-index: 1000000
}

.editor-txt-box[data-v-51537770] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 7px 14px;
  margin: 0 auto;
  box-sizing: border-box;
  z-index: 2
}

.yl[data-v-6195c862] {
  position: relative;
  width: 100%;
  height: 100%
}

.yl .line[data-v-6195c862] {
  background: #000
}

.yl .item[data-v-6195c862] {
  position: absolute;
  display: flex;
  align-items: center;
  flex-direction: column
}

.yl .item .index[data-v-6195c862] {
  border: 1px solid #000;
  border-radius: 50%;
  font-size: 10px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center
}

.yl .item .line-v[data-v-6195c862] {
  width: 1px;
  position: absolute;
  height: 100%
}

.yl .item .line-v.lr[data-v-6195c862] {
  right: 0
}

.yl .item .line-v.rl[data-v-6195c862] {
  left: 0
}

.yl .item .line-h[data-v-6195c862] {
  bottom: 0;
  height: 1px;
  position: absolute
}

.yl .item .line-h.lr[data-v-6195c862] {
  right: 0
}

.yl .item .line-h.rl[data-v-6195c862] {
  left: 0
}

.yl .item .line-h-2[data-v-6195c862] {
  top: 0;
  height: 1px;
  position: absolute
}

.yl .item .line-h-2.lr[data-v-6195c862] {
  left: 0
}

.yl .item .line-h-2.rl[data-v-6195c862] {
  right: 0
}

.yl .item .content[data-v-6195c862] {
  display: flex;
  position: relative
}

.yl .item .content .name[data-v-6195c862] {
  width: 19px;
  overflow: hidden;
  position: relative
}

.yl .item .content .name i[data-v-6195c862] {
  position: absolute;
  display: none;
  cursor: pointer;
  color: #f86e04
}

.yl .item .content .name i.t[data-v-6195c862] {
  top: 0;
  left: 3px
}

.yl .item .content .name i.b[data-v-6195c862] {
  bottom: 0;
  left: 3px
}

.yl .item .content .name i.d[data-v-6195c862] {
  left: -5px;
  top: 39px
}

.yl .item .content .name:hover i[data-v-6195c862] {
  display: inline
}

.yl .item .content .intro[data-v-6195c862] {
  font-size: 10px;
  overflow: hidden;
  position: absolute;
  height: 126px;
  width: 40px;
  top: -5px
}

.yl .item .content .intro.lr[data-v-6195c862] {
  left: 18px
}

.yl .item .content .intro.rl[data-v-6195c862] {
  left: -39px
}

.yl .next-page-line[data-v-6195c862] {
  height: 1px;
  top: 0
}

.yl .next-page-line.rl[data-v-6195c862] {
  left: 0;
  position: absolute
}

.yl .next-page-line.lr[data-v-6195c862] {
  right: 0;
  position: absolute
}

.yl .pre-page-line[data-v-6195c862] {
  height: 1px;
  top: 0
}

.yl .pre-page-line.rl[data-v-6195c862] {
  right: 0;
  position: absolute
}

.yl .pre-page-line.lr[data-v-6195c862] {
  left: 0;
  position: absolute
}

.vdr[data-v-49241710] {
  touch-action: none;
  position: absolute;
  box-sizing: border-box;
  border: 1px dashed #d6d6d6
}

.handle[data-v-49241710] {
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  background: #fff;
  border: 1px solid #333;
  box-shadow: 0 0 2px #bbb
}

.nw-resize[data-v-49241710] {
  cursor: nw-resize
}

.n-resize[data-v-49241710] {
  cursor: n-resize
}

.ne-resize[data-v-49241710] {
  cursor: ne-resize
}

.w-resize[data-v-49241710] {
  cursor: w-resize
}

.e-resize[data-v-49241710] {
  cursor: e-resize
}

.sw-resize[data-v-49241710] {
  cursor: sw-resize
}

.s-resize[data-v-49241710] {
  cursor: s-resize
}

.se-resize[data-v-49241710] {
  cursor: se-resize
}

.handle-tl[data-v-49241710] {
  top: -5px;
  left: -5px;
  cursor: nw-resize
}

.handle-tm[data-v-49241710] {
  top: -5px;
  left: calc(50% - 4px);
  cursor: n-resize
}

.handle-tr[data-v-49241710] {
  top: -5px;
  right: -5px;
  cursor: ne-resize
}

.handle-ml[data-v-49241710] {
  top: calc(50% - 4px);
  left: -5px;
  cursor: w-resize
}

.handle-mr[data-v-49241710] {
  top: calc(50% - 4px);
  right: -5px;
  cursor: e-resize
}

.handle-bl[data-v-49241710] {
  bottom: -5px;
  left: -5px;
  cursor: sw-resize
}

.handle-bm[data-v-49241710] {
  bottom: -5px;
  left: calc(50% - 4px);
  cursor: s-resize
}

.handle-br[data-v-49241710] {
  bottom: -5px;
  right: -5px;
  cursor: se-resize
}

.ref-line[data-v-49241710] {
  position: absolute;
  background-color: #f0c;
  z-index: 9999
}

.v-line[data-v-49241710] {
  width: 1px
}

.h-line[data-v-49241710] {
  height: 1px
}

@media only screen and (max-width: 768px) {
  [class*=handle-][data-v-49241710]:before {
    content: "";
    left: -10px;
    right: -10px;
    bottom: -10px;
    top: -10px;
    position: absolute
  }
}

.rotate[data-v-49241710] {
  position: absolute;
  height: 15px;
  width: 15px;
  background: red;
  background-color: #ff6200;
  border-radius: 100%;
  top: -25px;
  left: calc(50% - 7px);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  cursor: pointer;
  cursor: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAKTWlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjanVN3WJP3Fj7f92UPVkLY8LGXbIEAIiOsCMgQWaIQkgBhhBASQMWFiApWFBURnEhVxILVCkidiOKgKLhnQYqIWotVXDjuH9yntX167+3t+9f7vOec5/zOec8PgBESJpHmomoAOVKFPDrYH49PSMTJvYACFUjgBCAQ5svCZwXFAADwA3l4fnSwP/wBr28AAgBw1S4kEsfh/4O6UCZXACCRAOAiEucLAZBSAMguVMgUAMgYALBTs2QKAJQAAGx5fEIiAKoNAOz0ST4FANipk9wXANiiHKkIAI0BAJkoRyQCQLsAYFWBUiwCwMIAoKxAIi4EwK4BgFm2MkcCgL0FAHaOWJAPQGAAgJlCLMwAIDgCAEMeE80DIEwDoDDSv+CpX3CFuEgBAMDLlc2XS9IzFLiV0Bp38vDg4iHiwmyxQmEXKRBmCeQinJebIxNI5wNMzgwAABr50cH+OD+Q5+bk4eZm52zv9MWi/mvwbyI+IfHf/ryMAgQAEE7P79pf5eXWA3DHAbB1v2upWwDaVgBo3/ldM9sJoFoK0Hr5i3k4/EAenqFQyDwdHAoLC+0lYqG9MOOLPv8z4W/gi372/EAe/tt68ABxmkCZrcCjg/1xYW52rlKO58sEQjFu9+cj/seFf/2OKdHiNLFcLBWK8ViJuFAiTcd5uVKRRCHJleIS6X8y8R+W/QmTdw0ArIZPwE62B7XLbMB+7gECiw5Y0nYAQH7zLYwaC5EAEGc0Mnn3AACTv/mPQCsBAM2XpOMAALzoGFyolBdMxggAAESggSqwQQcMwRSswA6cwR28wBcCYQZEQAwkwDwQQgbkgBwKoRiWQRlUwDrYBLWwAxqgEZrhELTBMTgN5+ASXIHrcBcGYBiewhi8hgkEQcgIE2EhOogRYo7YIs4IF5mOBCJhSDSSgKQg6YgUUSLFyHKkAqlCapFdSCPyLXIUOY1cQPqQ28ggMor8irxHMZSBslED1AJ1QLmoHxqKxqBz0XQ0D12AlqJr0Rq0Hj2AtqKn0UvodXQAfYqOY4DRMQ5mjNlhXIyHRWCJWBomxxZj5Vg1Vo81Yx1YN3YVG8CeYe8IJAKLgBPsCF6EEMJsgpCQR1hMWEOoJewjtBK6CFcJg4Qxwicik6hPtCV6EvnEeGI6sZBYRqwm7iEeIZ4lXicOE1+TSCQOyZLkTgohJZAySQtJa0jbSC2kU6Q+0hBpnEwm65Btyd7kCLKArCCXkbeQD5BPkvvJw+S3FDrFiOJMCaIkUqSUEko1ZT/lBKWfMkKZoKpRzame1AiqiDqfWkltoHZQL1OHqRM0dZolzZsWQ8ukLaPV0JppZ2n3aC/pdLoJ3YMeRZfQl9Jr6Afp5+mD9HcMDYYNg8dIYigZaxl7GacYtxkvmUymBdOXmchUMNcyG5lnmA+Yb1VYKvYqfBWRyhKVOpVWlX6V56pUVXNVP9V5qgtUq1UPq15WfaZGVbNQ46kJ1Bar1akdVbupNq7OUndSj1DPUV+jvl/9gvpjDbKGhUaghkijVGO3xhmNIRbGMmXxWELWclYD6yxrmE1iW7L57Ex2Bfsbdi97TFNDc6pmrGaRZp3mcc0BDsax4PA52ZxKziHODc57LQMtPy2x1mqtZq1+rTfaetq+2mLtcu0W7eva73VwnUCdLJ31Om0693UJuja6UbqFutt1z+o+02PreekJ9cr1Dund0Uf1bfSj9Rfq79bv0R83MDQINpAZbDE4Y/DMkGPoa5hpuNHwhOGoEctoupHEaKPRSaMnuCbuh2fjNXgXPmasbxxirDTeZdxrPGFiaTLbpMSkxeS+Kc2Ua5pmutG003TMzMgs3KzYrMnsjjnVnGueYb7ZvNv8jYWlRZzFSos2i8eW2pZ8ywWWTZb3rJhWPlZ5VvVW16xJ1lzrLOtt1ldsUBtXmwybOpvLtqitm63Edptt3xTiFI8p0in1U27aMez87ArsmuwG7Tn2YfYl9m32zx3MHBId1jt0O3xydHXMdmxwvOuk4TTDqcSpw+lXZxtnoXOd8zUXpkuQyxKXdpcXU22niqdun3rLleUa7rrStdP1o5u7m9yt2W3U3cw9xX2r+00umxvJXcM970H08PdY4nHM452nm6fC85DnL152Xlle+70eT7OcJp7WMG3I28Rb4L3Le2A6Pj1l+s7pAz7GPgKfep+Hvqa+It89viN+1n6Zfgf8nvs7+sv9j/i/4XnyFvFOBWABwQHlAb2BGoGzA2sDHwSZBKUHNQWNBbsGLww+FUIMCQ1ZH3KTb8AX8hv5YzPcZyya0RXKCJ0VWhv6MMwmTB7WEY6GzwjfEH5vpvlM6cy2CIjgR2yIuB9pGZkX+X0UKSoyqi7qUbRTdHF09yzWrORZ+2e9jvGPqYy5O9tqtnJ2Z6xqbFJsY+ybuIC4qriBeIf4RfGXEnQTJAntieTE2MQ9ieNzAudsmjOc5JpUlnRjruXcorkX5unOy553PFk1WZB8OIWYEpeyP+WDIEJQLxhP5aduTR0T8oSbhU9FvqKNolGxt7hKPJLmnVaV9jjdO31D+miGT0Z1xjMJT1IreZEZkrkj801WRNberM/ZcdktOZSclJyjUg1plrQr1zC3KLdPZisrkw3keeZtyhuTh8r35CP5c/PbFWyFTNGjtFKuUA4WTC+oK3hbGFt4uEi9SFrUM99m/ur5IwuCFny9kLBQuLCz2Lh4WfHgIr9FuxYji1MXdy4xXVK6ZHhp8NJ9y2jLspb9UOJYUlXyannc8o5Sg9KlpUMrglc0lamUycturvRauWMVYZVkVe9ql9VbVn8qF5VfrHCsqK74sEa45uJXTl/VfPV5bdra3kq3yu3rSOuk626s91m/r0q9akHV0IbwDa0b8Y3lG19tSt50oXpq9Y7NtM3KzQM1YTXtW8y2rNvyoTaj9nqdf13LVv2tq7e+2Sba1r/dd3vzDoMdFTve75TsvLUreFdrvUV99W7S7oLdjxpiG7q/5n7duEd3T8Wej3ulewf2Re/ranRvbNyvv7+yCW1SNo0eSDpw5ZuAb9qb7Zp3tXBaKg7CQeXBJ9+mfHvjUOihzsPcw83fmX+39QjrSHkr0jq/dawto22gPaG97+iMo50dXh1Hvrf/fu8x42N1xzWPV56gnSg98fnkgpPjp2Snnp1OPz3Umdx590z8mWtdUV29Z0PPnj8XdO5Mt1/3yfPe549d8Lxw9CL3Ytslt0utPa49R35w/eFIr1tv62X3y+1XPK509E3rO9Hv03/6asDVc9f41y5dn3m978bsG7duJt0cuCW69fh29u0XdwruTNxdeo94r/y+2v3qB/oP6n+0/rFlwG3g+GDAYM/DWQ/vDgmHnv6U/9OH4dJHzEfVI0YjjY+dHx8bDRq98mTOk+GnsqcTz8p+Vv9563Or59/94vtLz1j82PAL+YvPv655qfNy76uprzrHI8cfvM55PfGm/K3O233vuO+638e9H5ko/ED+UPPR+mPHp9BP9z7nfP78L/eE8/sl0p8zAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAHHSURBVHjarJTPSxtREMc/bw2FJRgh2ViI2ATqoQ0YrNQftNCLF7EnL4WCp4iEHnPvRY8iHrx5tIjgP2CFKnqUVGz9QQ5Vyl499AeyqSR083pwUrLrJujqwLK8N9/3mdmdN6O01tynRQCUUu00HcBDeZ8DNQDLskilUgPVanUFKAB7ALTJ8DWwC2jfswe8Ec2k7L3zZOizNPAReCrrE+Ar8Ff2hoF1YEYCAvz5f9qX4WhTJktANCCgARTl0xvat0HAx4ArghctfsNz4AuwA/xoAk4HAbfFOdamQCPAT+AXcAQcAp+BrAdomuZLga2FvS6xWIx8Pn8FTCaTywIcDAtMp9OUSiUMANd1X0mlDkLylOu6qlarXQGBXuDsDg2yABxGIpEnDeAlYN4BOAT0O47Tg9aaRCLR6IgHIYG/4/H4aS6XyxgA9Xp9SxwTIWCjQJdhGJ8cx7HRWqOUetTUp7e1DUCbpvnMsizPxZ4X6PtbwApy5kOrXi6JYPYGsKJov3kK6gN2Avsi/C5NH2/yd8nIOhbNKdDtCdNiHs75ZqADXPj2FgNvuBQlyNeZyWSmstnseKVS6QNUNBq1y+Xypm3bqzIkrtm/AQAKt6Tx4nc2+wAAAABJRU5ErkJggg==) 5 5, auto
}

.rotate[data-v-49241710]:after {
  content: "îœ";
  position: absolute;
  width: 100%;
  height: 100%;
  transform: scale(.7);
  color: #fff
}

.vdr {
  touch-action: none;
  border: 1px dashed #d6d6d6
}

.handle,
.vdr {
  position: absolute;
  box-sizing: border-box
}

.handle {
  width: 8px;
  height: 8px;
  background: #fff;
  border: 1px solid #333;
  box-shadow: 0 0 2px #bbb
}

.nw-resize {
  cursor: nw-resize
}

.n-resize {
  cursor: n-resize
}

.ne-resize {
  cursor: ne-resize
}

.w-resize {
  cursor: w-resize
}

.e-resize {
  cursor: e-resize
}

.sw-resize {
  cursor: sw-resize
}

.s-resize {
  cursor: s-resize
}

.se-resize {
  cursor: se-resize
}

.handle-tl {
  top: -5px;
  left: -5px;
  cursor: nw-resize
}

.handle-tm {
  top: -5px;
  left: calc(50% - 4px);
  cursor: n-resize
}

.handle-tr {
  top: -5px;
  right: -5px;
  cursor: ne-resize
}

.handle-ml {
  top: calc(50% - 4px);
  left: -5px;
  cursor: w-resize
}

.handle-mr {
  top: calc(50% - 4px);
  right: -5px;
  cursor: e-resize
}

.handle-bl {
  bottom: -5px;
  left: -5px;
  cursor: sw-resize
}

.handle-bm {
  bottom: -5px;
  left: calc(50% - 4px);
  cursor: s-resize
}

.handle-br {
  bottom: -5px;
  right: -5px;
  cursor: se-resize
}

.ref-line {
  position: absolute;
  background-color: #f0c;
  z-index: 9999
}

.v-line {
  width: 1px
}

.h-line {
  height: 1px
}

@media only screen and (max-width: 768px) {
  [class*=handle-]:before {
    content: "";
    left: -10px;
    right: -10px;
    bottom: -10px;
    top: -10px;
    position: absolute
  }
}

.rotate {
  position: absolute;
  height: 15px;
  width: 15px;
  background: red;
  background-color: #ff6200;
  border-radius: 100%;
  top: -25px;
  left: calc(50% - 7px);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  cursor: pointer;
  cursor: url(data:image/png;base64,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) 5 5, auto
}

.rotate:after {
  content: "îœ";
  position: absolute;
  width: 100%;
  height: 100%;
  transform: scale(.7);
  color: #fff
}

.vdr {
  cursor: move;
  border-color: transparent !important
}

.vdr .handle {
  border-color: #ff6200;
  z-index: 99
}

.vdr.active {
  border-color: #ff6200 !important
}

.vdr.active.no-border {
  border-color: transparent !important
}

.vdr.active .item-tools {
  display: block
}

.com-item-box {
  display: flex;
  height: 100%;
  width: 100%;
  box-sizing: border-box
}

.pointer-evetns {
  pointer-events: none
}

.lock {
  border-color: #ccc !important
}
</style>
<style scoped lang="less">
[data-v-05cce657] .el-dialog__body {
  padding-bottom: 20px
}

[data-v-05cce657] .el-dialog__footer {
  padding-top: 30px;
  border-top: 1px dashed #e5e5e5
}

[data-v-e89adbf8] .el-form .el-input {
  width: 360px
}

[data-v-e89adbf8] .el-dialog__body {
  padding-bottom: 20px
}

[data-v-e89adbf8] .el-dialog__footer {
  padding-top: 30px;
  border-top: 1px dashed #e5e5e5
}

.tools[data-v-eeac83fe] {
  flex-shrink: 0;
  position: relative;
  height: 80px;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 8px 6px 0 rgba(0, 0, 0, .04);
  z-index: 2;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 15px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.tools.pointer-events[data-v-eeac83fe] {
  pointer-events: none
}

.tools .add[data-v-eeac83fe] {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer
}

.tools .add .txt[data-v-eeac83fe] {
  padding-top: 5px
}

.tools .add:hover .txt[data-v-eeac83fe] {
  color: #ff7926
}

.tools .icon-box[data-v-eeac83fe] {
  display: flex;
  flex-wrap: wrap
}

.tools .icon-box .item[data-v-eeac83fe] {
  height: 30px;
  width: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer
}

.tools .icon-box .item[data-v-eeac83fe]:hover {
  background: #ebeef5
}

.catalog-li[data-v-1e542682] {
  width: 100%
}

.catalog-li .catalog-item[data-v-1e542682] {
  margin: 0
}

.catalog-li .catalog-item .title[data-v-1e542682] {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between
}

.catalog-li .catalog-item .title:hover>.op[data-v-1e542682] {
  display: flex
}

.catalog-li .catalog-item .title>.content[data-v-1e542682] {
  flex: 1
}

.catalog-li .catalog-item .title>.content.active .icon[data-v-1e542682],
.catalog-li .catalog-item .title>.content.active[data-v-1e542682] {
  color: #f86e04
}

.catalog-li .catalog-item .title>.op[data-v-1e542682] {
  display: none
}

.catalog-li .catalog-item .title>.op i[data-v-1e542682]:hover {
  color: #f86e04
}

.catalog-li .catalog-item .add-cell[data-v-1e542682] {
  margin: 0 10px;
  padding: 10px
}

.catalog-li .catalog-item .add-cell.add-puwen[data-v-1e542682] {
  background-color: #f5f5f5
}

.catalog-li .catalog-item .add-cell .btn[data-v-1e542682] {
  border: 1px dashed #f86e04;
  border-radius: 2px;
  width: 110px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff7926;
  cursor: pointer
}

.catalog-li .catalog-item .add-cell .btn .txt[data-v-1e542682] {
  margin-left: 10px
}

.catalog-li .catalog-item.catalog[data-v-1e542682] {
  font-size: 16px;
  cursor: pointer
}

.catalog-li .catalog-item.catalog .icon[data-v-1e542682] {
  font-size: 18px
}

.catalog-li .catalog-item.article[data-v-1e542682] {
  font-size: 14px;
  margin: 0 10px;
  background-color: #f5f5f5
}

.catalog-li .catalog-item.article .current[data-v-1e542682] {
  color: #f86e04
}

.catalog-li .catalog-item.article .publish-status[data-v-1e542682] {
  color: #b2b2b2
}

.catalog-li .catalog-item.article .publish-status.publish[data-v-1e542682] {
  color: purple
}

.edit-input[data-v-84e0bf1c] {
  display: inline-block;
  padding: 0 10px;
  min-width: 60px;
  height: 26px;
  line-height: 26px;
  box-sizing: border-box;
  background-color: #ebeef5;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  color: grey
}

.edit-input.on[data-v-84e0bf1c] {
  background-color: #fdf1e7;
  color: #f86e04;
  box-shadow: inset 0 0 0 1px #f86e04
}

.edit-input .inp[data-v-84e0bf1c] {
  border-radius: 2px;
  background-color: #fff;
  box-sizing: border-box;
  padding: 0 5px;
  width: 100px
}

.edit-input .icon[data-v-84e0bf1c] {
  color: #ff7926;
  margin-left: 5px;
  transition: all .3s
}

.edit-input .icon[data-v-84e0bf1c]:hover {
  transform: scale(1.2) translateY(-5%)
}

.title[data-v-6cbf505c] {
  padding: 10px 0
}

.list[data-v-6cbf505c] {
  display: flex;
  flex-wrap: wrap
}

.list .disabled[data-v-6cbf505c] {
  opacity: .4;
  pointer-events: none
}

.list .item[data-v-6cbf505c] {
  margin-right: 10px;
  margin-bottom: 10px
}

.row[data-v-0428944b] {
  display: flex;
  align-items: center
}

.pagin[data-v-0428944b] {
  margin-top: 10px;
  text-align: right
}

.catalog[data-v-0428944b] {
  color: #f86e04;
  border: 1px solid #f86e04;
  text-align: center;
  margin-right: 10px;
  padding: 0 5px;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: keep-all
}

.m-l-5[data-v-0428944b] {
  margin-left: 5px
}

.m-l-10[data-v-0428944b] {
  margin-left: 10px
}

[data-v-0428944b] .el-avatar img {
  width: 100%
}

.catalog[data-v-4a403703] {
  display: flex;
  flex-direction: column;
  height: 0;
  flex: 1
}

.catalog .tabs[data-v-4a403703] {
  flex-shrink: 0;
  display: flex;
  background: #f1f3f6;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.catalog .tabs .item[data-v-4a403703] {
  flex: 1;
  height: 47px;
  line-height: 47px;
  text-align: center
}

.catalog .tabs .item.active[data-v-4a403703] {
  background: #fff
}

.catalog .catalog-list[data-v-4a403703] {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden
}

.catalog .catalog-list .el-scrollbar[data-v-4a403703] {
  height: 100%
}

.catalog .catalog-list .el-scrollbar .add-catalog[data-v-4a403703] {
  padding: 10px
}

.catalog .catalog-list .el-scrollbar .add-catalog .btn[data-v-4a403703] {
  border: 1px dashed #f86e04;
  border-radius: 2px;
  width: 110px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff7926;
  cursor: pointer
}

.catalog .catalog-list .el-scrollbar .add-catalog .btn .txt[data-v-4a403703] {
  margin-left: 10px
}

.catalog .catalog-footer[data-v-4a403703] {
  width: 100%;
  height: 78px;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 10px 10px 0 0;
  background: #f9f5f1
}

.catalog .catalog-footer .row[data-v-4a403703] {
  font-size: 16px;
  line-height: 34px;
  color: #666
}

.catalog .catalog-footer .row .on[data-v-4a403703] {
  color: #f86e04
}

.recycle-bin[data-v-4a403703] {
  display: flex;
  align-items: center;
  color: #f86e04 !important;
  cursor: pointer
}

.recycle-bin .el-icon-delete[data-v-4a403703] {
  font-size: 18px;
  margin-right: 5px
}

.h-svg-icon-box[data-v-2fda8f4e] {
  display: inline-flex
}

.svg-icon[data-v-2fda8f4e] {
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.tool-item[data-v-e27a1336] {
  cursor: pointer
}

.tool-item .item[data-v-e27a1336] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-e27a1336],
.tool-item .item[data-v-e27a1336]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-e27a1336] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-e27a1336] {
  word-break: keep-all
}

.tool-title[data-v-e27a1336] {
  border-radius: 0
}

.tool-title .title[data-v-e27a1336] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-e27a1336] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-e27a1336] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-e27a1336]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-e27a1336] {
  margin-left: 4px
}

.dropdown-list li[data-v-e27a1336]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-e27a1336] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-e27a1336] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-e27a1336] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-e27a1336] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-e27a1336] {
  margin-top: 3px
}

.dropdown-list .item[data-v-e27a1336] {
  padding: 8px
}

.tool-item[data-v-d864f666] {
  cursor: pointer
}

.tool-item .item[data-v-d864f666] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-d864f666],
.tool-item .item[data-v-d864f666]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-d864f666] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-d864f666] {
  word-break: keep-all
}

.tool-title[data-v-d864f666] {
  border-radius: 0
}

.tool-title .title[data-v-d864f666] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-d864f666] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-d864f666] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-d864f666]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-d864f666] {
  margin-left: 4px
}

.dropdown-list li[data-v-d864f666]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-d864f666] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-d864f666] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-d864f666] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-d864f666] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-d864f666] {
  margin-top: 3px
}

li[data-v-53faed55],
ul[data-v-53faed55] {
  list-style: none;
  margin: 0;
  padding: 0
}

.predefine[data-v-53faed55] {
  position: relative;
  padding: 8px
}

.predefine .title[data-v-53faed55] {
  padding: 5px 0;
  cursor: default
}

.predefine .color-list[data-v-53faed55] {
  display: flex;
  justify-content: space-between
}

.predefine .color-list-2[data-v-53faed55] {
  margin-top: 6px
}

.predefine .color-item[data-v-53faed55] {
  cursor: pointer;
  height: 15px;
  width: 15px;
  transition: all .3s ease
}

.predefine .color-item[data-v-53faed55]:hover {
  box-shadow: 0 0 5px rgba(0, 0, 0, .4);
  transform: scale(1.3)
}

.predefine .color-item.white[data-v-53faed55] {
  border: 1px solid #e2e6ed;
  box-sizing: border-box
}

.sv-panel[data-v-aba1b996] {
  height: 120px;
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid #d6d6d6;
  position: relative;
  width: 100%;
  background: linear-gradient(0deg, #000, transparent)
}

.sv-panel .black[data-v-aba1b996],
.sv-panel .white[data-v-aba1b996] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0
}

.sv-panel .white[data-v-aba1b996] {
  background: linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))
}

.sv-panel .black[data-v-aba1b996] {
  background: linear-gradient(0deg, #000, transparent)
}

.sv-panel .thumb[data-v-aba1b996] {
  position: absolute
}

.sv-panel .thumb>div[data-v-aba1b996] {
  cursor: head;
  width: 4px;
  height: 4px;
  box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, .3), 0 0 1px 2px rgba(0, 0, 0, .4);
  border-radius: 50%;
  transform: translate(-2px, -2px)
}

.hue[data-v-4c2c4fb2] {
  position: relative;
  margin-top: 8px
}

.hue .bar[data-v-4c2c4fb2] {
  height: 12px;
  background: linear-gradient(90deg, red 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, red)
}

.hue .thumb[data-v-4c2c4fb2] {
  position: absolute;
  cursor: pointer;
  box-sizing: border-box;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  border-radius: 1px;
  background: #fff;
  border: 1px solid #f0f0f0;
  box-shadow: 0 0 2px rgba(0, 0, 0, .6);
  z-index: 1
}

.alpha-slider[data-v-0e9de8f0] {
  margin-top: 8px;
  position: relative;
  box-sizing: border-box;
  height: 12px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==)
}

.alpha-slider .bar[data-v-0e9de8f0] {
  position: relative;
  height: 100%
}

.alpha-slider .thumb[data-v-0e9de8f0] {
  position: absolute;
  cursor: pointer;
  box-sizing: border-box;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  border-radius: 1px;
  background: #fff;
  border: 1px solid #f0f0f0;
  box-shadow: 0 0 2px rgba(0, 0, 0, .6);
  z-index: 1
}

.panel[data-v-43bda5f2] {
  color: rgba(0, 0, 0, .8);
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e3e3e3;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .12);
  position: absolute;
  z-index: 99
}

.color-box[data-v-43bda5f2] {
  padding: 8px;
  width: 200px
}

.color-box .bar-box[data-v-43bda5f2] {
  display: flex
}

.color-box .bar-box .bar[data-v-43bda5f2] {
  width: 170px
}

.color-box .bar-box .color[data-v-43bda5f2] {
  flex: 1;
  margin-left: 5px;
  margin-top: 8px
}

.color-box .display[data-v-43bda5f2] {
  display: flex;
  justify-content: space-between;
  margin-top: 8px
}

.color-box .display input[data-v-43bda5f2] {
  padding: 4px;
  font-size: 11px;
  box-shadow: inset 0 0 0 1px #ccc;
  width: 100%;
  box-sizing: border-box
}

.color-box .display .hex[data-v-43bda5f2],
.color-box .display .rgba[data-v-43bda5f2] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center
}

.color-box .display .hex span[data-v-43bda5f2],
.color-box .display .rgba span[data-v-43bda5f2] {
  font-size: 11px;
  color: #222
}

.color-box .display .hex[data-v-43bda5f2] {
  width: 65px
}

.color-box .display .rgba[data-v-43bda5f2] {
  width: 30px
}

.color-picker[data-v-38b498f2] {
  width: 200px
}

.color[data-v-38b498f2] {
  position: relative
}

.color[data-v-38b498f2]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.color .row[data-v-38b498f2] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.color .row .title[data-v-38b498f2] {
  margin-left: 4px
}

.tool-item[data-v-7b4b48b5] {
  cursor: pointer
}

.tool-item .item[data-v-7b4b48b5] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-7b4b48b5],
.tool-item .item[data-v-7b4b48b5]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-7b4b48b5] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-7b4b48b5] {
  word-break: keep-all
}

.tool-title[data-v-7b4b48b5] {
  border-radius: 0
}

.tool-title .title[data-v-7b4b48b5] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-7b4b48b5] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-7b4b48b5] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-7b4b48b5]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-7b4b48b5] {
  margin-left: 4px
}

.dropdown-list li[data-v-7b4b48b5]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-7b4b48b5] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-7b4b48b5] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-7b4b48b5] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-7b4b48b5] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-7b4b48b5] {
  margin-top: 3px
}

.line-style[data-v-7b4b48b5] {
  width: 100px
}

.line-style .line[data-v-7b4b48b5] {
  border: none;
  background: none;
  width: 100%;
  margin: 8px 0
}

.line-width-item[data-v-7b4b48b5] {
  width: 50px
}

.tool-item[data-v-e1b2e8ec] {
  cursor: pointer
}

.tool-item .item[data-v-e1b2e8ec] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-e1b2e8ec],
.tool-item .item[data-v-e1b2e8ec]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-e1b2e8ec] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-e1b2e8ec] {
  word-break: keep-all
}

.tool-title[data-v-e1b2e8ec] {
  border-radius: 0
}

.tool-title .title[data-v-e1b2e8ec] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-e1b2e8ec] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-e1b2e8ec] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-e1b2e8ec]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-e1b2e8ec] {
  margin-left: 4px
}

.dropdown-list li[data-v-e1b2e8ec]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-e1b2e8ec] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-e1b2e8ec] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-e1b2e8ec] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-e1b2e8ec] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-e1b2e8ec] {
  margin-top: 3px
}

.item[data-v-e1b2e8ec] {
  flex-direction: column
}

.tool-item[data-v-49e72aac] {
  cursor: pointer
}

.tool-item .item[data-v-49e72aac] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-49e72aac],
.tool-item .item[data-v-49e72aac]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-49e72aac] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-49e72aac] {
  word-break: keep-all
}

.tool-title[data-v-49e72aac] {
  border-radius: 0
}

.tool-title .title[data-v-49e72aac] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-49e72aac] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-49e72aac] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-49e72aac]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-49e72aac] {
  margin-left: 4px
}

.dropdown-list li[data-v-49e72aac]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-49e72aac] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-49e72aac] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-49e72aac] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-49e72aac] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-49e72aac] {
  margin-top: 3px
}

.tool-item[data-v-f34c85de] {
  cursor: pointer
}

.tool-item .item[data-v-f34c85de] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-f34c85de],
.tool-item .item[data-v-f34c85de]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-f34c85de] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-f34c85de] {
  word-break: keep-all
}

.tool-title[data-v-f34c85de] {
  border-radius: 0
}

.tool-title .title[data-v-f34c85de] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-f34c85de] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-f34c85de] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-f34c85de]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-f34c85de] {
  margin-left: 4px
}

.dropdown-list li[data-v-f34c85de]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-f34c85de] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-f34c85de] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-f34c85de] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-f34c85de] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-f34c85de] {
  margin-top: 3px
}

.auto-paging[data-v-3d1e030b] {
  position: absolute;
  visibility: hidden;
  box-sizing: border-box;
  border: 1px solid #000;
  background: red;
  z-index: 1000
}

.textContainer[data-v-3d1e030b] {
  height: 100%;
  width: 100%;
  display: flex
}

.tool-item[data-v-0a720a03] {
  cursor: pointer
}

.tool-item .item[data-v-0a720a03] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0a720a03],
.tool-item .item[data-v-0a720a03]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0a720a03] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0a720a03] {
  word-break: keep-all
}

.tool-title[data-v-0a720a03] {
  border-radius: 0
}

.tool-title .title[data-v-0a720a03] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0a720a03] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0a720a03] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0a720a03]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0a720a03] {
  margin-left: 4px
}

.dropdown-list li[data-v-0a720a03]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0a720a03] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0a720a03] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0a720a03] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0a720a03] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0a720a03] {
  margin-top: 3px
}

.item[data-v-0a720a03] {
  flex-direction: column
}

.tool-item[data-v-1134a084] {
  cursor: pointer
}

.tool-item .item[data-v-1134a084] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-1134a084],
.tool-item .item[data-v-1134a084]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-1134a084] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-1134a084] {
  word-break: keep-all
}

.tool-title[data-v-1134a084] {
  border-radius: 0
}

.tool-title .title[data-v-1134a084] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-1134a084] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-1134a084] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-1134a084]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-1134a084] {
  margin-left: 4px
}

.dropdown-list li[data-v-1134a084]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-1134a084] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-1134a084] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-1134a084] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-1134a084] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-1134a084] {
  margin-top: 3px
}

.dropdown-list .disabled[data-v-1134a084] {
  pointer-events: none;
  opacity: .3
}

.tool-item[data-v-2027d200] {
  cursor: pointer
}

.tool-item .item[data-v-2027d200] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-2027d200],
.tool-item .item[data-v-2027d200]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-2027d200] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-2027d200] {
  word-break: keep-all
}

.tool-title[data-v-2027d200] {
  border-radius: 0
}

.tool-title .title[data-v-2027d200] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-2027d200] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-2027d200] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-2027d200]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-2027d200] {
  margin-left: 4px
}

.dropdown-list li[data-v-2027d200]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-2027d200] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-2027d200] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-2027d200] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-2027d200] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-2027d200] {
  margin-top: 3px
}

.item[data-v-2027d200] {
  flex-direction: column
}

.tool-item[data-v-658bc200] {
  cursor: pointer
}

.tool-item .item[data-v-658bc200] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-658bc200],
.tool-item .item[data-v-658bc200]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-658bc200] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-658bc200] {
  word-break: keep-all
}

.tool-title[data-v-658bc200] {
  border-radius: 0
}

.tool-title .title[data-v-658bc200] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-658bc200] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-658bc200] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-658bc200]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-658bc200] {
  margin-left: 4px
}

.dropdown-list li[data-v-658bc200]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-658bc200] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-658bc200] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-658bc200] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-658bc200] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-658bc200] {
  margin-top: 3px
}

.item[data-v-658bc200] {
  flex-direction: column
}

.orc_container .header[data-v-658bc200] {
  display: flex;
  height: 300px;
  margin-bottom: 20px;
  justify-content: space-between
}

.orc_container .header .img[data-v-658bc200] {
  padding: 10px;
  background-color: hsla(0, 0%, 87.1%, .8705882352941177);
  border: 1px solid hsla(0, 0%, 87.1%, .8705882352941177);
  width: 40%;
  display: flex;
  justify-content: center;
  align-items: center
}

.orc_container .header .img img[data-v-658bc200] {
  max-width: 100%;
  max-height: 100%
}

.orc_container .header .btn[data-v-658bc200] {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column
}

.orc_container .header .btn .el-button[data-v-658bc200] {
  margin: 0;
  margin-bottom: 10px
}

.orc_container .header .result[data-v-658bc200] {
  width: 40%
}

.orc_container .words[data-v-658bc200] {
  position: relative
}

.orc_container .words .word[data-v-658bc200] {
  position: absolute;
  border: 1px solid #000;
  box-sizing: border-box
}

.orc_container .tools[data-v-658bc200] {
  display: flex;
  height: 100px
}

.orc_container[data-v-658bc200] .h-slider .el-slider__runway {
  width: 2px !important
}

.orc_container[data-v-658bc200] .h-slider .el-slider__bar {
  width: 2px !important;
  background-color: #f86e04 !important
}

.orc_container[data-v-658bc200] .h-slider .el-slider__button-wrapper {
  left: -17px
}

.orc_container[data-v-658bc200] .h-slider .el-slider__button {
  height: 10px;
  width: 10px;
  box-sizing: border-box;
  border: 0;
  box-shadow: 0 0 3px 1px hsla(0, 0%, 42%, .17)
}

.orc_container[data-v-658bc200] .v-slider .el-slider__runway {
  height: 2px !important;
  width: 100px
}

.orc_container[data-v-658bc200] .v-slider .el-slider__bar {
  height: 2px !important;
  background-color: #f86e04 !important
}

.orc_container[data-v-658bc200] .v-slider .el-slider__button-wrapper {
  top: -17px
}

.orc_container[data-v-658bc200] .v-slider .el-slider__button {
  height: 10px;
  width: 10px;
  box-sizing: border-box;
  border: 0;
  box-shadow: 0 0 3px 1px hsla(0, 0%, 42%, .17)
}

.setting-section[data-v-3093231e] {
  display: flex;
  height: 36px;
  margin-top: 26px
}

.setting-section .setting-section-left[data-v-3093231e] {
  width: 60px
}

.setting-section .setting-name[data-v-3093231e] {
  margin-right: 5px;
  line-height: 36px
}

.setting-section .el-input-number[data-v-3093231e] {
  width: 80px
}

.setting-section .unit[data-v-3093231e] {
  margin-left: 2px
}

.setting-section .ml38[data-v-3093231e] {
  margin-left: 38px
}

.tool-item[data-v-0d9796fe] {
  cursor: pointer
}

.tool-item .item[data-v-0d9796fe] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0d9796fe],
.tool-item .item[data-v-0d9796fe]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0d9796fe] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0d9796fe] {
  word-break: keep-all
}

.tool-title[data-v-0d9796fe] {
  border-radius: 0
}

.tool-title .title[data-v-0d9796fe] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0d9796fe] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0d9796fe] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0d9796fe]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0d9796fe] {
  margin-left: 4px
}

.dropdown-list li[data-v-0d9796fe]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0d9796fe] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0d9796fe] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0d9796fe] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0d9796fe] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0d9796fe] {
  margin-top: 3px
}

.tool-item[data-v-0cd29738] {
  cursor: pointer
}

.tool-item .item[data-v-0cd29738] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0cd29738],
.tool-item .item[data-v-0cd29738]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0cd29738] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0cd29738] {
  word-break: keep-all
}

.tool-title[data-v-0cd29738] {
  border-radius: 0
}

.tool-title .title[data-v-0cd29738] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0cd29738] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0cd29738] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0cd29738]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0cd29738] {
  margin-left: 4px
}

.dropdown-list li[data-v-0cd29738]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0cd29738] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0cd29738] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0cd29738] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0cd29738] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0cd29738] {
  margin-top: 3px
}

.item[data-v-0cd29738] {
  flex-direction: column
}

.tool-item[data-v-50dae217] {
  cursor: pointer
}

.tool-item .item[data-v-50dae217] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-50dae217],
.tool-item .item[data-v-50dae217]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-50dae217] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-50dae217] {
  word-break: keep-all
}

.tool-title[data-v-50dae217] {
  border-radius: 0
}

.tool-title .title[data-v-50dae217] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-50dae217] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-50dae217] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-50dae217]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-50dae217] {
  margin-left: 4px
}

.dropdown-list li[data-v-50dae217]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-50dae217] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-50dae217] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-50dae217] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-50dae217] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-50dae217] {
  margin-top: 3px
}

.item[data-v-50dae217] {
  flex-direction: column
}

.tool-item[data-v-02439280] {
  cursor: pointer
}

.tool-item .item[data-v-02439280] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-02439280],
.tool-item .item[data-v-02439280]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-02439280] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-02439280] {
  word-break: keep-all
}

.tool-title[data-v-02439280] {
  border-radius: 0
}

.tool-title .title[data-v-02439280] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-02439280] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-02439280] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-02439280]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-02439280] {
  margin-left: 4px
}

.dropdown-list li[data-v-02439280]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-02439280] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-02439280] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-02439280] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-02439280] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-02439280] {
  margin-top: 3px
}

.item .icon[data-v-02439280] {
  width: 16px;
  height: 16px;
  margin-right: 5px
}

.tool-item[data-v-0d499a54] {
  cursor: pointer
}

.tool-item .item[data-v-0d499a54] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0d499a54],
.tool-item .item[data-v-0d499a54]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0d499a54] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0d499a54] {
  word-break: keep-all
}

.tool-title[data-v-0d499a54] {
  border-radius: 0
}

.tool-title .title[data-v-0d499a54] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0d499a54] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0d499a54] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0d499a54]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0d499a54] {
  margin-left: 4px
}

.dropdown-list li[data-v-0d499a54]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0d499a54] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0d499a54] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0d499a54] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0d499a54] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0d499a54] {
  margin-top: 3px
}

.textbox[data-v-0d499a54] {
  flex-direction: column;
  justify-content: center
}

.textbox .text[data-v-0d499a54],
.textbox[data-v-0d499a54] {
  display: flex;
  align-items: center
}

.tool-item[data-v-64c85647] {
  cursor: pointer
}

.tool-item .item[data-v-64c85647] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-64c85647],
.tool-item .item[data-v-64c85647]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-64c85647] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-64c85647] {
  word-break: keep-all
}

.tool-title[data-v-64c85647] {
  border-radius: 0
}

.tool-title .title[data-v-64c85647] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-64c85647] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-64c85647] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-64c85647]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-64c85647] {
  margin-left: 4px
}

.dropdown-list li[data-v-64c85647]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-64c85647] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-64c85647] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-64c85647] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-64c85647] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-64c85647] {
  margin-top: 3px
}

.item[data-v-64c85647] {
  flex-direction: column
}

.tool-item[data-v-68cb8ad0] {
  cursor: pointer
}

.tool-item .item[data-v-68cb8ad0] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-68cb8ad0],
.tool-item .item[data-v-68cb8ad0]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-68cb8ad0] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-68cb8ad0] {
  word-break: keep-all
}

.tool-title[data-v-68cb8ad0] {
  border-radius: 0
}

.tool-title .title[data-v-68cb8ad0] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-68cb8ad0] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-68cb8ad0] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-68cb8ad0]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-68cb8ad0] {
  margin-left: 4px
}

.dropdown-list li[data-v-68cb8ad0]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-68cb8ad0] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-68cb8ad0] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-68cb8ad0] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-68cb8ad0] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-68cb8ad0] {
  margin-top: 3px
}

.table-box[data-v-68cb8ad0] {
  padding: 8px
}

.table-box .row[data-v-68cb8ad0] {
  padding: 8px 0;
  cursor: pointer
}

table[data-v-68cb8ad0] {
  width: 140px;
  cursor: default
}

table td div[data-v-68cb8ad0] {
  height: 12px;
  width: 12px;
  border: 1px solid #ccc
}

table td div.selected[data-v-68cb8ad0] {
  background: rgba(255, 121, 38, .6)
}

.setting-section-right[data-v-68cb8ad0] {
  display: flex;
  align-items: center
}

.tool-item[data-v-0fd1815a] {
  cursor: pointer
}

.tool-item .item[data-v-0fd1815a] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-0fd1815a],
.tool-item .item[data-v-0fd1815a]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-0fd1815a] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-0fd1815a] {
  word-break: keep-all
}

.tool-title[data-v-0fd1815a] {
  border-radius: 0
}

.tool-title .title[data-v-0fd1815a] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-0fd1815a] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-0fd1815a] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-0fd1815a]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-0fd1815a] {
  margin-left: 4px
}

.dropdown-list li[data-v-0fd1815a]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-0fd1815a] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-0fd1815a] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-0fd1815a] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-0fd1815a] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-0fd1815a] {
  margin-top: 3px
}

.textbox[data-v-0fd1815a] {
  flex-direction: column;
  justify-content: center
}

.textbox .text[data-v-0fd1815a],
.textbox[data-v-0fd1815a] {
  display: flex;
  align-items: center
}

.tool-item[data-v-1877ddb3] {
  cursor: pointer
}

.tool-item .item[data-v-1877ddb3] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-1877ddb3],
.tool-item .item[data-v-1877ddb3]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-1877ddb3] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-1877ddb3] {
  word-break: keep-all
}

.tool-title[data-v-1877ddb3] {
  border-radius: 0
}

.tool-title .title[data-v-1877ddb3] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-1877ddb3] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-1877ddb3] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-1877ddb3]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-1877ddb3] {
  margin-left: 4px
}

.dropdown-list li[data-v-1877ddb3]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-1877ddb3] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-1877ddb3] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-1877ddb3] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-1877ddb3] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-1877ddb3] {
  margin-top: 3px
}

.tool-item {
  cursor: pointer
}

.tool-item .item {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active,
.tool-item .item:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt {
  word-break: keep-all
}

.tool-title {
  border-radius: 0
}

.tool-title .title {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips {
  max-width: 120px;
  line-height: 150%
}

.picker-item {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title {
  margin-left: 4px
}

.dropdown-list li:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked {
  position: absolute;
  left: 12px
}

.text-style-select-tool {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom] {
  margin-top: 3px
}

.select[data-v-1a61a581] {
  justify-content: center
}

.text[data-v-1a61a581] {
  margin-right: 10px;
  min-width: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.dropdown-list[data-v-1a61a581] {
  max-height: 500px;
  overflow-x: hidden
}

.dropdown-list .inp-box[data-v-1a61a581] {
  position: relative;
  display: flex;
  align-items: center;
  width: 142px;
  box-sizing: border-box;
  height: 35px;
  padding: 0 10px 0 32px
}

.dropdown-list .inp-box .inp[data-v-1a61a581] {
  width: 100%
}

.dropdown-list .inp-box .inp[data-v-1a61a581]::-webkit-input-placeholder {
  color: #999
}

.dropdown-list .inp-box .inp[data-v-1a61a581]::-moz-placeholder {
  color: #999
}

.dropdown-list .inp-box .placeholder[data-v-1a61a581] {
  width: 100%;
  background-color: #fff;
  color: #999
}

.dropdown-list .inp-box .checked[data-v-1a61a581] {
  position: absolute;
  left: 12px
}

.dropdown-list .inp-box .icon[data-v-1a61a581]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.tool-item[data-v-5fbb675f] {
  cursor: pointer
}

.tool-item .item[data-v-5fbb675f] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-5fbb675f],
.tool-item .item[data-v-5fbb675f]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-5fbb675f] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-5fbb675f] {
  word-break: keep-all
}

.tool-title[data-v-5fbb675f] {
  border-radius: 0
}

.tool-title .title[data-v-5fbb675f] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-5fbb675f] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-5fbb675f] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-5fbb675f]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-5fbb675f] {
  margin-left: 4px
}

.dropdown-list li[data-v-5fbb675f]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-5fbb675f] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-5fbb675f] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-5fbb675f] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-5fbb675f] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-5fbb675f] {
  margin-top: 3px
}

.tool-item[data-v-46d8869c] {
  cursor: pointer
}

.tool-item .item[data-v-46d8869c] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-46d8869c],
.tool-item .item[data-v-46d8869c]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-46d8869c] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-46d8869c] {
  word-break: keep-all
}

.tool-title[data-v-46d8869c] {
  border-radius: 0
}

.tool-title .title[data-v-46d8869c] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-46d8869c] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-46d8869c] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-46d8869c]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-46d8869c] {
  margin-left: 4px
}

.dropdown-list li[data-v-46d8869c]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-46d8869c] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-46d8869c] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-46d8869c] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-46d8869c] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-46d8869c] {
  margin-top: 3px
}

.tool-item[data-v-4d7ab355] {
  cursor: pointer
}

.tool-item .item[data-v-4d7ab355] {
  display: flex;
  align-items: center;
  margin-right: 4px;
  padding: 4px
}

.tool-item .item.active[data-v-4d7ab355],
.tool-item .item[data-v-4d7ab355]:hover {
  background-color: rgba(0, 0, 0, .08)
}

.tool-item .item.disabled[data-v-4d7ab355] {
  opacity: .3;
  pointer-events: none
}

.tool-item .txt[data-v-4d7ab355] {
  word-break: keep-all
}

.tool-title[data-v-4d7ab355] {
  border-radius: 0
}

.tool-title .title[data-v-4d7ab355] {
  font-weight: bolder;
  margin-bottom: 5px
}

.tool-title .tips[data-v-4d7ab355] {
  max-width: 120px;
  line-height: 150%
}

.picker-item[data-v-4d7ab355] {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer
}

.picker-item[data-v-4d7ab355]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.picker-item .title[data-v-4d7ab355] {
  margin-left: 4px
}

.dropdown-list li[data-v-4d7ab355]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item[data-v-4d7ab355] {
  cursor: pointer;
  padding: 8px 32px;
  display: flex;
  align-items: center;
  position: relative
}

.dropdown-list .item.active[data-v-4d7ab355] {
  background-color: rgba(0, 0, 0, .04)
}

.dropdown-list .item .checked[data-v-4d7ab355] {
  position: absolute;
  left: 12px
}

.text-style-select-tool[data-v-4d7ab355] {
  padding: 0
}

.text-style-select-tool[x-placement^=bottom][data-v-4d7ab355] {
  margin-top: 3px
}

.tool-bar[data-v-59157854] {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.tool-bar.disabled[data-v-59157854] {
  pointer-events: none;
  opacity: .3
}

.tool-bar .section[data-v-59157854] {
  flex-shrink: 0;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 10px 0;
  margin: 0 15px;
  box-sizing: border-box
}

.tool-bar .section .row[data-v-59157854] {
  display: flex
}

.tool-bar .section[data-v-59157854]:after {
  content: "";
  position: absolute;
  background: #eaeef1;
  top: 50%;
  transform: translateY(-50%);
  left: -15px;
  height: 80%;
  width: 1px
}

.tool-bar .section .combox[data-v-59157854] {
  display: flex;
  border: 1px solid #cacaca;
  width: 100%
}

.tool-bar .section .combox .item[data-v-59157854] {
  flex: 1;
  border-right: 1px solid #cacaca
}

.tool-bar .section .combox .item[data-v-59157854]:last-child {
  border-right: none
}

.tool-bar .section .column[data-v-59157854] {
  flex-direction: column
}

.tool-bar .section .icon-group[data-v-59157854] {
  display: flex;
  justify-content: space-between
}

.tool-bar .section .icon-group .item[data-v-59157854] {
  cursor: pointer;
  margin-right: 5px;
  display: flex;
  align-items: center
}

.tool-bar .section .icon-group .item.disabled[data-v-59157854] {
  opacity: .3;
  pointer-events: none
}

.tool-bar .section .icon-group .item.large[data-v-59157854] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center
}

.tool-bar .section .icon-group .active[data-v-59157854] {
  background-color: rgba(0, 0, 0, .08)
}

.flex-row[data-v-59157854] {
  flex-direction: row !important
}

.first-section[data-v-59157854] {
  position: sticky !important;
  left: 0;
  top: var(--top-tab-height);
  background-color: #fff;
  z-index: 10
}

.top-box[data-v-7b7dd3a5] {
  flex-shrink: 0;
  height: 80px;
  padding-left: 30px;
  background-color: #fff;
  box-shadow: 0 8px 6px 0 rgba(0, 0, 0, .04)
}

.top-box .row[data-v-7b7dd3a5] {
  padding: 15px 0 5px;
  font-size: 16px
}

.top-box .tabs[data-v-7b7dd3a5] {
  padding-right: 30px;
  color: #666;
  display: flex;
  justify-content: space-between;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none
}

.top-box .tabs .item[data-v-7b7dd3a5] {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding-bottom: 2px;
  cursor: pointer
}

.top-box .tabs .item.on[data-v-7b7dd3a5] {
  background-color: #f2f2f2;
  border-radius: 2px;
  color: #f86e04;
  border-bottom: 2px solid #f86e04;
  padding-bottom: 0
}

.h-select-box[data-v-173d4ca8] {
  position: relative;
  width: 360px
}

.cell[data-v-173d4ca8] {
  width: 100%;
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  border-radius: 2px;
  padding: 0 10px 0 15px;
  display: inline-flex;
  align-items: center
}

.cell .inp[data-v-173d4ca8] {
  flex: 1;
  cursor: pointer;
  color: #606266
}

.cell .arrows[data-v-173d4ca8] {
  flex-shrink: 0;
  color: #c0c4cc;
  transition: all .3s
}

.cell .arrows.on[data-v-173d4ca8] {
  transform: rotate(-180deg)
}

.pull-down[data-v-173d4ca8] {
  width: 360px;
  padding-top: 10px;
  line-height: 0
}

.pull-down .item[data-v-173d4ca8] {
  display: inline-block;
  padding: 0 10px;
  min-width: 60px;
  height: 26px;
  line-height: 26px;
  box-sizing: border-box;
  background-color: #ebeef5;
  border-radius: 4px;
  margin: 0 0 10px 10px;
  text-align: center;
  cursor: pointer;
  color: grey
}

.pull-down .item .inp[data-v-173d4ca8] {
  border-radius: 2px;
  background-color: #fff;
  box-sizing: border-box;
  padding: 0 5px;
  width: 100px
}

.pull-down .item .icon[data-v-173d4ca8] {
  color: #ff7926;
  margin-left: 5px;
  transition: all .3s
}

.pull-down .item .icon[data-v-173d4ca8]:hover {
  transform: scale(1.2) translateY(-5%)
}

.pull-down .item.on[data-v-173d4ca8] {
  background-color: #fdf1e7;
  color: #f86e04;
  box-shadow: inset 0 0 0 1px #f86e04
}

.pull-down .add-btn[data-v-173d4ca8] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 88px;
  height: 22px;
  border-radius: 4px;
  border: 1px dashed #ff7926;
  color: #ff7926;
  margin: 0 0 10px 10px;
  cursor: pointer
}

[data-v-38092d62] .el-form .el-input {
  width: 360px
}

[data-v-38092d62] .el-dialog__body {
  padding-bottom: 20px
}

[data-v-38092d62] .el-dialog__footer {
  padding-top: 30px;
  border-top: 1px dashed #e5e5e5
}

.up-pic[data-v-38092d62] {
  box-sizing: border-box;
  width: 110px;
  height: 110px;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer
}

.up-pic .txt[data-v-38092d62] {
  color: grey
}

.h-page[data-v-22662402] {
  height: 100%
}

.list[data-v-22662402] {
  height: calc(100% - 120px);
  overflow-x: hidden
}

.list .section[data-v-22662402] {
  display: flex;
  margin-bottom: 25px
}

.list .section[data-v-22662402]:first-child {
  margin-top: 30px
}

.list .section.on .litimg[data-v-22662402] {
  border: 1px solid #ff7926;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .16)
}

.list .section.on .tool[data-v-22662402] {
  display: flex
}

.list .section .current[data-v-22662402] {
  width: 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  font-size: 16px
}

.list .section .litimg[data-v-22662402] {
  flex-shrink: 0;
  box-sizing: border-box;
  border: 1px solid #dcdfe5;
  overflow: hidden
}

.list .section .tool[data-v-22662402] {
  flex-shrink: 0;
  width: 30px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .16);
  display: none;
  align-items: center;
  flex-direction: column;
  justify-content: space-around;
  margin-left: 5px
}

.list .section .tool .tool-item[data-v-22662402] {
  cursor: pointer
}

.page[data-v-22662402] {
  transform-origin: left top;
  background-color: #fff;
  pointer-events: none;
  background: #fff;
  box-sizing: border-box
}

.page .pag-main[data-v-22662402] {
  display: flex;
  box-sizing: border-box
}

.page .page-head[data-v-22662402] {
  border-right: 1px solid #000;
  box-sizing: border-box;
  padding: 2pt
}

.page .page-head .title[data-v-22662402] {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  position: relative;
  box-sizing: border-box;
  border: 1px dashed #000
}

.page .page-head .title .txt[data-v-22662402] {
  font-size: 32px;
  font-family: å®‹ä½“;
  text-align: center;
  padding: 20px 0;
  width: 32px;
  word-break: break-word
}

.page .page-head .title .block[data-v-22662402] {
  width: 100%;
  height: 20px;
  background: #000
}

.page .page-head .title .page-no[data-v-22662402] {
  font-size: 20px
}

.page .page-head .title .clan-title[data-v-22662402] {
  font-weight: 700
}

.page .page-head .title .tips[data-v-22662402] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center
}

.page .page-content[data-v-22662402] {
  box-sizing: border-box;
  position: relative
}

.page .page-content .item-container[data-v-22662402] {
  box-sizing: border-box;
  position: relative;
  height: 100%;
  width: 100%
}

.tool2[data-v-22662402] {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 -24px 24px 0 hsla(0, 0%, 42%, .05);
  z-index: 10;
  cursor: pointer
}

[data-v-22662402] .vdr {
  border-color: transparent !important
}

[data-v-22662402] .vdr .handle {
  display: none !important
}

.h-page[data-v-e9822bbc] {
  height: 100%
}

.list[data-v-e9822bbc] {
  height: calc(100% - 120px);
  overflow-x: hidden
}

.list .box[data-v-e9822bbc] {
  border-bottom: 1px solid #f1f3f6
}

.list .box[data-v-e9822bbc]:first-child {
  margin-top: 30px
}

.list .box.fold .title-bar[data-v-e9822bbc] {
  color: #ff7926
}

.list .box.fold .title-bar .arrow[data-v-e9822bbc] {
  transform: rotate(-180deg)
}

.list .box .title-bar[data-v-e9822bbc] {
  height: 40px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
  cursor: pointer
}

.list .box .title-bar .left-title[data-v-e9822bbc] {
  flex: 1;
  width: 0
}

.list .box .title-bar .icon[data-v-e9822bbc] {
  transition: all .3s;
  cursor: pointer;
  margin-left: 10px
}

.list .box .sub-box[data-v-e9822bbc] {
  flex-wrap: wrap;
  margin-bottom: 10px;
  display: flex
}

.list .box .sub-box .sub-item[data-v-e9822bbc] {
  margin-left: 6px;
  width: 110px;
  display: inline-flex;
  align-items: center;
  flex-direction: column;
  color: #999;
  cursor: pointer
}

.list .box .sub-box .sub-item.on[data-v-e9822bbc] {
  color: #ff7926
}

.list .box .sub-box .sub-item.on .pic[data-v-e9822bbc]:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 0 2px #ff7926
}

.list .box .sub-box .sub-item .pic[data-v-e9822bbc] {
  position: relative;
  width: 100%;
  height: 150px;
  box-sizing: border-box;
  overflow: hidden
}

.list .box .sub-box .sub-item .pic .img[data-v-e9822bbc] {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover
}

.list .box .sub-box .sub-item .txt[data-v-e9822bbc] {
  line-height: 33px
}

.tool[data-v-e9822bbc] {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -24px 24px 0 hsla(0, 0%, 42%, .05);
  z-index: 10
}

.tool .item[data-v-e9822bbc] {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  cursor: pointer
}

.tool .item[data-v-e9822bbc]:first-child:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #e6e6e6
}

.el-button[data-v-e9822bbc] {
  width: 120px
}

.list[data-v-668887e5] {
  height: calc(100vh - 151px);
  overflow-x: hidden
}

.list .box[data-v-668887e5] {
  border-bottom: 1px solid #f1f3f6
}

.list .box[data-v-668887e5]:first-child {
  margin-top: 30px
}

.list .box.fold .title-bar[data-v-668887e5] {
  color: #ff7926
}

.list .box.fold .title-bar .h-icon-gengduo[data-v-668887e5] {
  transform: rotate(-180deg)
}

.list .box.fold .sub-box[data-v-668887e5] {
  display: flex
}

.list .box .title-bar[data-v-668887e5] {
  height: 40px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
  cursor: pointer
}

.list .box .title-bar .left-title[data-v-668887e5] {
  flex: 1;
  width: 0
}

.list .box .title-bar .icon[data-v-668887e5] {
  transition: all .3s;
  cursor: pointer;
  margin-left: 10px
}

.list .box .sub-box[data-v-668887e5] {
  flex-wrap: wrap;
  margin-bottom: 10px;
  display: none
}

.list .box .sub-box .sub-item[data-v-668887e5] {
  margin-left: 6px;
  width: 110px;
  display: inline-flex;
  align-items: center;
  flex-direction: column;
  color: #999;
  cursor: pointer
}

.list .box .sub-box .sub-item.on[data-v-668887e5] {
  color: #ff7926
}

.list .box .sub-box .sub-item.on .pic[data-v-668887e5]:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 0 2px #ff7926
}

.list .box .sub-box .sub-item .pic[data-v-668887e5] {
  position: relative;
  width: 100px;
  height: 100px;
  box-sizing: border-box;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.list .box .sub-box .sub-item .pic .img[data-v-668887e5] {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover
}

.list .box .sub-box .sub-item .txt[data-v-668887e5] {
  line-height: 33px
}

.list .box .sub-box .up-item .pic[data-v-668887e5] {
  border: 1px dashed #ff7926;
  border-radius: 4px
}

.tool[data-v-668887e5] {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -24px 24px 0 hsla(0, 0%, 42%, .05);
  z-index: 10
}

.tool .item[data-v-668887e5] {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  cursor: pointer
}

.tool .item[data-v-668887e5]:first-child:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #e6e6e6
}

.el-button[data-v-668887e5] {
  width: 120px
}

.h-page[data-v-f508efec] {
  height: 100%
}

.list[data-v-f508efec] {
  padding-top: 20px;
  box-sizing: border-box;
  height: calc(100% - 80px);
  overflow-x: hidden
}

.list .item[data-v-f508efec] {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border-bottom: 1px solid #f1f3f6;
  height: 40px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
  cursor: pointer
}

.list .item .left-title[data-v-f508efec] {
  flex: 1;
  width: 0;
  margin-left: 8px
}

.list .item .icon[data-v-f508efec] {
  cursor: pointer;
  margin-left: 10px
}

.list .item[data-v-f508efec]:hover {
  background-color: rgba(0, 0, 0, .04)
}

.list .item .icon[data-v-f508efec]:hover,
.list .item.on[data-v-f508efec] {
  color: #ff7926
}

.tool[data-v-f508efec] {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -24px 24px 0 hsla(0, 0%, 42%, .05);
  z-index: 10
}

.tool .item[data-v-f508efec] {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  cursor: pointer
}

.tool .item[data-v-f508efec]:first-child:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 30px;
  background-color: #e6e6e6
}

.h-line-select[data-v-6292a264] {
  position: relative
}

.line[data-v-6292a264] {
  width: 80%;
  border: none;
  background: none;
  border-top-style: solid
}

.title[data-v-6292a264] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  cursor: pointer
}

.title .left-bar[data-v-6292a264] {
  flex: 1;
  background-color: #f2f2f2;
  border-radius: 4px
}

.title .arrows[data-v-6292a264],
.title .left-bar[data-v-6292a264] {
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.title .arrows[data-v-6292a264] {
  flex-shrink: 0;
  width: 20px;
  transition: all .3s
}

.title .arrows.on[data-v-6292a264] {
  transform: rotate(-180deg)
}

.line-style[data-v-6292a264] {
  width: 100px
}

.line-style .line[data-v-6292a264] {
  border: none;
  background: none;
  width: 100%;
  margin: 8px 0
}

.line-width-item[data-v-6292a264] {
  width: 50px
}

.h-line-select[data-v-23ea0a40] {
  position: relative
}

.line[data-v-23ea0a40] {
  width: 80%;
  border: none;
  background: none;
  border-top-style: solid
}

.title[data-v-23ea0a40] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  cursor: pointer
}

.title .left-bar[data-v-23ea0a40] {
  flex: 1;
  background-color: #f2f2f2;
  border-radius: 4px
}

.title .arrows[data-v-23ea0a40],
.title .left-bar[data-v-23ea0a40] {
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.title .arrows[data-v-23ea0a40] {
  flex-shrink: 0;
  width: 20px;
  transition: all .3s
}

.title .arrows.on[data-v-23ea0a40] {
  transform: rotate(-180deg)
}

.line-style[data-v-23ea0a40] {
  width: 100px
}

.line-style .line[data-v-23ea0a40] {
  border: none;
  background: none;
  width: 100%;
  margin: 8px 0
}

.line-width-item[data-v-23ea0a40] {
  width: 50px
}

.empty-picker[data-v-3bf9a2c0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-3bf9a2c0] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-3bf9a2c0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.color-picker[data-v-3bf9a2c0] {
  position: relative
}

.color-picker .color-row[data-v-3bf9a2c0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-3bf9a2c0] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-3bf9a2c0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

::deep .input-number-box[data-v-209a265c] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-209a265c] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-209a265c] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-209a265c] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-209a265c],
::deep .el-input-number--small .el-input-number__increase[data-v-209a265c] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-209a265c] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-209a265c] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-209a265c] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-209a265c] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-209a265c],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-209a265c] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-209a265c] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-209a265c]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-209a265c] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-209a265c] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-209a265c] {
  border: 0
}

::deep .el-input__suffix[data-v-209a265c] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-209a265c] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-209a265c] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-209a265c] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-209a265c] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-209a265c] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-209a265c] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-209a265c] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-209a265c] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-209a265c] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-209a265c] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-209a265c] {
  position: relative
}

.color-picker .color-row[data-v-209a265c] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-209a265c] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-209a265c] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-209a265c] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-209a265c] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-209a265c] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-209a265c] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-209a265c] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-209a265c] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-209a265c] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-209a265c] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-209a265c] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-209a265c] {
  flex: 1
}

::deep .input-number-box[data-v-4e679bd0] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-4e679bd0] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-4e679bd0] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-4e679bd0] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-4e679bd0],
::deep .el-input-number--small .el-input-number__increase[data-v-4e679bd0] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-4e679bd0] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-4e679bd0] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-4e679bd0] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-4e679bd0] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-4e679bd0],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-4e679bd0] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-4e679bd0] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-4e679bd0]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-4e679bd0] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-4e679bd0] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-4e679bd0] {
  border: 0
}

::deep .el-input__suffix[data-v-4e679bd0] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-4e679bd0] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-4e679bd0] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-4e679bd0] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-4e679bd0] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-4e679bd0] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-4e679bd0] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-4e679bd0] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-4e679bd0] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-4e679bd0] {
  position: relative
}

.color-picker .color-row[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-4e679bd0] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-4e679bd0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-4e679bd0] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-4e679bd0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-4e679bd0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-4e679bd0] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-4e679bd0] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-4e679bd0] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-4e679bd0] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-4e679bd0] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-4e679bd0] {
  flex: 1
}

::deep .input-number-box[data-v-68919dc0] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-68919dc0] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-68919dc0] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-68919dc0] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-68919dc0],
::deep .el-input-number--small .el-input-number__increase[data-v-68919dc0] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-68919dc0] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-68919dc0] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-68919dc0] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-68919dc0] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-68919dc0],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-68919dc0] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-68919dc0] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-68919dc0]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-68919dc0] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-68919dc0] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-68919dc0] {
  border: 0
}

::deep .el-input__suffix[data-v-68919dc0] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-68919dc0] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-68919dc0] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-68919dc0] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-68919dc0] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-68919dc0] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-68919dc0] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-68919dc0] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-68919dc0] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-68919dc0] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-68919dc0] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-68919dc0] {
  position: relative
}

.color-picker .color-row[data-v-68919dc0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-68919dc0] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-68919dc0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-68919dc0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-68919dc0] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-68919dc0] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-68919dc0] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-68919dc0] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-68919dc0] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-68919dc0] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-68919dc0] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-68919dc0] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-68919dc0] {
  flex: 1
}

::deep .input-number-box[data-v-7caac130] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-7caac130] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-7caac130] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-7caac130] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-7caac130],
::deep .el-input-number--small .el-input-number__increase[data-v-7caac130] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-7caac130] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-7caac130] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-7caac130] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-7caac130] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-7caac130],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-7caac130] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-7caac130] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-7caac130]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-7caac130] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-7caac130] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-7caac130] {
  border: 0
}

::deep .el-input__suffix[data-v-7caac130] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-7caac130] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-7caac130] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-7caac130] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-7caac130] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-7caac130] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-7caac130] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-7caac130] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-7caac130] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-7caac130] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-7caac130] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-7caac130] {
  position: relative
}

.color-picker .color-row[data-v-7caac130] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-7caac130] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-7caac130] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-7caac130] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-7caac130] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-7caac130] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-7caac130] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-7caac130] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-7caac130] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-7caac130] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-7caac130] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-7caac130] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-7caac130] {
  flex: 1
}

::deep .input-number-box[data-v-0a0658c3] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-0a0658c3] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-0a0658c3] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-0a0658c3] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-0a0658c3],
::deep .el-input-number--small .el-input-number__increase[data-v-0a0658c3] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-0a0658c3] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-0a0658c3] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-0a0658c3] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-0a0658c3] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-0a0658c3],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-0a0658c3] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-0a0658c3] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-0a0658c3]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-0a0658c3] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-0a0658c3] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-0a0658c3] {
  border: 0
}

::deep .el-input__suffix[data-v-0a0658c3] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-0a0658c3] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-0a0658c3] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-0a0658c3] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-0a0658c3] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-0a0658c3] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-0a0658c3] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-0a0658c3] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-0a0658c3] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-0a0658c3] {
  position: relative
}

.color-picker .color-row[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-0a0658c3] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-0a0658c3] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-0a0658c3] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-0a0658c3] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-0a0658c3] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-0a0658c3] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-0a0658c3] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-0a0658c3] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-0a0658c3] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-0a0658c3] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-0a0658c3] {
  flex: 1
}

::deep .input-number-box[data-v-19ddd330] {
  position: relative;
  height: 26px;
  overflow: hidden
}

::deep .input-number-box .unit[data-v-19ddd330] {
  position: absolute;
  top: 50%;
  right: 25px;
  line-height: 26px;
  transform: translateY(-50%)
}

::deep .input-number-box .el-input-number--small .el-input__inner[data-v-19ddd330] {
  padding-right: 20px;
  margin-right: 20px
}

::deep .el-input-number--small[data-v-19ddd330] {
  width: 120px;
  height: 26px;
  line-height: 26px;
  padding: 1px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  border-radius: 4px
}

::deep .el-input-number--small .el-input-number__decrease[data-v-19ddd330],
::deep .el-input-number--small .el-input-number__increase[data-v-19ddd330] {
  width: 20px
}

::deep .el-input-number--small .el-input__inner[data-v-19ddd330] {
  padding-left: 0;
  padding-right: 0;
  background-color: #f2f2f2;
  height: 22px;
  line-height: 22px;
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 4px
}

::deep .el-input-number__decrease[data-v-19ddd330] {
  background-color: #fff;
  border-right: 0;
  line-height: 22px
}

::deep .el-input-number__increase[data-v-19ddd330] {
  background-color: #fff;
  border-left: 0;
  line-height: 22px
}

::deep .el-input-number .el-input[data-v-19ddd330] {
  display: flex
}

::deep .sub-checkbox .el-checkbox__input.is-checked .el-checkbox__inner[data-v-19ddd330],
::deep .sub-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner[data-v-19ddd330] {
  background-color: #fff !important;
  border-color: #f86e04 !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-19ddd330] {
  border: 1px solid #dcdfe6 !important;
  background-color: #fff !important
}

::deep .sub-checkbox .el-checkbox__inner[data-v-19ddd330]:after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #f86e04;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform .15s ease-in .05s;
  transform-origin: center
}

::deep .el-select[data-v-19ddd330] {
  width: 120px;
  height: 26px;
  box-sizing: border-box;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #dcdfe6
}

::deep .el-input--small .el-input__inner[data-v-19ddd330] {
  height: 22px;
  line-height: 22px;
  background-color: #f2f2f2;
  margin-right: 20px;
  box-sizing: border-box;
  padding: 0 1px
}

::deep .el-input__inner[data-v-19ddd330] {
  border: 0
}

::deep .el-input__suffix[data-v-19ddd330] {
  width: 20px;
  right: 0
}

::deep .el-input--small[data-v-19ddd330] {
  display: flex
}

::deep .el-input--small .el-input__icon[data-v-19ddd330] {
  line-height: 22px;
  height: 22px;
  width: 20px
}

.list[data-v-19ddd330] {
  box-sizing: border-box;
  height: calc(100vh - 100px);
  overflow-x: hidden
}

.list .section[data-v-19ddd330] {
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f6
}

.title-bar[data-v-19ddd330] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px
}

.title-bar .left-title[data-v-19ddd330] {
  display: flex;
  align-items: center;
  cursor: pointer
}

.title-bar .left-title .left-arrows[data-v-19ddd330] {
  transition: all .3s
}

.title-bar .left-title .left-arrows.on[data-v-19ddd330] {
  color: #f86e04;
  transform: rotate(90deg)
}

.title-bar .left-title .title[data-v-19ddd330] {
  font-size: 16px;
  margin-left: 2px
}

.set-box[data-v-19ddd330] {
  padding: 0 10px 0 25px;
  display: flex;
  flex-direction: column
}

.color-picker[data-v-19ddd330] {
  position: relative
}

.color-picker .color-row[data-v-19ddd330] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.color-picker .color-row .bgcolor[data-v-19ddd330] {
  width: 98px;
  height: 100%;
  border-radius: 4px
}

.color-picker .color-row .arrows[data-v-19ddd330] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.empty-picker[data-v-19ddd330] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box
}

.empty-picker .txt[data-v-19ddd330] {
  width: 98px;
  height: 100%;
  background-color: #f2f2f2;
  border-radius: 4px;
  text-align: center
}

.empty-picker .arrows[data-v-19ddd330] {
  width: 20px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center
}

.up-pic[data-v-19ddd330] {
  display: flex;
  align-items: center;
  width: 120px;
  height: 26px;
  padding: 1px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden
}

.up-pic .img[data-v-19ddd330] {
  width: 100%;
  height: 100%;
  border-radius: 4px
}

.set-item-row[data-v-19ddd330] {
  padding-top: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between
}

.set-item-row .label[data-v-19ddd330] {
  flex-shrink: 0;
  text-align: right;
  color: grey
}

.set-item-row .up-btn[data-v-19ddd330] {
  color: #f86e04;
  cursor: pointer
}

.h-shuxing-page[data-v-19ddd330] {
  height: 100%;
  display: flex;
  flex-direction: column
}

.h-shuxing-page .list[data-v-19ddd330] {
  flex: 1
}

.pw-right-all[data-v-d44e5bba] {
  flex-shrink: 0;
  display: flex;
  height: 100%
}

.pw-right-box[data-v-d44e5bba] {
  height: 100%
}

.pw-right-bar[data-v-d44e5bba] {
  flex-shrink: 0;
  width: 60px;
  height: 100%;
  border-left: 1px solid #ebeef5;
  color: #5b5b5b;
  background-color: #fff;
  z-index: 2
}

.pw-right-bar .title[data-v-d44e5bba] {
  text-align: center;
  height: 40px;
  line-height: 40px;
  background-color: #f7f7f7
}

.pw-right-bar .item[data-v-d44e5bba] {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 15px 0;
  cursor: pointer;
  margin: 0 4px;
  box-sizing: border-box
}

.pw-right-bar .item[data-v-d44e5bba]:last-child {
  border-top: 1px solid #e6e6e6
}

.pw-right-bar .item.on .txt[data-v-d44e5bba] {
  color: #ff7926
}

.mapped[data-v-d44e5bba] {
  color: #3390ff
}

.mapping-name[data-v-d44e5bba] {
  color: #f86e04
}

.pw-status-bar[data-v-bc15e642] {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
  background-color: #fff;
  height: 40px;
  padding: 4px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  box-shadow: 0 0 24px 0 hsla(0, 0%, 42%, .1);
  text-align: center;
  color: #666;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  transition: all .4s ease
}

.pw-status-bar .left-bar[data-v-bc15e642] {
  display: flex;
  overflow: hidden;
  flex-shrink: 0
}

.pw-status-bar .left-bar .item[data-v-bc15e642] {
  width: 110px;
  min-width: 70px;
  border-right: 1px solid #e6e6e6
}

.pw-status-bar .right-bar[data-v-bc15e642] {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  overflow: hidden
}

.pw-status-bar .right-bar .item[data-v-bc15e642] {
  display: flex;
  align-items: center;
  width: 110px;
  cursor: pointer
}

.pw-status-bar .right-bar .item .txt[data-v-bc15e642] {
  margin-left: 10px
}

.pw-status-bar .right-bar .item[data-v-bc15e642]:hover {
  color: #ff7926
}

.pw-status-bar .scale[data-v-bc15e642] {
  border-left: 1px solid #e6e6e6;
  display: flex;
  align-items: center
}

.pw-status-bar .scale .percent[data-v-bc15e642] {
  padding: 0 10px;
  width: 70px;
  box-sizing: border-box;
  text-align: right
}

.pw-status-bar .scale .slider-box[data-v-bc15e642] {
  width: 150px
}

.pw-status-bar .scale .h-svg-icon-box[data-v-bc15e642] {
  cursor: pointer;
  transition: all .2s
}

.pw-status-bar .scale .h-svg-icon-box[data-v-bc15e642]:hover {
  transform: scale(1.1);
  color: #ff7926
}

.pw-status-bar .scale>div[data-v-bc15e642] {
  margin: 0 2px
}

@media screen and (max-width: 1400px) {
  [data-v-bc15e642] .pw-status-bar .left-bar .item {
    width: 100px !important
  }

  [data-v-bc15e642] .pw-status-bar .right-bar .scale .slider-box {
    width: 120px !important
  }

  [data-v-bc15e642] .el-pagination .el-pagination__jump {
    margin-left: 0
  }
}

.cropper[data-v-6f603704] {
  height: 480px
}

.pointer-events {
  opacity: .3
}

.disabled__el {
  pointer-events: none;
  opacity: .3
}

.disabled__el.no-opacity {
  opacity: 1
}

.el-message.offset {
  right: 250px;
  min-width: 100px;
  left: auto
}

.flex[data-v-1ce89d73] {
  display: flex
}

.flex-row[data-v-1ce89d73] {
  flex-direction: row
}

.flex-column[data-v-1ce89d73] {
  flex-direction: column
}

.flex-center[data-v-1ce89d73] {
  align-items: center
}

.flex1[data-v-1ce89d73] {
  flex: 1
}

.pointer-events[data-v-1ce89d73] {
  pointer-events: none
}

.disabled__el[data-v-1ce89d73] {
  pointer-events: none;
  opacity: .3
}

.disabled__el.no-opacity[data-v-1ce89d73] {
  opacity: 1
}

[data-v-1ce89d73] .slider-box .el-slider__runway {
  height: 2px !important
}

[data-v-1ce89d73] .slider-box .el-slider__bar {
  height: 2px !important;
  background-color: #787878
}

[data-v-1ce89d73] .slider-box .el-slider__button-wrapper {
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px
}

[data-v-1ce89d73] .slider-box .el-slider__button {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  box-shadow: 0 0 3px 1px hsla(0, 0%, 42%, .17)
}

[data-v-1ce89d73] .slider-box .el-slider__button-wrapper .el-tooltip,
[data-v-1ce89d73] .slider-box .el-slider__button-wrapper:after {
  display: block
}

[data-v-1ce89d73] .toggle-left-side .inner {
  box-sizing: content-box
}

.pw-container[data-v-1ce89d73] {
  height: 100vh;
  display: flex;
  background: #fff;
  overflow: hidden;
  position: relative
}

.pw-container .pw-side[data-v-1ce89d73] {
  padding-top: 4px
}

.pw-container .pw-body[data-v-1ce89d73] {
  width: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #ebeef5
}

.pw-container .pw-body .pw-content[data-v-1ce89d73] {
  flex-grow: 1;
  display: flex;
  margin-top: 15px;
  height: calc(100vh - 81px);
  box-sizing: border-box;
  overflow: hidden
}

.pw-container .pw-body .pw-content .content-left[data-v-1ce89d73] {
  flex-grow: 1;
  display: flex;
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 40px;
  position: relative
}

.pw-container .pw-body .pw-content .content-left .content-left-top[data-v-1ce89d73] {
  flex-grow: 1;
  display: flex
}

.pw-container .pw-body .pw-content .content-left .status-bar[data-v-1ce89d73] {
  flex-shrink: 0;
  overflow: hidden
}

.pw-container .pw-body .pw-content .content-right[data-v-1ce89d73] {
  flex-shrink: 0;
  height: 100%;
  padding-left: 10px
}

.pw-container .pw-tool-bar[data-v-1ce89d73] {
  flex-shrink: 0;
  position: relative;
  height: 80px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
  overflow-y: hidden;
  background-color: #fff
}

.pw-container .pw-center[data-v-1ce89d73] {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 0;
  overflow: auto
}

.pw-container .pw-center .pw-main[data-v-1ce89d73] {
  background: #ebeef5;
  overflow: auto
}

.pw-container .pw-center .pw-main.pointer-events[data-v-1ce89d73] {
  opacity: 1
}

.pw-container .pw-center .pw-main[data-v-1ce89d73]::-webkit-scrollbar {
  display: none
}

.pw-container .pw-center .pw-main .paging-tools[data-v-1ce89d73] {
  text-align: center;
  padding: 20px
}

.pw-container .pw-center .pw-main .page[data-v-1ce89d73] {
  margin: 0 auto;
  margin-bottom: 10px;
  box-shadow: 0 7px 21px 0 rgba(0, 0, 0, .12);
  background: #fff;
  transform-origin: 50% 0;
  box-sizing: border-box
}

.pw-container .pw-center .pw-main .page .pag-main[data-v-1ce89d73] {
  display: flex;
  border: 1px solid #000;
  height: 100%
}

.pw-container .pw-center .pw-main .page .page-head[data-v-1ce89d73] {
  border-right: 1px solid #000;
  box-sizing: border-box;
  padding: 2pt
}

.pw-container .pw-center .pw-main .page .page-head .title[data-v-1ce89d73] {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  position: relative;
  box-sizing: border-box;
  border: 1px dashed #000
}

.pw-container .pw-center .pw-main .page .page-head .title .txt[data-v-1ce89d73] {
  font-size: 32px;
  font-family: "宋体";
  text-align: center;
  padding: 20px 0;
  width: 32px;
  word-break: break-word
}

.pw-container .pw-center .pw-main .page .page-head .title .block[data-v-1ce89d73] {
  width: 100%;
  height: 20px;
  background: #000
}

.pw-container .pw-center .pw-main .page .page-head .title .page-no[data-v-1ce89d73] {
  font-size: 20px
}

.pw-container .pw-center .pw-main .page .page-head .title .clan-title[data-v-1ce89d73] {
  font-weight: 700
}

.pw-container .pw-center .pw-main .page .page-head .title .tips[data-v-1ce89d73] {
  position: relative;
  flex: 1;
  display: flex;
  writing-mode: vertical-rl;
  transform-origin: 50% 0;
  word-break: keep-all;
  height: 0;
  overflow: hidden
}

.pw-container .pw-center .pw-main .page .page-head .title .tips .txt-in[data-v-1ce89d73] {
  position: absolute;
  left: 50%;
  top: 20px;
  transform-origin: center top;
  white-space: nowrap
}

.pw-container .pw-center .pw-main .page .page-content[data-v-1ce89d73] {
  box-sizing: border-box;
  position: relative;
  flex-shrink: 0
}

.pw-container .pw-center .pw-main .page .page-content .item-container[data-v-1ce89d73] {
  box-sizing: border-box;
  position: relative;
  height: 100%;
  width: 100%
}

.pw-container .pw-right[data-v-1ce89d73] {
  flex-shrink: 0
}

.select-box[data-v-1ce89d73] {
  position: absolute;
  z-index: -1;
  border: 1px dashed #aeb5c0 !important
}

.select-box-tools[data-v-1ce89d73] {
  width: 21px;
  position: absolute;
  z-index: 999;
  padding: 2px;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e3e3e3;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .12);
  color: rgba(0, 0, 0, .8);
  right: -30px;
  top: 0
}

.select-box-tools i[data-v-1ce89d73] {
  display: block;
  font-size: 21px;
  margin: 5px 0;
  cursor: pointer
}

.select-box-tools i[data-v-1ce89d73]:hover {
  background: rgba(0, 0, 0, .04)
}

#test-mask-pw[data-v-1ce89d73] {
  position: fixed;
  width: 200px;
  height: 200px;
  right: 400px;
  top: 200px;
  background-color: rgba(255, 0, 0, .5);
  pointer-events: none
}
</style>
<style scoped lang="less">
.pw-container .pw-center .pw-main .page[data-v-1ce89d73] {
  margin: 0 auto;
  margin-bottom: 10px;
  box-shadow: 0 7px 21px 0 rgba(0, 0, 0, .12);
  background: #fff;
  transform-origin: 50% 0;
  box-sizing: border-box
}
</style>
<style scoped>
.lastJustify div:last-child {
    -moz-text-align-last: justify!important;
    text-align-last: justify!important
}

.text-container[data-v-2bb504ce] {
    display: flex;
    height: 100%;
    width: 100%;
    cursor: move;
    position: relative
}

.text-container .line[data-v-2bb504ce] {
    position: absolute;
    width: 100%;
    border-top: 1px solid #000;
    top: 24px
}

.edit-text[data-v-2bb504ce] {
    outline: none;
    min-width: 10px;
    min-height: 10px;
    flex: 1;
    cursor: text
}

.placeholder[data-v-2bb504ce] {
    color: #d7d7d7!important
}

.svg[data-v-4d6d7ab6] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-4d6d7ab6] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-4d6d7ab6] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-4d6d7ab6] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.svg[data-v-30b67dde] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-30b67dde] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-30b67dde] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-30b67dde] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.svg[data-v-04ce3935] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-04ce3935] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-04ce3935] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-04ce3935] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2;
    left: 50%
}

.svg[data-v-696254ec] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-696254ec] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-696254ec] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-696254ec] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2;
    right: 50%
}

.svg[data-v-ba483f52] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-ba483f52] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-ba483f52] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-ba483f52] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.svg[data-v-6da076a5] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-6da076a5] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-6da076a5] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-6da076a5] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2;
    left: 3%;
    right: 3%
}

.svg[data-v-79e0187c] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-79e0187c] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-79e0187c] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-79e0187c] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.cursor-nwse-resize[data-v-79e0187c] {
    top: 100%
}

.svg[data-v-d3e9a402] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-d3e9a402] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-d3e9a402] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-d3e9a402] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.svg[data-v-6ec3658c] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-6ec3658c] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-6ec3658c] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-6ec3658c] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.svg[data-v-05858f3a] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-05858f3a] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-05858f3a] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-05858f3a] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2;
    right: auto
}

.h-image[data-v-6caf1e4a] {
    height: 100%;
    width: 100%;
    image-rendering: -webkit-optimize-contrast
}

.pw-h-line[data-v-222187b4] {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    cursor: move
}

.svg[data-v-b0c00794] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-b0c00794] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-b0c00794] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-b0c00794] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.h-rectangle[data-v-e601d7ee] {
    height: 100%;
    width: 100%;
    box-sizing: border-box
}

.h-rectangle.img-box[data-v-e601d7ee] {
    overflow: hidden
}

.h-rectangle img[data-v-e601d7ee],.svg[data-v-e8cb1800] {
    height: 100%;
    width: 100%
}

.svg[data-v-e8cb1800] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible
}

.svg .editor-cursor-shape[data-v-e8cb1800] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-e8cb1800] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-e8cb1800] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.svg[data-v-5bcfd538] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-5bcfd538] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-5bcfd538] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-5bcfd538] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.table[data-v-271bf1b6] {
    position: absolute;
    border: 1px dashed transparent
}

.table.border[data-v-271bf1b6] {
    border: 1px dashed #ff6200
}

.table .tools[data-v-271bf1b6] {
    position: absolute;
    padding: 8px;
    background: #fff;
    border-radius: 3px;
    border: 1px solid #e3e3e3;
    box-shadow: 0 2px 6px 0 rgba(0,0,0,.12);
    color: rgba(0,0,0,.8);
    top: -60px;
    display: flex;
    cursor: pointer
}

.table .tools .item[data-v-271bf1b6] {
    margin: 0 8px;
    display: flex;
    align-items: center
}

.table .cell[data-v-271bf1b6] {
    position: absolute;
    height: 100%;
    width: 100%
}

.table .cell.selected[data-v-271bf1b6] {
    background: hsla(0,0%,80%,.6)
}

.table .cell .content[data-v-271bf1b6] {
    height: calc(100% - 6px);
    width: calc(100% - 6px);
    overflow: hidden;
    margin: 3px;
    outline: none;
    cursor: text;
    box-sizing: border-box
}

.table .cell .line[data-v-271bf1b6] {
    position: absolute
}

.table .cell .line .l[data-v-271bf1b6] {
    position: absolute;
    z-index: 90
}

.table .cell .top-line[data-v-271bf1b6] {
    top: 0
}

.table .cell .bottom-line[data-v-271bf1b6] {
    bottom: 0
}

.table .cell .left-line[data-v-271bf1b6] {
    left: 0
}

.table .cell .right-line[data-v-271bf1b6] {
    right: 0
}

.table .cell .bottom-line[data-v-271bf1b6],.table .cell .top-line[data-v-271bf1b6] {
    width: 100%;
    left: 0;
    right: 0
}

.table .cell .bottom-line .l[data-v-271bf1b6],.table .cell .top-line .l[data-v-271bf1b6] {
    height: 10px;
    width: 100%;
    cursor: row-resize;
    top: -5px
}

.table .cell .left-line[data-v-271bf1b6],.table .cell .right-line[data-v-271bf1b6] {
    height: 100%;
    cursor: col-resize;
    top: 0;
    bottom: 0
}

.table .cell .left-line .l[data-v-271bf1b6],.table .cell .right-line .l[data-v-271bf1b6] {
    height: 100%;
    width: 10px;
    cursor: col-resize;
    left: -5px
}

.text-style-select-tool[data-v-271bf1b6] {
    padding: 0
}

.dropdown-list li[data-v-271bf1b6]:hover {
    background-color: rgba(0,0,0,.04)
}

.dropdown-list .item[data-v-271bf1b6] {
    cursor: pointer;
    padding: 8px;
    display: flex;
    align-items: center;
    position: relative
}

.dropdown-list .item .checked[data-v-271bf1b6] {
    position: absolute;
    left: 12px
}

.border-list[data-v-271bf1b6] {
    display: flex;
    width: 192px;
    flex-wrap: wrap
}

.border-list i[data-v-271bf1b6] {
    margin: 8px;
    cursor: pointer
}

.svg[data-v-51537770] {
    position: absolute;
    left: 0;
    top: 0;
    overflow: visible;
    width: 100%;
    height: 100%
}

.svg .editor-cursor-shape[data-v-51537770] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    box-sizing: border-box
}

.cursor-nwse-resize[data-v-51537770] {
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: #f5a623;
    border: 1px solid #fff;
    cursor: nwse-resize;
    transform: rotate(45deg) translate3d(-80%,0,0);
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0,0,0,.32);
    z-index: 1000000
}

.editor-txt-box[data-v-51537770] {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 7px 14px;
    margin: 0 auto;
    box-sizing: border-box;
    z-index: 2
}

.yl[data-v-6195c862] {
    position: relative;
    width: 100%;
    height: 100%
}

.yl .line[data-v-6195c862] {
    background: #000
}

.yl .item[data-v-6195c862] {
    position: absolute;
    display: flex;
    align-items: center;
    flex-direction: column
}

.yl .item .index[data-v-6195c862] {
    border: 1px solid #000;
    border-radius: 50%;
    font-size: 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center
}

.yl .item .line-v[data-v-6195c862] {
    width: 1px;
    position: absolute;
    height: 100%
}

.yl .item .line-v.lr[data-v-6195c862] {
    right: 0
}

.yl .item .line-v.rl[data-v-6195c862] {
    left: 0
}

.yl .item .line-h[data-v-6195c862] {
    bottom: 0;
    height: 1px;
    position: absolute
}

.yl .item .line-h.lr[data-v-6195c862] {
    right: 0
}

.yl .item .line-h.rl[data-v-6195c862] {
    left: 0
}

.yl .item .line-h-2[data-v-6195c862] {
    top: 0;
    height: 1px;
    position: absolute
}

.yl .item .line-h-2.lr[data-v-6195c862] {
    left: 0
}

.yl .item .line-h-2.rl[data-v-6195c862] {
    right: 0
}

.yl .item .content[data-v-6195c862] {
    display: flex;
    position: relative
}

.yl .item .content .name[data-v-6195c862] {
    width: 19px;
    overflow: hidden;
    position: relative
}

.yl .item .content .name i[data-v-6195c862] {
    position: absolute;
    display: none;
    cursor: pointer;
    color: #f86e04
}

.yl .item .content .name i.t[data-v-6195c862] {
    top: 0;
    left: 3px
}

.yl .item .content .name i.b[data-v-6195c862] {
    bottom: 0;
    left: 3px
}

.yl .item .content .name i.d[data-v-6195c862] {
    left: -5px;
    top: 39px
}

.yl .item .content .name:hover i[data-v-6195c862] {
    display: inline
}

.yl .item .content .intro[data-v-6195c862] {
    font-size: 10px;
    overflow: hidden;
    position: absolute;
    height: 126px;
    width: 40px;
    top: -5px
}

.yl .item .content .intro.lr[data-v-6195c862] {
    left: 18px
}

.yl .item .content .intro.rl[data-v-6195c862] {
    left: -39px
}

.yl .next-page-line[data-v-6195c862] {
    height: 1px;
    top: 0
}

.yl .next-page-line.rl[data-v-6195c862] {
    left: 0;
    position: absolute
}

.yl .next-page-line.lr[data-v-6195c862] {
    right: 0;
    position: absolute
}

.yl .pre-page-line[data-v-6195c862] {
    height: 1px;
    top: 0
}

.yl .pre-page-line.rl[data-v-6195c862] {
    right: 0;
    position: absolute
}

.yl .pre-page-line.lr[data-v-6195c862] {
    left: 0;
    position: absolute
}

.vdr[data-v-49241710] {
    touch-action: none;
    position: absolute;
    box-sizing: border-box;
    border: 1px dashed #d6d6d6
}

.handle[data-v-49241710] {
    box-sizing: border-box;
    position: absolute;
    width: 8px;
    height: 8px;
    background: #fff;
    border: 1px solid #333;
    box-shadow: 0 0 2px #bbb
}

.nw-resize[data-v-49241710] {
    cursor: nw-resize
}

.n-resize[data-v-49241710] {
    cursor: n-resize
}

.ne-resize[data-v-49241710] {
    cursor: ne-resize
}

.w-resize[data-v-49241710] {
    cursor: w-resize
}

.e-resize[data-v-49241710] {
    cursor: e-resize
}

.sw-resize[data-v-49241710] {
    cursor: sw-resize
}

.s-resize[data-v-49241710] {
    cursor: s-resize
}

.se-resize[data-v-49241710] {
    cursor: se-resize
}

.handle-tl[data-v-49241710] {
    top: -5px;
    left: -5px;
    cursor: nw-resize
}

.handle-tm[data-v-49241710] {
    top: -5px;
    left: calc(50% - 4px);
    cursor: n-resize
}

.handle-tr[data-v-49241710] {
    top: -5px;
    right: -5px;
    cursor: ne-resize
}

.handle-ml[data-v-49241710] {
    top: calc(50% - 4px);
    left: -5px;
    cursor: w-resize
}

.handle-mr[data-v-49241710] {
    top: calc(50% - 4px);
    right: -5px;
    cursor: e-resize
}

.handle-bl[data-v-49241710] {
    bottom: -5px;
    left: -5px;
    cursor: sw-resize
}

.handle-bm[data-v-49241710] {
    bottom: -5px;
    left: calc(50% - 4px);
    cursor: s-resize
}

.handle-br[data-v-49241710] {
    bottom: -5px;
    right: -5px;
    cursor: se-resize
}

.ref-line[data-v-49241710] {
    position: absolute;
    background-color: #f0c;
    z-index: 9999
}

.v-line[data-v-49241710] {
    width: 1px
}

.h-line[data-v-49241710] {
    height: 1px
}

@media only screen and (max-width: 768px) {
    [class*=handle-][data-v-49241710]:before {
        content:"";
        left: -10px;
        right: -10px;
        bottom: -10px;
        top: -10px;
        position: absolute
    }
}

.rotate[data-v-49241710] {
    position: absolute;
    height: 15px;
    width: 15px;
    background: red;
    background-color: #ff6200;
    border-radius: 100%;
    top: -25px;
    left: calc(50% - 7px);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    cursor: pointer;
    cursor: url(data:image/png;base64,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) 5 5,auto
}

.rotate[data-v-49241710]:after {
    content: "îœ";
    position: absolute;
    width: 100%;
    height: 100%;
    transform: scale(.7);
    color: #fff
}

.vdr {
    touch-action: none;
    border: 1px dashed #d6d6d6
}

.handle,.vdr {
    position: absolute;
    box-sizing: border-box
}

.handle {
    width: 8px;
    height: 8px;
    background: #fff;
    border: 1px solid #333;
    box-shadow: 0 0 2px #bbb
}

.nw-resize {
    cursor: nw-resize
}

.n-resize {
    cursor: n-resize
}

.ne-resize {
    cursor: ne-resize
}

.w-resize {
    cursor: w-resize
}

.e-resize {
    cursor: e-resize
}

.sw-resize {
    cursor: sw-resize
}

.s-resize {
    cursor: s-resize
}

.se-resize {
    cursor: se-resize
}

.handle-tl {
    top: -5px;
    left: -5px;
    cursor: nw-resize
}

.handle-tm {
    top: -5px;
    left: calc(50% - 4px);
    cursor: n-resize
}

.handle-tr {
    top: -5px;
    right: -5px;
    cursor: ne-resize
}

.handle-ml {
    top: calc(50% - 4px);
    left: -5px;
    cursor: w-resize
}

.handle-mr {
    top: calc(50% - 4px);
    right: -5px;
    cursor: e-resize
}

.handle-bl {
    bottom: -5px;
    left: -5px;
    cursor: sw-resize
}

.handle-bm {
    bottom: -5px;
    left: calc(50% - 4px);
    cursor: s-resize
}

.handle-br {
    bottom: -5px;
    right: -5px;
    cursor: se-resize
}

.ref-line {
    position: absolute;
    background-color: #f0c;
    z-index: 9999
}

.v-line {
    width: 1px
}

.h-line {
    height: 1px
}

@media only screen and (max-width: 768px) {
    [class*=handle-]:before {
        content:"";
        left: -10px;
        right: -10px;
        bottom: -10px;
        top: -10px;
        position: absolute
    }
}

.rotate {
    position: absolute;
    height: 15px;
    width: 15px;
    background: red;
    background-color: #ff6200;
    border-radius: 100%;
    top: -25px;
    left: calc(50% - 7px);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    cursor: pointer;
    cursor: url(data:image/png;base64,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) 5 5,auto
}

.rotate:after {
    content: "îœ";
    position: absolute;
    width: 100%;
    height: 100%;
    transform: scale(.7);
    color: #fff
}

.vdr {
    cursor: move;
    border-color: transparent!important
}

.vdr .handle {
    border-color: #ff6200;
    z-index: 99
}

.vdr.active {
    border-color: #ff6200!important
}

.vdr.active.no-border {
    border-color: transparent!important
}

.vdr.active .item-tools {
    display: block
}

.com-item-box {
    display: flex;
    height: 100%;
    width: 100%;
    box-sizing: border-box
}

.pointer-evetns {
    pointer-events: none
}

.lock {
    border-color: #ccc!important
}
</style>
