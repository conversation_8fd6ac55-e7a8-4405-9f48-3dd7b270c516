import request from '@/utils/request'

const dynamicPre = ':clanId'
const dynamicPedgreeId = ':pedigreeId'
const dynamicTagType = ':tagType'
const dynamicBranchId = ':branchId'
const pxApi = {
  AddPedigree: '/web/family/:clanId/addPedigree',
  UpdatePedigree: '/web/family/:clanId/:pedigreeId/save',
  GetPedigreeList: `/web/family/:clanId/getPedigreeList`,
  DeletedPedigree: '/web/family/:clanId/deletedPedigree',
  AddAncestor: `/web/family/:clanId/:pedigreeId/addAncestor`,
  AddMemberChild: `/web/family/:clanId/:pedigreeId/addMemberChild`,
  AddWife: `/web/family/:clanId/:pedigreeId/addWife`,
  DeletedMember: '/web/family/:clanId/:pedigreeId/deletedMember',
  GetPedigreeMemberList: '/web/family/:clanId/:pedigreeId/getPedigreeMemberList',
  GetMemberDetail: '/web/family/:clanId/:pedigreeId/memberDetail',
  SaveMemberInfo: '/web/family/:clanId/:pedigreeId/saveMemberInfo',
  GetMemberList: '/web/family/:clanId/:pedigreeId/getMemberList',
  MoveMember: '/web/family/:clanId/:pedigreeId/moveMember',
  GetTagTypeList: '/web/family/:clanId/:pedigreeId/:tagType/list',
  SaveMemberBranch: '/web/family/:clanId/:pedigreeId/saveMemberBranch',
  DeletedBranch: '/web/family/:clanId/:pedigreeId/:branchId/deleted'
}

/**
 * 添加谱系
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {string} parameter.name - 谱系名称
 * @param {string} parameter.first_name - 谱系姓氏
 * @param {string} [parameter.hall_name] - 堂号
 * @param {string} parameter.province - 省份
 * @param {string} parameter.city - 城市
 * @param {string} parameter.district - 区域
 * @returns {Promise<object>}
 */
export async function addPedigree (parameter) {
  return request({
    url: pxApi.AddPedigree.replace(dynamicPre, parameter.clan_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 修改谱系
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {string} parameter.name - 谱系名称
 * @param {string} parameter.first_name - 谱系姓氏
 * @param {string} [parameter.hall_name] - 堂号
 * @param {string} parameter.province - 省份
 * @param {string} parameter.city - 城市
 * @param {string} parameter.district - 区域
 * @returns {Promise<object>}
 */
export async function updatePedigree (parameter) {
  return request({
    url: pxApi.UpdatePedigree.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 删除谱系
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.id - 谱系id
 * @returns {Promise<object>}
 */
export async function deletedPedigree (parameter) {
  return request({
    url: pxApi.DeletedPedigree.replace(dynamicPre, parameter.clan_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 获取谱系列表
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @returns {Promise<object>}
 */
export async function getPedigreeList (parameter) {
  return request({
    url: pxApi.GetPedigreeList.replace(dynamicPre, parameter.clan_id),
    method: 'get',
    data: parameter
  })
}

/**
 * 添加始祖，谱系第一人
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} parameter.generation - 世别层级
 * @param {string} parameter.first_name - 姓氏
 * @param {string} parameter.name - 名
 * @param {string} parameter.full_name - 全名
 * @param {number} parameter.ranking - 排行
 * @param {string} parameter.ranking_text - 排行名称
 * @param {string} parameter.relation - 关系
 * @param {'男'|'女'} parameter.sex - 性别
 * @returns {Promise<object>}
 */
export async function addAncestor (parameter) {
  return request({
    url: pxApi.AddAncestor.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 添加谱系成员的子女信息
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} parameter.father_id - 父亲id
 * @param {number} parameter.generation - 世别层级
 * @returns {Promise<object>}
 */
export async function addMemberChild (parameter) {
  return request({
    url: pxApi.AddMemberChild.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 删除谱系成员
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} parameter.member_id - 需要删除的成员id
 * @returns {Promise<object>}
 */
export async function deletedMember (parameter) {
  return request({
    url: pxApi.DeletedMember.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 添加谱系成员的妻子信息
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} parameter.husband_id - 丈夫id
 * @param {number} parameter.generation - 世别层级
 * @returns {Promise<object>}
 */
export async function addWife (parameter) {
  return request({
    url: pxApi.AddWife.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id),
    method: 'post',
    data: parameter
  })
}

/**
 * 获取谱系成员树状结构
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} [parameter.generation] - tree对应的世别层级(1-5,建议3)
 * @returns {Promise<object>}
 */
export async function getPedigreeMemberList (parameter) {
  const generation = parameter.generation || 3
  return request({
    url: pxApi.GetPedigreeMemberList.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id) + `?generation=${generation}&rd=` + Math.random(),
    method: 'get',
    data: parameter
  })
}

/**
 * 家族成员详情明细
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} parameter.id - 成员id
 * @param {number} parameter.father_id - 父级id
 * @returns {Promise<object>}
 */
export async function getMemberDetail (parameter) {
  const bseUrl = pxApi.GetMemberDetail.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id)
  const paramsUrl = `?id=${parameter.id}&father_id=${parameter.father_id}`
  return request({
    url: bseUrl + paramsUrl,
    method: 'get',
    data: parameter
  })
}

/**
 * 保存成员扩展信息
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} parameter.id - 成员id
 * @returns {Promise<object>}
 */
export async function saveMemberInfo (parameter) {
  const bseUrl = pxApi.SaveMemberInfo.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id)
  return request({
    url: bseUrl,
    method: 'post',
    data: parameter
  })
}

/**
 * 获取谱系成员列表（用于搜索+按世系获取数据使用场景）
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} [parameter.search_generation]
 * @param {string} [parameter.search_name]
 * @param {number} [parameter.page]
 * @param {number} [parameter.pageSize]
 * @param {boolean} [parameter.pagination]
 * @returns {Promise<object>}
 */
export async function getMemberList (parameter) {
  const bseUrl = pxApi.GetMemberList.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id)
  const paramsUrl = `?search_generation=${parameter.search_generation}&search_name=${parameter.search_name}&page=${parameter.page || 1}&pageSize=${parameter.pageSize || 10}&pagination=${parameter.pagination || true}`
  return request({
    url: bseUrl + paramsUrl,
    method: 'get',
    data: parameter
  })
}

/**
 * 移动谱系成员（过继场景）
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} parameter.member_id - 需要移动的成员id
 * @param {'嗣'|'祧'} parameter.relation - 与目标的关系
 * @param {number} parameter.target_id - 目标成员id
 * @returns {Promise<object>}
 */
export async function moveMember (parameter) {
  const bseUrl = pxApi.MoveMember.replace(dynamicPre, parameter.clan_id).replace(dynamicPedgreeId, parameter.pedigree_id)
  return request({
    url: bseUrl,
    method: 'post',
    data: parameter
  })
}

/**
 * 通过类型获取分房列表
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {1|2|3|4} parameter.tag_type - 分房tag (用于URL)
 * @returns {Promise<object>}
 */
export async function getTagTypeList (parameter) {
  const bseUrl = pxApi.GetTagTypeList
    .replace(dynamicPre, parameter.clan_id)
    .replace(dynamicPedgreeId, parameter.pedigree_id)
    .replace(dynamicTagType, parameter.tag_type)
  return request({
    url: bseUrl,
    method: 'get',
    data: parameter
  })
}

/**
 * 保存成员分房信息
 * @param {object} parameter - 请求体
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} [parameter.branch_id] - 分房id,为0时则为新增
 * @param {string} parameter.name - 名字
 * @param {number} parameter.clan_member_id - 成员id
 * @param {string} parameter.clan_member_name - 成员的名字
 * @param {1|2|3|4} parameter.branch_tag - 分房tag
 * @param {string} parameter.ancestor - 谱系祖先名
 * @returns {Promise<object>}
 */
export async function saveMemberBranch (parameter) {
  const bseUrl = pxApi.SaveMemberBranch
    .replace(dynamicPre, parameter.clan_id)
    .replace(dynamicPedgreeId, parameter.pedigree_id)
  return request({
    url: bseUrl,
    method: 'post',
    data: parameter
  })
}

/**
 * 通过id删除分房功能数据
 * @param {object} parameter - 请求参数
 * @param {number} parameter.clan_id - 族谱ID (用于URL)
 * @param {number} parameter.pedigree_id - 谱系ID (用于URL)
 * @param {number} parameter.branch_id - 分房ID (用于URL)
 * @returns {Promise<object>}
 */
export async function deletedBranch (parameter) {
  const bseUrl = pxApi.DeletedBranch
    .replace(dynamicPre, parameter.clan_id)
    .replace(dynamicPedgreeId, parameter.pedigree_id)
    .replace(dynamicBranchId, parameter.branch_id)
  const paramsUrl = `?branch_id=${parameter.branch_id}`
  return request({
    url: bseUrl + paramsUrl,
    method: 'get',
    data: parameter
  })
}
