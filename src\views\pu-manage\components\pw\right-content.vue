<template>
  <div class="right">
    <div class="pages">
      <div
        class="page"
        v-for="page in pageList"
        :key="page.pageNum"
        :class="page.pageNum === currentPageNum?'selected':''"
        @click="handleSelected(page)"
      >
        <div class="num">{{ page.pageNum }}</div>
        <div class="content">内容</div>
        <div class="opt hidden">
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxinjian c-pointer-hover" @click="handleOpt('add-next')"></i>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarfuzhi- c-pointer-hover" @click="handleOpt('copy')"></i>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxiayi c-pointer-hover" @click="handleOpt('down')"></i>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarshangyi c-pointer-hover" @click="handleOpt('up')"></i>
          <i class="cus-edit-toolbar-icon icon-edit-toolbaraixin c-pointer-hover" @click="handleOpt('shoucang')"></i>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover" @click="handleOpt('delete')"></i>
        </div>
      </div>
    </div>
    <div class="btn" @click="handleOpt('add-last')">新建页面</div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex'
export default {
  name: 'PwRightContent',
  data () {
    return {
      //
    }
  },
  computed: {
    ...mapState({
      currentPageNum: state => state.pw.currentPageNum,
      pageList: state => state.pw.data
    })
  },
  methods: {
    ...mapActions(['InsertPage', 'UpdateCurrentPageNum']),
    handleSelected (page) {
      const { UpdateCurrentPageNum } = this
      UpdateCurrentPageNum(page.pageNum)
    },
    handleOpt (type) {
      this.$emit('handleOpt', type)
      if (type === 'add-last') {
        this.InsertPage()
      } else if (type === 'add-next') {
        this.InsertPage()
      }
    }
  }
}
</script>
<style lang="less" scoped>
.right {
  width: 100%;
  background-color:#fff;
  display:flex;
  flex-direction:column;
  height: calc(100vh - 88px);
  .pages {
    flex:1;
    overflow-y:auto;
    .page {
      display:flex;
      flex-direction:row;
      margin-bottom: 12px;
      .num {
        width: 40px;
        align-content: flex-end;
        text-align:center;
      }
      .content {
        flex:1;
        border: 1px solid #dcdfe5;
        width: 150px;
        height: 220px;
      }
      .opt {
        width: 40px;
        display:flex;
        flex-direction:column;
        align-items: center;
        justify-content: space-between;
        i {
          font-size: 18px;
          cursor:pointer;
          flex:1;
        }
      }
      .opt.hidden {
       visibility:hidden;
      }
    }
    .page.selected {
      .content {
        border: 1px solid #ff7926;
      }
    }
  }
  .btn {
    box-shadow:0 -24px 24px 0 hsla(0,0%,42%,.05);
    text-align: center;
    height: 40px;
    line-height: 40px;
    cursor:pointer;
  }
}
</style>
