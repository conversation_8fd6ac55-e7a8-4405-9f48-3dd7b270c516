<template>
  <div>
    <div class="tab-base-info flex-direction-row">
      <div style="width: 104px">
        <div class="photo">
          <div class="default" />
          <div class="close">
            <i class="cus-edit-toolbar-icon icon-edit-toolbaricon-mianxing_fuzhi_guanbi" />
          </div>
          <div class="tips">点击更换</div>
        </div>
        <div class="flex justify-center align-center bind-account c-pointer-hover">绑定用户</div>
      </div>
      <div class="flex flex-1 flex-wrap">
        <div class="flex flex-direction-row">
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">姓氏</div><a-input v-model="data.first_name" @blur="handleSave('first_name')"/>
          </div>
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">名 字</div><a-input v-model="data.name" @blur="handleSave('name')"/>
          </div>
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">常用名</div><a-input v-model="data.real_name" style="width:120px" @blur="handleSave('real_name')" />
          </div>
          <div class="flex flex-direction-row align-center form-item">
            <div class="title">别 名</div><a-input v-model="data.other_name" style="width:120px" @blur="handleSave('other_name')" />
          </div>
        </div>
        <div class="flex flex-direction-row mt10">
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">性 别</div>
            <a-select
              style="width: 80px"
              v-model="data.sex"
              class="select-no-border"
              :disabled="!!data.husband_id"
              @change="handleSave('sex')">
              <a-select-option value="男">男</a-select-option>
              <a-select-option value="女">女</a-select-option>
            </a-select>
          </div>
          <!-- 主支成员显示父母关系 -->
          <div class="flex flex-direction-row align-center form-item mr8" v-if="!data.husband_id">
            <div class="title">父母</div>
            <a-select style="width: 90px;" v-model="data.father_id" class="select-no-border" noBorder :dropdownMatchSelectWidth="false">
              <a-select-option :value="item.id" :key="item.id" v-for="item in getParentsList">{{ item.name }}</a-select-option>
            </a-select>
            <a-select style="width: 70px" v-model="data.relation" class="select-no-border select-left-border" @change="handleSave('relation')">
              <a-select-option :value="item.id" v-for="item in relationList" :key="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </div>
          <!-- 配偶显示父亲字段 -->
          <div class="flex flex-direction-row align-center form-item mr8" v-if="data.husband_id">
            <div class="title">父亲</div>
            <a-input
              style="width: 160px"
              v-model="data.father_name"
              placeholder="请输入父亲姓名"
              @blur="handleSave('father_name')"
              @pressEnter="handleSave('father_name')" />
            <a-select style="width: 70px" v-model="data.ranking_text" class="select-no-border select-left-border" @change="handleSave('ranking_text')">
              <a-select-option :value="item" v-for="item in getCurrentRankingList" :key="item">{{ item }}</a-select-option>
            </a-select>
          </div>
          <!-- 配偶显示配偶关系 -->
          <div class="flex flex-direction-row align-center form-item mr8" v-if="data.husband_id">
            <div class="title">配偶关系</div>
            <a-select style="width: 160px" v-model="data.relation" class="select-no-border" @change="handleSave('relation')">
              <a-select-option :value="relation" v-for="relation in wifeConstantList" :key="relation">{{ relation }}</a-select-option>
            </a-select>
          </div>

          <div class="flex flex-direction-row align-center form-item mr8" v-if="!data.husband_id">
            <div class="title">排行</div>
            <a-select style="width: 86px" v-model="data.ranking_text" class="select-no-border" @change="handleSave('ranking_text')">
              <a-select-option :value="item" v-for="item in getCurrentRankingList" :key="item">{{ item }}</a-select-option>
            </a-select>
          </div>
          <div class="flex flex-direction-row align-center form-item" v-if="!data.husband_id">
            <div class="title">婚 姻</div>
            <a-select style="width: 74px" v-model="data.marital_status" class="select-no-border select-left-border" @change="handleSave('marital_status')">
              <a-select-option :value="item.value" v-for="item in marriageStatus" :key="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="flex flex-direction-row mt10">
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">学校</div><a-input style="width: 270px" v-model="data.school" @blur="handleSave('school')"/>
          </div>
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">学历</div>
            <a-select style="width: 270px" v-model="data.education" class="select-no-border select-left-border" @change="handleSave('education')">
              <a-select-option :value="item" v-for="item in educationList" :key="item">{{ item }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="flex flex-direction-row mt10">
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">单位</div><a-input style="width: 270px" v-model="data.company" @blur="handleSave('company')"/>
          </div>
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">职位</div><a-input style="width: 270px" v-model="data.job" @blur="handleSave('job')"/>
          </div>
        </div>
        <div class="flex flex-direction-row mt10">
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">生于</div><a-input style="width: 440px" v-model="data.birthday" @blur="handleSave('birthday')"/>
          </div>
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">状态</div>
            <a-select style="width: 100px" v-model="data.living_status" class="select-no-border select-left-border" @change="handleSave('living_status')">
              <a-select-option :value="item.id" v-for="item in livingStatusList" :key="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </div>
        </div>
        <!-- 已故相关信息 -->
        <div class="flex flex-direction-row mt10" v-show="data.living_status === 2">
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">殁于</div><a-input
              v-model="data.death_time"
              placeholder="请输入"
              style="width: 440px"
              @blur="handleSave('death_time')" />
          </div>
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">享年</div>
            <a-select
              v-model="data.age_at_death"
              style="width: 100px"
              class="select-no-border"
              @change="handleSave('age_at_death')">
              <a-select-option :value="age" v-for="age in ageOptionList" :key="age">{{ age }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="flex flex-direction-row mt10" v-show="data.living_status === 2">
          <div class="flex flex-direction-row align-center form-item mr8">
            <div class="title">葬地</div><a-input
              v-model="data.cemetery_address"
              style="width: 610px"
              @blur="handleSave('cemetery_address')" />
          </div>
        </div>
        <div class="flex flex-direction-row mt10">
          <div class="flex flex-direction-row align-center form-item mr8" style="height:56px">
            <div class="title" style="height:54px">简介</div>
            <a-textarea style="width: 610px;resize: none;" v-model="data.introduction" @blur="handleSave('introduction')"/>
          </div>
        </div>
      </div>
    </div>
    <div class="tab-base-info flex-direction-row" style="padding-top: 34px;">
      <div class="flex align-center w100p header">
        <div class="f16">子女信息</div>
        <!-- <div class="add ml12">+增加子女</div> -->
        <div class="ml12">共</div>
        <div>
          <a-select class="w100" v-model="getSonsCounts">
            <a-select-option :value="item" v-for="item in generationList" :key="item">{{ item }}</a-select-option>
          </a-select>
        </div>
        <div class="ml2">子</div>
        <div>
          <a-select class="w100" v-model="getDaughtersCounts">
            <a-select-option :value="item" v-for="item in generationList" :key="item">{{ item }}</a-select-option>
          </a-select>
        </div>
        <div class="ml2">女</div>
        <div class="flex-1">
          <div class="line"></div>
        </div>
      </div>
    </div>
    <div class="tab-base-info flex" style="padding-top: 8px">
      <children-table :source="childrenList" :nodeInfo="nodeInfo"/>
    </div>
    <div class="tab-base-info flex" style="padding-top: 12px">
      <div class="flex flex-direction-row align-center form-item flex-1" style="height:56px;background-color: #f5f7fa">
        <div class="title" style="height:54px">行传<br/>预览</div>
        <div class="flex-1 ml8">{{ showText }}</div>
      </div>
    </div>
    <div class="tab-base-info flex" style="padding-top: 12px; padding-bottom: 12px">
      <div class="flex flex-direction-row align-center form-item mr8">
        <!-- <div class="title">输出设置</div>
        <a-select style="width: 150px" class="select-no-border">
          <a-select-option :value="item.name" v-for="item in outputSetting" :key="item.id">{{ item.name }}</a-select-option>
        </a-select> -->
      </div>
    </div>
  </div>
</template>
<script>
import cloneDeep from 'lodash.clonedeep'
import { getRankText } from '@/utils/rankTextUtil'
import { mapActions, mapState } from 'vuex'
import { marriageStatus } from '@/constant/marriage-status'
import { educationList } from '@/constant/education'
import ChildrenTable from '@/views/pu-manage/components/px/components/detail-tabs/children-table'
import { outputSetting } from '@/constant/output-setting'
import { generationList } from '@/constant/generation'
import { rankingList } from '@/constant/ranking-list'
import { relationList } from '@/constant/relation-list'
import { wifeConstantList } from '@/constant/wife-constant'
import { femaleRankingList } from '@/constant/female-ranking-list'
import { livingStatusList } from '@/constant/living-status'
import { numberToChinese } from '@/utils/number2ChineseUtils'

export default {
  name: 'Tab1BaseInfo',
  components: { ChildrenTable },
  data () {
    return {
      marriageStatus,
      educationList,
      outputSetting,
      generationList,
      rankingList,
      relationList,
      wifeConstantList,
      livingStatusList,
      femaleRankingList,
      rankingOptionsList: [],
      ageOptionList: Array(151).fill(0).map((_, index) => index), // 享年选项列表 0-150
      data: {},
      childrenList: []
    }
  },
  props: {
    nodeInfo: {
      type: Object,
      default: () => {}
    },
    father: {
      type: Object,
      default: () => {}
    },
    showText: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    }),
    getParentsList () {
      const { id = -1, name, wife = [] } = this.father || {}
      const showName = name || '-'
      const wifeList = wife || []
      const options = [{ id, name: showName }]
      wifeList.forEach(item => {
        options.push({
          id: item.id,
          name: `${showName}|${item.name}`
        })
      })
      return options
    },
    getSonsCounts () {
      const len = this.childrenList.filter(item => item.sex === '男').length
      return len === 0 ? '无' : numberToChinese(len)
    },
    getDaughtersCounts () {
      const len = this.childrenList.filter(item => item.sex === '女').length
      return len === 0 ? '无' : numberToChinese(len)
    },
    getCurrentRankingList () {
      // 根据当前成员的性别返回对应的排行列表
      return this.data.sex === '女' ? this.femaleRankingList : this.rankingList
    }
  },
  watch: {
    nodeInfo: {
      handler (val) {
        if (val) {
          const copyData = cloneDeep(val)
          // 如果是配偶节点，性别固定为女性
          if (copyData.husband_id) {
            copyData.sex = '女'
          }
          // father_id为0时是始祖，id改成-1
          if (copyData.father_id === 0) {
            copyData.father_id = -1
          }
          this.data = copyData
          this.childrenList = copyData.children || []
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions(['SaveMemberInfo']),
    getRankingText (num) {
      return getRankText(num)
    },
    async handleSave (fieldName) {
      const { SaveMemberInfo, data, nodeInfo } = this
      if (!data?.id) {
        this.$message.error('请选择要保存的节点')
        return
      }

      // 检查值是否真的发生了变化
      const originalValue = nodeInfo[fieldName]
      const currentValue = data[fieldName]

      // 如果值没有变化，直接返回，不发起请求
      if (originalValue === currentValue) {
        return
      }

      // 如果是性别改变，自动更新排行文本
      if (fieldName === 'sex' && !data.husband_id) {
        this.updateRankingTextBySex()
      }

      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId,
        [fieldName]: data[fieldName],
        id: data.id
      }

      // 如果是性别改变，同时在接口中包含更新后的排行文本
      if (fieldName === 'sex' && !data.husband_id) {
        params.ranking_text = data.ranking_text
      }

      try {
        const res = await SaveMemberInfo(params)
        if (res.code === 0) {
          this.$message.success('保存成功')
          // 通知父组件更新世系树数据，本地数据会通过updateTreeData事件同步更新
          this.$emit('updateTreeData', {
            nodeId: data.id,
            updatedData: params,
            fieldType: fieldName
          })
        } else {
          this.$message.error(res.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      }
    },
    updateRankingTextBySex () {
      // 当性别改变时，自动更新排行文本
      const currentRanking = this.data.ranking_text

      // 如果当前排行是"始祖"，不需要更新
      if (currentRanking === '始祖') {
        return
      }

      // 处理"长子"/"长女"的转换
      if (currentRanking === '长子' && this.data.sex === '女') {
        this.data.ranking_text = '长女'
      } else if (currentRanking === '长女' && this.data.sex === '男') {
        this.data.ranking_text = '长子'
      } else if (currentRanking && currentRanking.includes('子') && this.data.sex === '女') {
          // 处理"次子"/"次女"等的转换
        this.data.ranking_text = currentRanking.replace('子', '女')
      } else if (currentRanking && currentRanking.includes('女') && this.data.sex === '男') {
        this.data.ranking_text = currentRanking.replace('女', '子')
      } else if (currentRanking === '之子' && this.data.sex === '女') {
          // 处理"之子"/"之女"的转换
        this.data.ranking_text = '之女'
      } else if (currentRanking === '之女' && this.data.sex === '男') {
        this.data.ranking_text = '之子'
      }
    }
  }
}
</script>
<style scoped lang='less'>
.tab-base-info {
  display:flex;
  width: 100%;
  background:#fff;
  padding-left: 10px;
  .photo {
    height: 134px;
    width: 100px;
    position: relative;
    box-sizing: border-box;
    padding: 8px;
    margin: 0 auto;
    margin-bottom: 5px;

    .default {
      background: #f0f2f5 url(~@/assets/photo.png) no-repeat center center;
      height: 100%;
      width: 100%;
      background-size: cover;
    }
    .tips {
      position: absolute;
      bottom: 0;
      right: 0;
      left: 0;
      background: rgba(0, 0, 0, .4);
      color: #fff;
      font-size: 12px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      cursor: pointer;
    }
    .close {
      position: absolute;
      height: 16px;
      width: 16px;
      background: #fff;
      border-radius: 50%;
      right: 0;
      top: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      z-index: 1;
      .cus-edit-toolbar-icon {
        color: #f76d02;
      }
    }
  }
}
.form-item {
  border:1px solid #dcdfe6;
  border-radius:4px;
  height: 34px;
  .title {
    text-align:center;
    height: 32px;
    width: 60px;
    align-content:center;
    border-right:1px solid #dcdfe6;
    background-color:#f5f7fa;
    font-size:13px;
  }
}
.bind-account {
  height: 26px;
  justify-content: center;
  border: 1px solid #f86e04;
  color: #f86e04;
  margin: 10px 0 0 12px;
  font-size: 12px;
  width: 70px;
  &:hover {
    background-color:#f86e04;
    color: #fff;
  }
}
.add {
  border: 1px solid #f86e04;
  line-height: 1;
  padding: 7px 5px;
  font-size: 12px;
  color: #f86e04;
  border-radius: 2px;
  cursor: pointer;
  &:hover {
    background-color:#f86e04;
    color: #fff;
  }
}
.line {
  flex-grow: 1;
  height: 0;
  border-top: 1px dashed #ddd;
}
/deep/ .ant-input {
  border: 0;
  font-size:13px;
  width: 80px;
}
/deep/ .ant-select {
  border: 0;
  font-size:13px;

  .ant-select-arrow-icon {
    font-size: 13px !important;
  }
}
</style>
