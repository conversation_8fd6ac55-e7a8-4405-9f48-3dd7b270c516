<template>
  <div class="calendar">
    <div class="tabs">
      <button
        :class="{ active: activeTab === 'solar' }"
        @click="activeTab = 'solar'">公历</button>
      <button
        :class="{ active: activeTab === 'lunar' }"
        @click="activeTab = 'lunar'">农历</button>
      <button
        :class="{ active: activeTab === 'era' }"
        @click="activeTab = 'era'">年号</button>
      <button
        :class="{ active: activeTab === 'custom' }"
        @click="activeTab = 'custom'">自定义</button>
    </div>

    <div class="range-select">
      <div class="range-label">
        <a-select style="width: 200px">
          <a-select-option :value="item.id" v-for="item in centuryList" :key="item.id">{{ item.value }}</a-select-option>
        </a-select>
      </div>
      <div class="options">
        <label>
          <input type="checkbox" v-model="showGregorian" />
          公元
        </label>
        <label v-if="activeTab === 'lunar'">
          <input type="checkbox" v-model="showStemBranch" />
          干支
        </label>
      </div>
      <button class="save-btn" @click="saveSelection">保存</button>
    </div>

    <div class="calendars">
      <div class="main-calendar">
        <div class="input-display">
          <input type="text" :placeholder="activeTab === 'era' ? '年号' : '-'" readonly />
        </div>

        <div class="year-calendar" v-if="activeTab !== 'era'">
          <button
            v-for="year in getYearList(activeTab === 'lunar')"
            :key="year.year"
            :class="getYearClass(year, activeTab)"
            @click="selectYear(year)">
            {{ year.display }}
          </button>
        </div>

        <div class="era-calendar" v-else>
          <button
            v-for="era in eraList"
            :key="era.id"
            :class="getEraClass(era)"
            @click="selectEra(era)">
            {{ era.display }}
          </button>
        </div>
      </div>

      <div class="side-calendar" v-if="activeTab === 'era'">
        <div class="input-display">
          <input type="text" placeholder="-" readonly />
        </div>

        <div class="era-year-list">
          <div class="era-year-item" v-for="eraYear in eraYearList" :key="eraYear.year">
            {{ eraYear.display }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { centuryRange } from '@/components/calendar/constant'

export default {
  name: 'MultiCalendar',
  data () {
    return {
      activeTab: 'solar',
      centuryList: centuryRange,
      selectedYear: null,
      selectedEra: null,
      showGregorian: true,
      showStemBranch: false,
      yearsRange: Array.from({ length: 26 }, (_, i) => 2000 + i),
      lunarYears: Array.from({ length: 26 }, (_, i) => {
        const year = 2000 + i
        const stemBranch = this.getStemBranch(year)
        return {
          year,
          stemBranch,
          display: `${year}(${stemBranch})`
        }
      }),
      eraList: [
        { id: 1, name: '民国', startYear: 1912, endYear: 1949, display: '民国(1912-1949)' },
        { id: 2, name: '中华人民共和国', startYear: 1949, endYear: 2025, display: '中华人民共和国(1949-2025)' }
      ],
      eraYearList: [
        { year: 1949, display: '一九四九年己丑(1949)' },
        { year: 1950, display: '一九五〇年庚寅(1950)' },
        { year: 1951, display: '一九五一年辛卯(1951)' },
        { year: 1952, display: '一九五二年壬辰(1952)' },
        { year: 1953, display: '一九五三年癸巳(1953)' },
        { year: 1954, display: '一九五四年甲午(1954)' },
        { year: 1955, display: '一九五五年乙未(1955)' },
        { year: 1956, display: '一九五六年丙申(1956)' },
        { year: 1957, display: '一九五七年丁酉(1957)' }
      ]
    }
  },
  methods: {
    closeCalendar () {
      this.$emit('close')
    },
    saveSelection () {
      const selection = {
        tab: this.activeTab,
        year: this.selectedYear,
        era: this.selectedEra,
        showGregorian: this.showGregorian,
        showStemBranch: this.showStemBranch
      }
      this.$emit('save', selection)
    },
    getStemBranch (year) {
      // 计算天干地支
      const stem = ['庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己'][year % 60 / 10]
      const branch = ['辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯'][year % 12]
      return stem + branch
    },
    getYearList (isLunar) {
      return isLunar ? this.lunarYears : this.yearsRange.map(year => ({
        year,
        display: year + ''
      }))
    },
    getYearClass (year, tab) {
      const isLunar = tab === 'lunar'
      const selected = isLunar
        ? this.selectedYear && this.selectedYear.year === year.year
        : this.selectedYear === year.year

      return {
        year: true,
        selected: selected,
        [isLunar ? 'lunar' : 'solar']: true
      }
    },
    getEraClass (era) {
      return {
        era: true,
        selected: this.selectedEra && this.selectedEra.id === era.id
      }
    },
    selectYear (year) {
      if (this.activeTab === 'lunar') {
        this.selectedYear = year
      } else {
        this.selectedYear = year.year
      }
    },
    selectEra (era) {
      this.selectedEra = era
    },
    handleOk () {},
    handleCancel () {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.calendar {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  width:496px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  padding: 10px 15px;
}

.tabs button {
  padding: 8px 15px;
  margin-right: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
}

.tabs button.active {
  color: #ff6b00;
  border-bottom: 2px solid #ff6b00;
}

.close {
  margin-left: auto;
}

.range-select {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.range-label {
  flex: 1;
  color: #666;
}

.options {
  display: flex;
  align-items: center;
}

.options label {
  margin-left: 15px;
  cursor: pointer;
}

.save-btn {
  background: #ff6b00;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 15px;
}

.calendars {
  display: flex;
  padding: 15px;
}

.main-calendar {
  flex: 1;
}

.side-calendar {
  flex: 1;
  padding-left: 15px;
  border-left: 1px solid #f5f5f5;
}

.input-display {
  margin-bottom: 15px;
}

.input-display input {
  width: 100%;
  padding: 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.year-calendar, .era-calendar, .era-year-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.year {
  width: calc(20% - 8px);
  padding: 8px;
  text-align: center;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.year.selected {
  border-color: #ff6b00;
  background: #fff9f0;
}

.era, .era-year-item {
  width: calc(50% - 5px);
  padding: 8px;
  text-align: center;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.era.selected {
  border-color: #ff6b00;
  background: #fff9f0;
}

.solar {
  color: #333;
}

.lunar {
  color: #666;
}

.era {
  color: #333;
}
</style>
