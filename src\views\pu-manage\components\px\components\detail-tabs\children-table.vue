<template>
  <a-table
    :columns="columns"
    :data-source="list"
    size="small"
    :pagination="false"
    style="width: 100%"
  >
    <template slot="parent">
      <div>
        无
      </div>
    </template>
    <template slot="action">
      <div class="flex justify-content-space-between">
        <span class="c-pointer-hover">选择</span>
        <span class="c-pointer-hover">上移</span>
        <span class="c-pointer-hover">下移</span>
        <span class="c-pointer-hover">删除</span>
      </div>
    </template>
  </a-table>
</template>
<script>
import cloneDeep from 'lodash.clonedeep'
const columns = [
  {
    title: '姓',
    dataIndex: 'first_name',
    key: 'first_name'
  },
  {
    title: '名字',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '性别',
    dataIndex: 'sex',
    key: 'sex'
  },
  {
    title: '父母',
    key: 'parents',
    dataIndex: 'parents',
    scopedSlots: { customRender: 'parent' }
  },
  {
    title: '关系',
    key: 'relation',
    dataIndex: 'relation'
  },
  {
    title: '排行',
    key: 'ranking_text',
    dataIndex: 'ranking_text'
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: '150px',
    scopedSlots: { customRender: 'action' }
  }
]
export default {
  name: 'ChildrenTable',
  data () {
    return {
      columns,
      list: []
    }
  },
  props: {
    source: {
      type: Array,
      default: () => []
    },
    nodeInfo: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    source: {
      handler (val) {
        this.list = cloneDeep(val)
      },
      immediate: true
    }
  },
  methods: {}
}
</script>
<style lang="less" scoped>
</style>
