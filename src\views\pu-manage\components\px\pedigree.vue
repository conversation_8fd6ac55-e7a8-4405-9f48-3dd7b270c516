<template>
  <a-modal
    class="add-pedigree"
    v-model="visible"
    :title="item?.id ? '编辑谱系分支':'新增谱系分支'"
    @cancel="$emit('close-modal')"
  >
    <a-spin :spinning="loading">
      <a-form :form="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }" @submit="handleSubmit">
        <a-form-item label="谱系名称">
          <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: true, message: '请输入谱系名称!' }] }]" />
        </a-form-item>
        <a-form-item label="谱系姓氏">
          <a-input placeholder="请输入" :maxLength="30" v-decorator="['first_name', { rules: [{ required: true, message: '请输入谱系姓氏!' }] }]" />
        </a-form-item>
        <a-form-item label="堂号">
          <a-input placeholder="请输入" :maxLength="30" v-decorator="['hall_name', { rules: [{ required: false, message: '请输入堂号!' }] }]" />
        </a-form-item>
        <a-form-item label="所在地区">
          <area-cascader :init-value="initValue" v-decorator="['province', {rules: [{ required: true, validator: checkProvince }]}]" />
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
          <a-button type="default" class="mr10" @click="$emit('close-modal')">取消</a-button>
          <a-button type="primary" html-type="submit" :loading="submitting">确定</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import AreaCascader from '@/components/area-cascader'

export default {
    name: 'Pedigree',
    components: {
      AreaCascader
    },
    props: {
        item: {
          type: Object,
          default: () => {}
        }
    },
   data () {
      return {
        visible: true,
        loading: false,
        submitting: false,
        initValue: [],
        form: this.$form.createForm(this, { name: 'pedigreeForm' })
      }
    },
    computed: {
      ...mapState({
        currentSelectedClanId: state => state.family.currentSelectedClanId
      })
    },
    mounted () {
      if (this.item?.id > 0) {
        this.id = this.item.id
        const { name, province, city, district } = this.item
        this.initValue = [province, city, district]
        this.form.getFieldDecorator('name', { initialValue: name })
        this.form.getFieldDecorator('first_name', { initialValue: this.item.first_name })
        this.form.getFieldDecorator('hall_name', { initialValue: this.item.hall_name })
        this.form.getFieldDecorator('province', { initialValue: district ? [province, city, district] : [province, city] })
       }
    },
    methods: {
      ...mapActions(['AddPedigree', 'UpdatePedigree']),
      checkProvince (_, value, callback) {
        if (value && value.length > 0) {
          callback()
          return
        }
        const str = '请选择所在地区!'
        callback(str)
      },
      handleOk (e) {
        this.visible = false
      },
      async handleSubmit (e) {
        e.preventDefault()
        const { AddPedigree, UpdatePedigree } = this
        this.form.validateFields(async (err, values) => {
          if (!err) {
            const params = { ...values }
            params.province = values.province[0]
            params.city = values.province[1]
            params.district = values.province[2] ? values.province[2] : ''
            params.clan_id = this.currentSelectedClanId
            if (this.item?.id) {
              params.pedigree_id = this.item.id
            }
            this.submitting = true
            if (params.pedigree_id) {
              await UpdatePedigree(params)
            } else {
              await AddPedigree(params)
            }
            this.submitting = false
            this.$emit('refresh')
            this.$emit('close-modal')
          }
        })
      }
    }
}
</script>
<style scoped lang='less'>
.add-pedigree {
  /deep/ .ant-modal-footer { display:none;}
}
</style>
