<template>
  <a-modal
    class="add-shi-xi-clan-node-modal-wrap"
    v-model="visible"
    title="添加世系"
    width="1000px"
    :maskStyle="{ opacity: 0 }"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <div class="content flex-direction-column">
      <div>
        <a-select style="width: 200px" v-model="selectedClanId" placeholder="请选择世系" @change="handleClanChange">
          <a-select-option v-for="item in clanList" :value="item.id" :key="item.id">{{ item.name }}</a-select-option>
        </a-select>
        <a-select style="width: 200px" class="ml12" v-model="selectedPedigreeId" placeholder="请选择谱系">
          <a-select-option v-for="item in PedigreeList" :value="item.id" :key="item.id">{{ item.name }}</a-select-option>
        </a-select>
        <span class="ml12">当前选中节点：{{ currentSelectedNode?.full_name }}</span>
      </div>
      <div class="mt8">
        <a-select style="width: 200px" v-model="searchZuYuan" placeholder="请选择族员" @change="handleClanChange">
          <a-select-option value="">全部族员</a-select-option>
          <a-select-option v-for="(value, key) in TagType" :value="value" :key="value">{{ key }}</a-select-option>
        </a-select>
        <a-select style="width: 200px" class="ml12" v-model="searchShiXi" placeholder="请选择世系" @change="handleClanChange">
          <a-select-option value="">全部世系</a-select-option>
          <a-select-option v-for="item in generationNum" :value="item" :key="item">第{{ item }}世</a-select-option>
        </a-select>
        <a-input style="width: 200px" class="ml12" placeholder="输入谱名搜索" />
        <a-button class="ml12" type="primary">搜索</a-button>
      </div>
      <div class="flex-1">
        <a-spin :spinning="pedigreeListLoading">
          <clan-tree
            v-if="treeData.length > 0"
            :show-line="true"
            :no-add="true"
            :nodes="treeData"
            :default-expanded-keys="expandedKeys"
            :defaultCheckedKeys.sync="expandedKeys"
            :selected-key="currentSelectedKey"
            @click="handleSelectedNode"
          />
          <a-empty v-if="treeData.length === 0" />
        </a-spin>
      </div>
      <div class="text-center" style="width: 100%;">
        <a-button @click="$emit('close')">取消</a-button>
        <a-button type="primary" class="ml12">确定</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import ClanTree from '@/components/clan-tree/Tree'
import { getSpecificLevelIdList } from '@/utils/getLevelIdsUtls'
import { getPedigreeList, getPedigreeMemberList } from '@/api/px'
import { TagType } from '@/constant'

function transformData (data) {
  data.forEach(item => {
    item.title = item.full_name || ''
    item.key = item.id
    item.writeMode = 2
    if (item.children && item.children.length > 0) {
      transformData(item.children)
    } else {
      item.children = []
    }
  })
}

export default {
  name: 'Step3AddClanNode',
  components: { ClanTree },
  data () {
    return {
      visible: true,
      confirmLoading: false,
      clanList: [],
      PedigreeList: [],
      pedigreeListLoading: false,
      selectedClanId: '',
      selectedPedigreeId: '',
      treeData: [],
      expandedKeys: [],
      currentSelectedKey: '',
      currentSelectedNode: null,
      TagType,
      searchZuYuan: '',
      searchShiXi: '',
      generationNum: Array(200).fill(0).map((_, index) => index + 1)
    }
  },
  computed: {
    ...mapState({
      groupClan: state => state.family.groupClan
    })
  },
  watch: {
    groupClan: {
      handler (val) {
        if (val) {
          val.forEach(item => {
            if (item.clan_list?.length > 0) {
              this.clanList = this.clanList.concat(item.clan_list)
            }
          })
          if (this.clanList?.length > 0) {
            this.selectedClanId = this.clanList[0].id
            this.handleGetPedigreeList()
          }
        }
      },
      immediate: true
    },
    selectedPedigreeId: {
      handler (val) {
        if (val) {
          this.handleGetPedigreeMemberList()
        }
      },
      immediate: true
    }
  },
  mounted () {
    console.log(this.currentSelectedClanId, this.currentSelectedPedigreeId)
    if (!this.currentSelectedClanId) {
      return
    }
    if (!this.currentSelectedPedigreeId) {
      return
    }
    this.GetPedigreeMemberList({
      clan_id: this.selectedClanId,
      pedigree_id: this.currentSelectedPedigreeId,
      generation: 3
    })
  },
  methods: {
    ...mapActions([ 'GetPedigreeMemberList' ]),
    async handleGetPedigreeList () {
      if (this.selectedClanId) {
        const res = await getPedigreeList({ clan_id: this.selectedClanId })
        if (res?.code === 0) {
          this.PedigreeList = res?.data?.list || []
          if (this.PedigreeList?.length > 0) {
            const isExist = this.PedigreeList.find(item => item.id === this.selectedPedigreeId)
            if (!this.selectedPedigreeId || !isExist) {
              this.selectedPedigreeId = this.PedigreeList[0].id
            }
          } else {
            this.selectedPedigreeId = undefined
            this.treeData = []
          }
        }
      }
    },
    async handleGetPedigreeMemberList () {
      if (!this.selectedClanId) {
        return
      }
      if (!this.selectedPedigreeId) {
        return
      }
      this.pedigreeListLoading = true
      const res = await getPedigreeMemberList({
        clan_id: this.selectedClanId,
        pedigree_id: this.selectedPedigreeId,
        generation: 3
      })
      this.pedigreeListLoading = false
      if (res?.data) {
        const data = [res.data]
        transformData(data)
        this.treeData = data
        this.expandedKeys = getSpecificLevelIdList(data)
      } else {
        this.treeData = []
        this.generationList = []
        this.currentSelectedGeneration = -999
      }
    },
    handleClanChange () {
      this.handleGetPedigreeList()
    },
    handleOk () {
      this.$emit('close')
    },
    handleSelectedNode (key, node) {
      this.currentSelectedKey = key
      this.currentSelectedNode = node
    },
    handleSubmit (e) {
      e.preventDefault()
    }
  }
}
</script>
<style lang='less' scoped>
.add-shi-xi-clan-node-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
  height: 600px;
  .content {
    display:flex;
    height: 600px;
  }
}
</style>
