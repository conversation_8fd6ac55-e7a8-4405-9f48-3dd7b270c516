<template>
  <a-modal
    class="edit-pu-wen-title-modal-wrap"
    v-model="visible"
    title="谱眉标题设置"
    width="500px"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <a-form :form="form" layout="vertical" @submit="handleSubmit">
      <a-form-item>
        <a-radio-group name="radioGroup" :default-value="type" @change="handleChangeRadioGroup">
          <a-radio :value="1">默认(谱眉标题+世系)</a-radio>
          <a-radio :value="2">自定义</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="type===1">
        <template #label>
          <strong style="color: #606266;">谱眉标题</strong>
        </template>
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['title', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===1">
        <template #label>
          <strong style="color: #606266;">章节目录设置</strong>
          <a-checkbox class="ml24">全部显示</a-checkbox>
        </template>
        <a-checkbox-group>
          <a-checkbox value="使用谱眉标题前缀">使用谱眉标题前缀</a-checkbox>
          <a-checkbox value="加粗">加粗</a-checkbox>
          <a-checkbox value="居中">居中</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <a-form-item v-if="type===1">
        <template #label>
          <div class="flex justify-content-space-between">
            <strong style="color: #606266;">检索表目录</strong>
            <div>
              <a-checkbox class="ml24">页码</a-checkbox>
              <a-checkbox class="ml24">显示</a-checkbox>
            </div>
          </div>
        </template>
        <a-input placeholder="请输入" :addon-before="form.getFieldValue('title')" :maxLength="30" v-decorator="['category', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===1">
        <template #label>
          <div class="flex justify-content-space-between">
            <strong style="color: #606266;">世系图目录</strong>
            <div>
              <a-checkbox class="ml24">页码</a-checkbox>
              <a-checkbox class="ml24">显示</a-checkbox>
            </div>
          </div>
        </template>
        <a-input placeholder="请输入" :addon-before="form.getFieldValue('title')" :maxLength="30" v-decorator="['tu', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===1">
        <template #label>
          <div class="flex justify-content-space-between">
            <strong style="color: #606266;">世系表目录</strong>
            <div>
              <a-checkbox class="ml24">页码</a-checkbox>
              <a-checkbox class="ml24">显示</a-checkbox>
            </div>
          </div>
        </template>
        <a-input placeholder="请输入" :addon-before="form.getFieldValue('title')" :maxLength="30" v-decorator="['biao', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===2">
        <template #label>
          <a-checkbox :checked="useOneTitle" @change="e=>useOneTitle=e .target.checked">使用统一标题</a-checkbox>
        </template>
        <a-input placeholder="请输入" v-if="useOneTitle" :maxLength="30" v-decorator="['one-title', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===2 && !useOneTitle">
        <template #label>
          <strong style="color: #606266;">检索表标题：</strong>
        </template>
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===2 && !useOneTitle">
        <template #label>
          <strong style="color: #606266;">检索表索引目录标题：</strong>
        </template>
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===2 && !useOneTitle">
        <template #label>
          <strong style="color: #606266;">世系图标题：</strong>
        </template>
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===2 && !useOneTitle">
        <template #label>
          <strong style="color: #606266;">世系图索引目录标题：</strong>
        </template>
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===2 && !useOneTitle">
        <template #label>
          <strong style="color: #606266;">世系表标题：</strong>
        </template>
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item v-if="type===2 && !useOneTitle">
        <template #label>
          <strong style="color: #606266;">世系表索引目录标题：</strong>
        </template>
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: false, message: '请输入!' }] }]" />
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 11 }">
        <a-button type="primary" html-type="submit" :loading="loading">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
 <script>
 export default {
   name: 'EditTitle',
    data () {
     return {
       visible: true,
       loading: false,
       type: 1,
       useOneTitle: false, // 使用统一标题
       form: this.$form.createForm(this, { name: 'edit-title' })
     }
   },
   methods: {
     handleChangeRadioGroup (e) {
       this.type = e.target.value
     },
     handleOk () {
       this.$emit('close')
     },
     handleSubmit (e) {
       e.preventDefault()
     }
   }
 }
 </script>
<style scoped lang='less'>
/deep/ .ant-modal-footer { display:none;}
/deep/ .ant-form-item { margin-bottom: 2px}
.edit-pu-wen-title-modal-wrap {
}
</style>
