<template>
  <a-modal
    title="自造字列表（点击即可复制文字）"
    :visible="visible"
    :width="800"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="保存"
    cancelText="取消"
  >
    <div>造字功能暂未实现</div>
  </a-modal>
</template>
<script>
export default {
  name: 'ZaoZiModal',
  data () {
    return {
      visible: true,
      confirmLoading: false
    }
  },
  methods: {
    handleOk () {
      this.confirmLoading = true
      setTimeout(() => this.$emit('close'), 1000)
    },
    handleCancel () {
      this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
</style>
