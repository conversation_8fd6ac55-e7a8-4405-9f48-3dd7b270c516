<template>
  <div class="third-column" v-show="showBaseDetail">
    <!-- 固定头部区域 -->
    <div class="detail-header">
      <div class="flex justify-content-space-between align-center detail-header-bar">
        <div class="flex flex-1 tabs">
          <div class="item" :class="detailTabs === 1 ? ' on' : ''" @click="detailTabs = 1">基本信息</div>
          <div class="item hidden" :class="detailTabs === 2 ? ' on' : ''" @click="detailTabs = 2">快捷录入</div>
          <div class="item hidden" :class="detailTabs === 3 ? ' on' : ''" @click="detailTabs = 3">帮助说明</div>
          <div class="item hidden" :class="detailTabs === 4 ? ' on' : ''" @click="detailTabs = 4">用户纠错</div>
        </div>
        <div class="rotate-icon">
          <i class="cus-edit-toolbar-icon icon-edit-toolbarzhedie1 c-pointer-hover" @click="handleToggleDetail" />
        </div>
      </div>
      <!-- 节点信息与配偶 -->
      <detail-tabs-header
        :mainNodeInfo="mainNodeInfo"
        :activeNodeInfo="activeNodeInfo"
        @handleSelectedNode="handleSelectedNode"
        @handleAddWife="handleAddWife"
        @handleDeleteWife="handleDeleteWife" />
    </div>

    <!-- 可滚动内容区域 -->
    <div class="detail-content-wrapper">
      <!-- 成员表单 -->
      <div v-if="detailTabs === 1" class="flex">
        <tab1-base-info
          :nodeInfo="activeNodeInfo"
          :father="father"
          :showText="rawDetail?.show_text || ''"
          @refresh="handleRefresh"
          @updateTreeData="handleUpdateTreeData" />
      </div>
      <div v-if="detailTabs === 2">
        <tab2-quick-info :fastText="rawDetail?.fast_text || ''" />
      </div>
      <div v-if="detailTabs === 3">
        <div class="flex flex-direction-column flex-1" style="height: 30vh; background-color:#fff;padding-left: 10px">
          帮助说明待补充</div>
      </div>
      <div v-if="detailTabs === 4">
        <div class="flex flex-direction-column flex-1" style="height: 30vh; background-color:#fff;padding-left: 10px">
          用户纠错待补充</div>
      </div>
    </div>
  </div>
</template>

<script>
import DetailTabsHeader from './detail-tabs/header'
import Tab1BaseInfo from './detail-tabs/tab-1-base-info'
import Tab2QuickInfo from './detail-tabs/tab-2-quick-info'

export default {
  name: 'MemberDetailInfo',
  components: {
    DetailTabsHeader,
    Tab1BaseInfo,
    Tab2QuickInfo
  },
  props: {
    showBaseDetail: {
      type: Boolean,
      default: false
    },
    mainNodeInfo: {
      type: Object,
      default: () => ({})
    },
    activeNodeInfo: {
      type: Object,
      default: () => ({})
    },
    father: {
      type: Object,
      default: () => ({})
    },
    rawDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      detailTabs: 1
    }
  },
  methods: {
    handleToggleDetail () {
      this.$emit('toggleDetail')
    },
    handleSelectedNode (node) {
      this.$emit('selectedNode', node)
      // 通知父组件更新世系树的选中状态
      this.$emit('updateTreeSelection', node)
    },
    handleAddWife () {
      this.$emit('addWife')
    },
    handleDeleteWife (wife) {
      this.$emit('deleteWife', wife)
    },
    handleRefresh () {
      this.$emit('refresh')
    },
    handleUpdateTreeData (updateInfo) {
      // 转发updateTreeData事件到父组件
      this.$emit('updateTreeData', updateInfo)
    }
  }
}
</script>

<style lang="less" scoped>
.third-column {
  width: 820px;
  padding-top: 4px;
  padding-left: 4px;
  height: calc(100vh - 70px);
  display: flex;
  flex-direction: column;

  .detail-header {
    flex-shrink: 0;

    &-bar {
      margin-bottom: 4px;
      background-color: #fff;
    }

    .rotate-icon {
      padding: 0 10px;
      transform: rotate(180deg);
    }
  }

  .detail-content-wrapper {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }

  .tabs {
    height: 39px;

    .item {
      margin-right: 1px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 87px;
      background-color: #f2f3f5;
      cursor: pointer;

      &.on {
        background-color: #fff;
      }
    }
  }
}
</style>
