<template>
  <a-modal
    class="add-ancestor-modal-wrap"
    v-model="visible"
    title="调整世系"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <a-form :form="form" :label-col="{ span: 7 }" :wrapper-col="{ span: 16 }" @submit="handleSubmit">
      <a-form-item label="设置对象">
        <a-input placeholder="请输入" disabled :maxLength="30" v-decorator="['name']" />
      </a-form-item>
      <a-form-item label="当前世系">
        <a-input placeholder="请输入" disabled :maxLength="30" v-decorator="['generation']" />
      </a-form-item>
      <a-form-item label="调整为">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['edit-generation', { rules: [{ required: true, message: '请输入世系!' }] }]" />
      </a-form-item>
      <a-form-item label="调整范围">
        <a-select style="width: 100%" v-decorator="['type', { rules: [{ required: true, message: '请选择调整范围!' }] }]">
          <a-select-option :value="1">仅自己</a-select-option>
          <a-select-option :value="2">自己及后代(分支下非断代，且世数连续的后裔)</a-select-option>
          <a-select-option :value="3">自己及后代(分支下非断代的所有后裔)</a-select-option>
          <a-select-option :value="4">自己及后代(分支下的所有后裔)</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" html-type="submit" :loading="loading">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
export default {
  name: 'TiaoZhengShiXiModal',
  data () {
    return {
      visible: true,
      loading: false,
      form: this.$form.createForm(this, { name: 'shixi' })
    }
  },
  props: {
    selectedNode: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    selectedNode: {
      handler (val) {
        if (val) {
          this.form.getFieldDecorator('name', { initialValue: val.name })
          this.form.getFieldDecorator('generation', { initialValue: val.generation })
          this.form.getFieldDecorator('type', { initialValue: 1 })
        } else {
          this.form.getFieldDecorator('name', { initialValue: undefined })
          this.form.getFieldDecorator('generation', { initialValue: undefined })
          this.form.getFieldDecorator('type', { initialValue: 1 })
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleOk () {
      this.$emit('close')
    },
    handleSubmit (e) {
      e.preventDefault()
      this.form.validateFields(async (err, values) => {
        if (!err) {
          this.$message.info('保存功能暂未实现')
          this.$emit('close')
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
.add-ancestor-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
}
</style>
