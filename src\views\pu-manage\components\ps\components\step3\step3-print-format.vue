<template>
  <div class="step3-print-format-wrap flex flex-direction-column">
    <div class="flex content">
      <div style="width:200px;">
        <!-- <div>
          版式类型
          <a-select style="width:100px" v-model="bookFormat">
            <a-select-option v-for="item in printBookTypesList" :key="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </div> -->
        <div class="menu">
          <div class="item" :class="item.id === bookId ? 'on':''" v-for="item in showFormatList" :key="item.id" @click="handleClick(item)">
            <span class="tag" :class="item.type==='custom'?'dingzhi':'tongyong'">{{ item.type==='custom'?'定制':'通用' }}</span>
            <span>{{ item.name }}</span>
          </div>
        </div>
      </div>
      <div class="flex-1 ml8">
        <div><strong>版式范例</strong></div>
        <book-preview :book-id="bookId" :key="bookId" :data="pdfPicturePath" />
      </div>
    </div>
    <div class="bottom">
      <div>
        <a-button @click="$emit('return')" class="mr6 ml6">返回</a-button>
        <a-button @click="$emit('return')" class="mr6">保存设置</a-button>
        <a-button @click="$emit('return')" class="mr6">保存并返回</a-button>
      </div>
      <!-- <div>
        <a-button class="c-pointer-hover mr6">高级设置</a-button>
        <a-button class="c-pointer-hover mr6">应用预设</a-button>
        <a-button class="c-pointer-hover mr6">存为预设</a-button>
        <a-button class="c-pointer-hover mr6">回复默认</a-button>
      </div> -->
    </div>
  </div>
</template>
<script>
import cloneDeep from 'lodash.clonedeep'
import { printBookTypesList } from '@/constant/print-book-types'
import list from '@/views/pu-manage/components/ps/components/step3/step3-mock-data'
import BookPreview from '@/views/pu-manage/components/ps/components/step3/book-preview'
export default {
  name: 'Step3PrintFormat',
  components: { BookPreview },
  data () {
    return {
      bookFormat: 0,
      bookId: '',
      showFormatList: list,
      printBookTypesList,
      pdfPicturePath: [],
      show: false
    }
  },
  watch: {
    bookFormat (val) {
      if (val === 0) {
        this.showFormatList = list
      } else {
        this.showFormatList = list.filter(item => item.groupId === val)
      }
    }
  },
  beforeMount () {
    this.pdfPicturePath = cloneDeep(this.showFormatList[0].samples)
    this.bookId = this.showFormatList[0].id
    this.show = true
  },
  mounted () {
  },
  methods: {
    handleClick (item) {
      this.bookId = item.id
      this.pdfPicturePath = cloneDeep(item.samples)
    }
  }
}
</script>
<style lang='less' scoped>
.step3-print-format-wrap {
  width:100%;
  position:relative;
  overflow-y:auto;
  //background-color:#f76d02;
  text-align:left;
  .content {
     flex:1;
     display:flex;
     //background-color:antiquewhite;
    .menu {
      margin-top: 12px;
      overflow-x: hidden;
      height: calc(100vh - 60px - 148px);
      .item{
        flex-shrink: 0;
        position: relative;
        width: 160px;
        height: 36px;
        background: #f5f5f5;
        border-radius: 2px;
        border: 1px solid #ccc;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        overflow: hidden;
        cursor: pointer;
        &.on {
          background: #feefe4;
          border: 1px solid #f86e04;
          color: #f86e04;
        }
        .tag {
          position: absolute;
          left: 0;
          top: 0;
          width: 80px;
          height: 16px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          z-index: 10;
          transform: rotate(-45deg);
          transform-origin: 42% 267%;
          font-size: 12px;
          &.tongyong {
            background-color: #ccc;
            color: #666;
          }
          &.dingzhi {
            background-color: #dca769 !important;
            color: #974d00 !important;
          }
        }
      }
    }
    .book {
        overflow: hidden;
        width: 780px;
        text-align: center;
        position:relative;
        #flipbook-preview{
          border: 3px solid black;
        }
       .prev,.next {
         position: absolute;
         top: 50%;
         background-color: rgba(0,0,0,.3);
         width: 40px;
         height:40px;
         line-height: 40px;
         border-radius:50%;
         i {
           color: #fff;
         }
       }
      .next {
        right: 0;
        transform:rotateY(180deg);
      }
    }
  }
  .bottom {
    border-top:1px solid #e5e5e5;
    height: 60px;
    display:flex;
    justify-content:space-between;
    align-items:center;
  }
}
</style>
