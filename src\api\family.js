import request from '@/utils/request'

const familyApi = {
  AddClan: '/web/family/addClan',
  GroupClanList: '/web/family/groupClanList'
}

/**
 * 添加族谱
 * @param {object} parameter - 请求体
 * @param {number} parameter.group_id - 分组ID
 * @param {string} parameter.name - 族谱名称
 * @param {string} parameter.surname - 姓氏
 * @param {string} parameter.ancestor - 始迁祖
 * @param {string} [parameter.hall_name] - 堂号
 * @param {string} [parameter.description] - 介绍
 * @param {string} parameter.province - 省份
 * @param {string} parameter.city - 城市
 * @param {string} parameter.district - 区域
 * @returns {Promise<object>}
 */
export async function addClan (parameter) {
  return request({
    url: familyApi.AddClan,
    method: 'post',
    data: parameter
  })
}

/**
 * 族谱列表
 * @param {object} [parameter] - 查询参数
 * @param {number} [parameter.group_id] - 分组ID
 * @returns {Promise<object>}
 */
export async function groupClanList (parameter) {
  return request({
    url: familyApi.GroupClanList,
    method: 'get',
    // 注意：GET请求的参数通常放在params中，但此处遵循原代码逻辑放在data
    data: parameter
  })
}
