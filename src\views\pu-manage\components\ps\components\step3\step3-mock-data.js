const list = [
  // {
  //   'id': 3,
  //   'name': '牒记式（标准版）',
  //   'type': 'normal',
  //   'groupId': 3,
  //   'description': '不用横竖线连接世代人名间的关系，而是纯用文字来表达这种关系；每个人名下都有一个相关的简介。牒记式的世系形式固定，次序分明，比较节约纸张。',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/3/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/3/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/3/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/3/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/3/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/3/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/3/00008.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/3/00009.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 3,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 1,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 1,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': 1,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 0,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 31,
  //   'name': '牒记式（定制一）',
  //   'type': 'custom',
  //   'groupId': 3,
  //   'description': '与牒记式相比，此排版采用不固定列宽的形式，使页面能够容纳更多的内容，但此版暂时不支持配偶单独立传，不支持头像显示，并且只支持右开排版。',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/31/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/31/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/31/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/31/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/31/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/31/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 31,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 0,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': 1,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 2,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 2,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 104,
  //   'name': '牒记式（定制二）',
  //   'type': 'custom',
  //   'groupId': 3,
  //   'description': '',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/104/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/104/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/104/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/104/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/104/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/104/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/104/00008.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/blank.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 104,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': 1,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 2,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 108,
  //   'name': '牒记式（定制三）',
  //   'type': 'custom',
  //   'groupId': 3,
  //   'description': '郴州贺氏家族定制版式，仅支持右开',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/108/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/108/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/108/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/108/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/108/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/108/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 108,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': 1,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 109,
  //   'name': '牒记式（定制四）',
  //   'type': 'custom',
  //   'groupId': 3,
  //   'description': '廖氏家族定制版式',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/109/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/109/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/109/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/109/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/109/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/blank.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 109,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': 1,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  {
    'id': 1,
    'name': '欧　式（标准版）',
    'type': null,
    'groupId': 1,
    'description': '欧阳修式家谱格式的特点是：世代分格，由右向左排列，欧式中，每个世代人名左侧可有一些生平记述，主要介绍该人的字、号、功名、官爵、生辰年月日、配偶、藏地、功绩等。',
    'project': false,
    'user': false,
    'base': true,
    'price': 0,
    'current': true,
    'cover': null,
    'samples': [
      'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00002.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00003.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00004.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00005.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00006.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00007.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00008.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00009.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/1/00010.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/blank.png',
      'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
    ],
    'config': {
      'version': 4,
      'format': {
        'customSize': {
          'enable': false,
          'fixedRatio': true,
          'paddingTop': 0.0,
          'paddingRight': 0.0,
          'paddingBottom': null,
          'version': 1
        },
        'formatId': 1,
        'showList': 1,
        'showDiagram': 2,
        'showDetail': 1,
        'binding': 2,
        'headType': 1,
        'pageType': 0,
        'spacing': 0,
        'direction': 1,
        'pageOrientation': 0,
        'pageSize': 2,
        'columnSize': 0,
        'offset': 0.0,
        'isMap': false
      },
      'map': {
        'height': null,
        'width': null,
        'shrink': 4,
        'memberSpacing': 1,
        'memberSpacingAt': null,
        'lineColor': 1,
        'showSideRemark': 0,
        'showDetail': 0,
        'showPhoto': 0,
        'showTitles': 0,
        'showBorder': 0,
        'generationType': 0,
        'generations': 6,
        'iconType': 0,
        'iconStyle': 1,
        'nameFontSize': 18.0,
        'wifeFontSize': 12.0,
        'contentFontSize': 12.0,
        'rankingFontSize': 12.0,
        'iconSize': 18.0,
        'scale': [],
        'showWifeStatus': 0,
        'repeatLastColumn': 1,
        'createPreview': 1,
        'title': {
          'display': 1,
          'style': 0,
          'borderStyle': 1,
          'font': '',
          'fontSize': 0.0,
          'color': '000000',
          'align': 2,
          'marginLeft': 5.0,
          'marginRight': 5.0
        },
        'couplet': {
          'topBorder': 1,
          'topFont': '',
          'topFontSize': 0.0,
          'topColor': '000000',
          'topAlign': 2,
          'border': 1,
          'font': '',
          'fontSize': 0.0,
          'color': '000000',
          'align': 1
        }
      },
      'index': {
        'pageNumberType': 1,
        'direction': 0,
        'showBranch': 0,
        'withOddPages': 1,
        'endWithOddPages': 0,
        'diagramIndent': 1,
        'detailIndent': 0
      },
      'header': {
        'backTitle': null,
        'hasBackTitle': false,
        'showBranchTitle': 1,
        'pageNumberType': 1,
        'pageNumberStyle': 0,
        'zeroType': 0,
        'showTitle': 1,
        'format': 0,
        'format2': {
          'title': true,
          'branch': false,
          'generation': true,
          'village': false
        },
        'useSystemCustom': true,
        'font': '',
        'addHeadSize': 0,
        'addTitleSize': 0,
        'verticalAlign': 0,
        'custom': null
      },
      'list': {
        'showSideRemark': 0,
        'titlePosition': 1,
        'showDaughter': 1,
        'showMultiGenerations': 0,
        'showBranchTitle': 1,
        'showBei': 0,
        'showLetter': 0,
        'columnSize': 3,
        'numberType': 1,
        'nameConfig': {
          'type': 1,
          'deathType': 1,
          'deathAppend': 0,
          'deathAppend2': 0,
          'deathAppendTo': 0,
          'deathAppendKey': '公',
          'deathAppendKey2': '公'
        },
        'listType': 1
      },
      'diagram': {
        'useCustom': 1,
        'repeatTitle': 0,
        'otherNameInSide': false,
        'showSideRemark': 1,
        'showWife': 0,
        'showChildrenCount': 0,
        'showChildrenStatus': 0,
        'showDaughter': 0,
        'showDaughterChildren': 0,
        'showDaughterIcon': 0,
        'showIntroduction': 0,
        'showBranch': 0,
        'newPageByBranch': 0,
        'showNameRemark': 0,
        'parentPosition': 0,
        'titlePosition': 1,
        'pageNumberType': 1,
        'repeatLastColumn': 1,
        'generations': 5,
        'generationChangePage': 0,
        'showMultiGenerations': 0,
        'multiGenerationStyle': 0,
        'mainGenerationStyle': 0,
        'showMultiBackground': 1,
        'showPageIcon': 1,
        'showRankingText': 0,
        'showLastColumnWife': 0,
        'showVillage': 0,
        'showBlankTitle': 1,
        'spacing': 0,
        'memberAlign': 0,
        'lineColor': 0,
        'memberListType': 0,
        'nameConfig': {
          'type': 1,
          'deathType': 1,
          'deathAppend': 0,
          'deathAppend2': 0,
          'deathAppendTo': 0,
          'deathAppendKey': '公',
          'deathAppendKey2': '公'
        }
      },
      'detail': {
        'memberListType': 0,
        'dateFormat': '',
        'customDateFormat': [],
        'monthFormat': [
          1
        ],
        'dayFormat': [
          1
        ],
        'childrenCountType': 1,
        'showBranch': 1,
        'showRanking': null,
        'rankingType': 1,
        'rankingAll': 0,
        'showChildrenRanking': 0,
        'showChildrenList': 1,
        'childrenWithNewline': 0,
        'siName': '嗣{0}',
        'tiaoName': '祧{0}',
        'yangName': '抚{0}',
        'zhuiName': '赘{0}',
        'fengName': '奉祀',
        'birthType': 1,
        'birthWord': ' ',
        'deathType': 1,
        'deathWord': ' ',
        'realNameKey': '又名',
        'graveKey': '葬',
        'deathWordStyle': 0,
        'splitWord': ' ',
        'splitWord2': 0,
        'nameSplitWord': ' ',
        'showWifeName': 0,
        'wifeNameStyle': 0,
        'showNameGeneration': 0,
        'wifeNewLine': 0,
        'showBei': 0,
        'beiOverName': 0,
        'showSi': 0,
        'showTiao': 1,
        'showReservedSpeace': 0,
        'showPageNumber': 1,
        'reservedAt': null,
        'pageWithSmallerGeneration': 0,
        'pageWithBrother': 0,
        'pageWithBrotherGeneration': null,
        'repeatGenerationTitle': 0,
        'showBrotherLine': 0,
        'lifeTimeFormat': 0,
        'lifeTimeType': 1,
        'showOtherName': 0,
        'showMultiGenerations': 1,
        'repeatLastColumn': 0,
        'repeatFirstColumn': 0,
        'maxContentLineLength': 0,
        'fontSize': 13,
        'showPreface': 0,
        'shrink': 0,
        'showOtherNameColumn': 0,
        'nameConfig': {
          'type': 1,
          'deathType': 1,
          'deathAppend': 0,
          'deathAppend2': 0,
          'deathAppendTo': 0,
          'deathAppendKey': '公',
          'deathAppendKey2': '公'
        },
        'columnNames102': {
          'c1': '世次',
          'c2': '传承',
          'c3': '姓名',
          'c4': '个人信息',
          'c5': '别名',
          'w1': null,
          'w2': null,
          'w3': null,
          'w4': null
        }
      },
      'display': {
        'showTiao': 1,
        'showPhoto': 0,
        'showSon': 1,
        'showDaughter': 1,
        'showDaughterList': 1,
        'showDaughterInfo': {
          'name': true,
          'detail': false,
          'wife': false,
          'wifeDetail': false,
          'children': false,
          'childrenDetail': false
        },
        'showWife': 1,
        'addVirtualWife': 0,
        'addVirtualWifeAt': null,
        'addEmptyIntroSpace': 0,
        'showWifeRelation': 1,
        'showDaughterChildren': 1,
        'showParent': 2,
        'showParentType': null,
        'showParentPosition': 0,
        'showParentPage': 1,
        'nameType': 1,
        'nameBlod': 0,
        'deathNameType': 1,
        'deathNameAppend': 0,
        'deathNameAppend2': 0,
        'deathNameAppendTo': 0,
        'deathNameAppendKey': '公',
        'deathNameAppendKey2': '公',
        'showRanking': 1,
        'showEmptyMember': 1,
        'showTopVillage': 0,
        'showVillage': 1,
        'showTopBranch': 0,
        'hiddenType': 1,
        'showVolumeIndex': 1,
        'showVolumeList': 1,
        'showPageIndex': 1,
        'showSingleMember': 1,
        'showFields': {
          'realName': true,
          'otherName': true,
          'bei': true,
          'school': true,
          'education': true,
          'major': true,
          'job': true,
          'grave': true,
          'lifeTime': true,
          'introduction': true,
          'father': true,
          'hometown': true,
          'birthPlace': true,
          'habitationDetail': true,
          'sideRemark': true,
          'titles': true,
          'identity': true,
          'preface': true,
          'remark': true,
          'remark2': true,
          'song': true,
          'birthDate': true,
          'deathDate': true,
          'namePrefix': true,
          'nameRemark': false
        },
        'showWifeFields': {
          'realName': true,
          'otherName': true,
          'bei': true,
          'school': true,
          'education': true,
          'major': true,
          'job': true,
          'grave': true,
          'lifeTime': true,
          'introduction': true,
          'father': true,
          'hometown': true,
          'birthPlace': true,
          'habitationDetail': true,
          'sideRemark': true,
          'titles': true,
          'identity': true,
          'preface': true,
          'remark': true,
          'remark2': true,
          'song': true,
          'birthDate': true,
          'deathDate': true,
          'namePrefix': true,
          'nameRemark': false
        },
        'sortFields': [
          '前注',
          '字辈',
          '头衔',
          '常用名',
          '别名',
          '身份',
          '出生日期',
          '出生地',
          '学校',
          '学历',
          '工作单位',
          '职位',
          '简介',
          '过世日期',
          '享年',
          '葬地',
          '后注',
          '赞词',
          '子女列表',
          '祖籍',
          '现居地',
          '后传'
        ],
        'sortWifeFields': [
          '前注',
          '父亲',
          '头衔',
          '常用名',
          '别名',
          '身份',
          '出生日期',
          '出生地',
          '学校',
          '学历',
          '工作单位',
          '职位',
          '简介',
          '过世日期',
          '享年',
          '葬地',
          '后注',
          '赞词',
          '子女列表',
          '祖籍',
          '现居地',
          '后传'
        ]
      },
      'global': {
        'language': '简体中文',
        'languageConvert': 0,
        'languageConvertStart': null,
        'languageConvertEnd': null,
        'languageConvertStart2': null,
        'languageConvertEnd2': null,
        'languageConvertTarget': 1,
        'addEmptyPage': true,
        'generationNumberType': 2,
        'generationFormat': 1,
        'generationFormatText': '',
        'generationFormatPrefix': '',
        'generationFormatSuffix': '',
        'generationFormatNumber': 1,
        'chineseNumber20': false,
        'chineseNumber30': false,
        'searchNumberType': 1,
        'showBranchBackground': 0,
        'generationStyle': 1,
        'generationStyle2': 1,
        'showBranch': 1,
        'repeatBranch': 1,
        'newPageByBranch': 0,
        'memberListType': 0
      },
      'origin': {},
      'font': {
        'listName': {
          'path': null,
          'size': null,
          'spacing': null,
          'lineHeight': null
        },
        'listNumber': {
          'path': null,
          'size': null,
          'spacing': null,
          'lineHeight': null
        },
        'diagramName': {
          'path': null,
          'size': null,
          'spacing': null,
          'lineHeight': null
        },
        'diagramWife': {
          'path': null,
          'size': null,
          'spacing': null,
          'lineHeight': null
        },
        'diagramInfo': {
          'path': null,
          'size': null,
          'spacing': null,
          'lineHeight': null
        },
        'detailName': {
          'path': null,
          'size': null,
          'spacing': null,
          'lineHeight': null
        },
        'detailWife': {
          'path': null,
          'size': null,
          'spacing': null,
          'lineHeight': null
        },
        'detailInfo': {
          'path': null,
          'size': null,
          'spacing': null,
          'lineHeight': null
        }
      },
      'volume': null,
      'volumeConfig': {
        'resetPageIndex': 1
      }
    }
  }
  // {
  //   'id': 101,
  //   'name': '欧-式（定制一）',
  //   'type': 'custom',
  //   'groupId': 1,
  //   'description': '吴川瑚琳杨氏家族所定制的版式',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00008.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00009.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/101/00010.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/blank.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 101,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 1,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': '殁于',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 1,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 1,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 2,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 9,
  //   'name': '苏-式（标准版）',
  //   'type': null,
  //   'groupId': 2,
  //   'description': '又称垂珠体，是北宋文学家苏洵创立的。苏式世系表特点是：世代间直行下垂，图表格式也是由右向左排列的，主要是强调宗法关系。',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/9/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/9/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/9/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/9/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/9/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/9/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/9/00008.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/9/00009.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 9,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': 1,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 1,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 105,
  //   'name': '苏-式（定制一）',
  //   'type': 'custom',
  //   'groupId': 2,
  //   'description': '浙江温州地区定制版式，采用五世一提的排版方式，并使用单独的定制页眉样式。',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/105/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/105/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/105/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/105/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/105/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/105/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/105/00008.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/105/00009.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 105,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 1,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 114,
  //   'name': '宝塔式（标准版）',
  //   'type': null,
  //   'groupId': 4,
  //   'description': '',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/114/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/114/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/114/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/114/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/114/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/blank.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 114,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 1,
  //       'showChildrenCount': 1,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 1,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 1,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 1,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 5,
  //   'name': '宝塔式（定制二）',
  //   'type': 'custom',
  //   'groupId': 4,
  //   'description': '',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/5/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/5/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/5/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/5/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/5/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/5/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 5,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 1,
  //       'headType': 10,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 1,
  //       'pageOrientation': 0,
  //       'pageSize': 0,
  //       'columnSize': 0,
  //       'offset': 0.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 1,
  //       'direction': 0,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 1,
  //       'showChildrenCount': 1,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 1,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 1,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 1,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': ' ',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 8,
  //   'name': '现代版（标准版）',
  //   'type': null,
  //   'groupId': 5,
  //   'description': '以现代书籍阅读习惯及设计风格，对世系、行传进行排版设计,符合现代人从左至右的阅读习惯。',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/8/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/8/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/8/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/8/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 8,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 2,
  //       'headType': 1,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 0,
  //       'pageOrientation': 0,
  //       'pageSize': 2,
  //       'columnSize': 0,
  //       'offset': 3.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 2,
  //       'direction': 1,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 2,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': '，',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 102,
  //   'name': '现代版（定制一）',
  //   'type': 'custom',
  //   'groupId': 5,
  //   'description': '世系单独成列',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/102/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/102/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/102/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/102/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/102/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/102/00007.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 102,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 2,
  //       'headType': 1,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 0,
  //       'pageOrientation': 0,
  //       'pageSize': 2,
  //       'columnSize': 0,
  //       'offset': 3.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 2,
  //       'direction': 1,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 2,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': '，',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 2,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 106,
  //   'name': '现代版（定制二）',
  //   'type': 'custom',
  //   'groupId': 5,
  //   'description': '在定制版的基础上，调整了列的显示样式，使有更多的空间来展示个人行传。',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/106/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/106/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/106/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/106/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/106/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/blank.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 106,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 2,
  //       'headType': 1,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 0,
  //       'pageOrientation': 0,
  //       'pageSize': 2,
  //       'columnSize': 0,
  //       'offset': 3.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 2,
  //       'direction': 1,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 2,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 1,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': '，',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 2,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 107,
  //   'name': '现代版（定制三）',
  //   'type': 'custom',
  //   'groupId': 5,
  //   'description': '给每个族员都分配一个对应世系位置的编号，并显示统计人数',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/107/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/107/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/107/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/107/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/107/00006.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/blank.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 107,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 2,
  //       'headType': 1,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 0,
  //       'pageOrientation': 0,
  //       'pageSize': 2,
  //       'columnSize': 0,
  //       'offset': 3.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 2,
  //       'direction': 1,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 2,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': '，',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 2,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 111,
  //   'name': '现代版（定制五）',
  //   'type': 'custom',
  //   'groupId': 5,
  //   'description': '无表格线条版式',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/111/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/111/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/111/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/111/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 111,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 2,
  //       'headType': 1,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 0,
  //       'pageOrientation': 0,
  //       'pageSize': 2,
  //       'columnSize': 0,
  //       'offset': 3.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 2,
  //       'direction': 1,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 2,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': '，',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 118,
  //   'name': '现代版（定制六）',
  //   'type': 'custom',
  //   'groupId': 5,
  //   'description': '',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/118/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/118/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/118/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/118/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 118,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 2,
  //       'headType': 0,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 0,
  //       'pageOrientation': 0,
  //       'pageSize': 2,
  //       'columnSize': 0,
  //       'offset': 3.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 2,
  //       'direction': 1,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 2,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': null,
  //       'rankingType': 2,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': '，',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // },
  // {
  //   'id': 117,
  //   'name': '现代相册',
  //   'type': 'normal',
  //   'groupId': 5,
  //   'description': '',
  //   'project': false,
  //   'user': false,
  //   'base': true,
  //   'price': 0,
  //   'current': false,
  //   'cover': null,
  //   'samples': [
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/cover.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/117/00002.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/117/00003.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/117/00004.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/117/00005.png',
  //     'https://cdn.baijiayoupu.com/api/images/print/preview/back.png'
  //   ],
  //   'config': {
  //     'version': 3,
  //     'format': {
  //       'customSize': {
  //         'enable': false,
  //         'fixedRatio': true,
  //         'paddingTop': 0.0,
  //         'paddingRight': 0.0,
  //         'paddingBottom': null,
  //         'version': 1
  //       },
  //       'formatId': 117,
  //       'showList': 1,
  //       'showDiagram': 2,
  //       'showDetail': 1,
  //       'binding': 2,
  //       'headType': 1,
  //       'pageType': 0,
  //       'spacing': 0,
  //       'direction': 0,
  //       'pageOrientation': 0,
  //       'pageSize': 2,
  //       'columnSize': 0,
  //       'offset': 3.0,
  //       'isMap': false
  //     },
  //     'map': {
  //       'height': null,
  //       'width': null,
  //       'shrink': 4,
  //       'memberSpacing': 1,
  //       'memberSpacingAt': null,
  //       'lineColor': 1,
  //       'showSideRemark': 0,
  //       'showDetail': 0,
  //       'showPhoto': 0,
  //       'showTitles': 0,
  //       'showBorder': 0,
  //       'generationType': 0,
  //       'generations': 6,
  //       'iconType': 0,
  //       'iconStyle': 1,
  //       'nameFontSize': 18.0,
  //       'wifeFontSize': 12.0,
  //       'contentFontSize': 12.0,
  //       'rankingFontSize': 12.0,
  //       'iconSize': 18.0,
  //       'scale': [],
  //       'showWifeStatus': 0,
  //       'repeatLastColumn': 1,
  //       'createPreview': 1,
  //       'title': {
  //         'display': 1,
  //         'style': 0,
  //         'borderStyle': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 2,
  //         'marginLeft': 5.0,
  //         'marginRight': 5.0
  //       },
  //       'couplet': {
  //         'topBorder': 1,
  //         'topFont': '',
  //         'topFontSize': 0.0,
  //         'topColor': '000000',
  //         'topAlign': 2,
  //         'border': 1,
  //         'font': '',
  //         'fontSize': 0.0,
  //         'color': '000000',
  //         'align': 1
  //       }
  //     },
  //     'index': {
  //       'pageNumberType': 2,
  //       'direction': 1,
  //       'showBranch': 0,
  //       'withOddPages': 1,
  //       'endWithOddPages': 0,
  //       'diagramIndent': 1,
  //       'detailIndent': 0
  //     },
  //     'header': {
  //       'backTitle': null,
  //       'hasBackTitle': false,
  //       'showBranchTitle': 1,
  //       'pageNumberType': 1,
  //       'pageNumberStyle': 0,
  //       'zeroType': 0,
  //       'showTitle': 1,
  //       'format': 0,
  //       'format2': {
  //         'title': true,
  //         'branch': false,
  //         'generation': true,
  //         'village': false
  //       },
  //       'useSystemCustom': true,
  //       'font': '',
  //       'addHeadSize': 0,
  //       'addTitleSize': 0,
  //       'verticalAlign': 0,
  //       'custom': null
  //     },
  //     'list': {
  //       'showSideRemark': 0,
  //       'titlePosition': 1,
  //       'showDaughter': 1,
  //       'showMultiGenerations': 0,
  //       'showBranchTitle': 1,
  //       'showBei': 0,
  //       'showLetter': 0,
  //       'columnSize': 3,
  //       'numberType': 1,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'listType': 1
  //     },
  //     'diagram': {
  //       'useCustom': 1,
  //       'repeatTitle': 1,
  //       'otherNameInSide': false,
  //       'showSideRemark': 1,
  //       'showWife': 0,
  //       'showChildrenCount': 0,
  //       'showChildrenStatus': 0,
  //       'showDaughter': 0,
  //       'showDaughterChildren': 0,
  //       'showDaughterIcon': 0,
  //       'showIntroduction': 0,
  //       'showBranch': 0,
  //       'newPageByBranch': 0,
  //       'showNameRemark': 0,
  //       'parentPosition': 0,
  //       'titlePosition': 1,
  //       'pageNumberType': 1,
  //       'repeatLastColumn': 1,
  //       'generations': 5,
  //       'generationChangePage': 0,
  //       'showMultiGenerations': 0,
  //       'multiGenerationStyle': 0,
  //       'mainGenerationStyle': 0,
  //       'showMultiBackground': 1,
  //       'showPageIcon': 1,
  //       'showRankingText': 0,
  //       'showLastColumnWife': 0,
  //       'showVillage': 0,
  //       'showBlankTitle': 1,
  //       'spacing': 0,
  //       'memberAlign': 0,
  //       'lineColor': 0,
  //       'memberListType': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       }
  //     },
  //     'detail': {
  //       'memberListType': 0,
  //       'dateFormat': '',
  //       'customDateFormat': [],
  //       'monthFormat': [
  //         1
  //       ],
  //       'dayFormat': [
  //         1
  //       ],
  //       'childrenCountType': 1,
  //       'showBranch': 1,
  //       'showRanking': 1,
  //       'rankingType': 1,
  //       'rankingAll': 0,
  //       'showChildrenRanking': 0,
  //       'showChildrenList': 1,
  //       'childrenWithNewline': 0,
  //       'siName': '嗣{0}',
  //       'tiaoName': '祧{0}',
  //       'yangName': '抚{0}',
  //       'zhuiName': '赘{0}',
  //       'fengName': '奉祀',
  //       'birthType': 1,
  //       'birthWord': ' ',
  //       'deathType': 1,
  //       'deathWord': ' ',
  //       'realNameKey': '又名',
  //       'graveKey': '葬',
  //       'deathWordStyle': 0,
  //       'splitWord': '，',
  //       'splitWord2': 0,
  //       'nameSplitWord': ' ',
  //       'showWifeName': 0,
  //       'wifeNameStyle': 0,
  //       'showNameGeneration': 0,
  //       'wifeNewLine': 0,
  //       'showBei': 0,
  //       'beiOverName': 2,
  //       'showSi': 1,
  //       'showTiao': 1,
  //       'showReservedSpeace': 0,
  //       'showPageNumber': 1,
  //       'reservedAt': null,
  //       'pageWithSmallerGeneration': 0,
  //       'pageWithBrother': 0,
  //       'pageWithBrotherGeneration': null,
  //       'repeatGenerationTitle': 0,
  //       'showBrotherLine': 0,
  //       'lifeTimeFormat': 0,
  //       'lifeTimeType': 1,
  //       'showOtherName': 0,
  //       'showMultiGenerations': 0,
  //       'repeatLastColumn': 0,
  //       'repeatFirstColumn': 0,
  //       'maxContentLineLength': 0,
  //       'fontSize': 13,
  //       'showPreface': 0,
  //       'shrink': 0,
  //       'showOtherNameColumn': 0,
  //       'nameConfig': {
  //         'type': 1,
  //         'deathType': 1,
  //         'deathAppend': 0,
  //         'deathAppend2': 0,
  //         'deathAppendTo': 0,
  //         'deathAppendKey': '公',
  //         'deathAppendKey2': '公'
  //       },
  //       'columnNames102': {
  //         'c1': '世次',
  //         'c2': '传承',
  //         'c3': '姓名',
  //         'c4': '个人信息',
  //         'c5': '别名',
  //         'w1': null,
  //         'w2': null,
  //         'w3': null,
  //         'w4': null
  //       }
  //     },
  //     'display': {
  //       'showTiao': 1,
  //       'showPhoto': 0,
  //       'showSon': 1,
  //       'showDaughter': 1,
  //       'showDaughterList': 1,
  //       'showDaughterInfo': {
  //         'name': true,
  //         'detail': false,
  //         'wife': false,
  //         'wifeDetail': false,
  //         'children': false,
  //         'childrenDetail': false
  //       },
  //       'showWife': 1,
  //       'addVirtualWife': 0,
  //       'addVirtualWifeAt': null,
  //       'addEmptyIntroSpace': 0,
  //       'showWifeRelation': 1,
  //       'showDaughterChildren': 1,
  //       'showParent': 1,
  //       'showParentType': null,
  //       'showParentPosition': 0,
  //       'showParentPage': 1,
  //       'nameType': 1,
  //       'nameBlod': 0,
  //       'deathNameType': 1,
  //       'deathNameAppend': 0,
  //       'deathNameAppend2': 0,
  //       'deathNameAppendTo': 0,
  //       'deathNameAppendKey': '公',
  //       'deathNameAppendKey2': '公',
  //       'showRanking': 1,
  //       'showEmptyMember': 1,
  //       'showTopVillage': 2,
  //       'showVillage': 1,
  //       'showTopBranch': 0,
  //       'hiddenType': 1,
  //       'showVolumeIndex': 1,
  //       'showVolumeList': 1,
  //       'showPageIndex': 1,
  //       'showSingleMember': 1,
  //       'showFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': true
  //       },
  //       'showWifeFields': {
  //         'realName': true,
  //         'otherName': true,
  //         'bei': true,
  //         'school': true,
  //         'education': true,
  //         'major': true,
  //         'job': true,
  //         'grave': true,
  //         'lifeTime': true,
  //         'introduction': true,
  //         'father': true,
  //         'hometown': true,
  //         'birthPlace': true,
  //         'habitationDetail': true,
  //         'sideRemark': true,
  //         'titles': true,
  //         'identity': true,
  //         'preface': true,
  //         'remark': true,
  //         'remark2': true,
  //         'song': true,
  //         'birthDate': true,
  //         'deathDate': true,
  //         'namePrefix': true,
  //         'nameRemark': false
  //       },
  //       'sortFields': [
  //         '前注',
  //         '字辈',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ],
  //       'sortWifeFields': [
  //         '前注',
  //         '父亲',
  //         '头衔',
  //         '常用名',
  //         '别名',
  //         '身份',
  //         '出生日期',
  //         '出生地',
  //         '学校',
  //         '学历',
  //         '工作单位',
  //         '职位',
  //         '简介',
  //         '过世日期',
  //         '享年',
  //         '葬地',
  //         '后注',
  //         '赞词',
  //         '子女列表',
  //         '祖籍',
  //         '现居地',
  //         '后传'
  //       ]
  //     },
  //     'global': {
  //       'language': '简体中文',
  //       'languageConvert': 0,
  //       'languageConvertStart': null,
  //       'languageConvertEnd': null,
  //       'languageConvertStart2': null,
  //       'languageConvertEnd2': null,
  //       'languageConvertTarget': 1,
  //       'addEmptyPage': true,
  //       'generationNumberType': 1,
  //       'generationFormat': 1,
  //       'generationFormatText': '',
  //       'generationFormatPrefix': '',
  //       'generationFormatSuffix': '',
  //       'generationFormatNumber': 1,
  //       'chineseNumber20': false,
  //       'chineseNumber30': false,
  //       'searchNumberType': 1,
  //       'showBranchBackground': 0,
  //       'generationStyle': 1,
  //       'generationStyle2': 1,
  //       'showBranch': 1,
  //       'repeatBranch': 1,
  //       'newPageByBranch': 0,
  //       'memberListType': 0
  //     },
  //     'origin': {},
  //     'font': {
  //       'listName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'listNumber': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'diagramInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailName': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailWife': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       },
  //       'detailInfo': {
  //         'path': null,
  //         'size': null,
  //         'spacing': null,
  //         'lineHeight': null
  //       }
  //     },
  //     'volume': null,
  //     'volumeConfig': {
  //       'resetPageIndex': 1
  //     }
  //   }
  // }
]
export default list
