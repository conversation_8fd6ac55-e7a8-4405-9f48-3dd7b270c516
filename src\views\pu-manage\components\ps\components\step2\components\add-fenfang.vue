<template>
  <a-modal
    class="add-shixi-modal-wrap"
    v-model="visible"
    title="添加分房"
    width="700px"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <div class="content">
      <a-form :form="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }" @submit="handleSubmit">
        <a-form-item label="指定家族" class="form-wrap">
          <a-select
            placeholder="请选择家族"
            v-decorator="['family', {rules: [{ required: false, message: '请选择家族' }]}]"
          >
            <a-select-option :value="1">默认纸张</a-select-option>
            <a-select-option :value="2">横向纸张</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="指定谱系" class="form-wrap">
          <a-select
            placeholder="请选择谱系"
            v-decorator="['clan', {rules: [{ required: false, message: '请选择谱系' }]}]"
          >
            <a-select-option :value="1">默认纸张</a-select-option>
            <a-select-option :value="2">横向纸张</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="分房范围" class="form-wrap">
          <a-select
            placeholder="请选择分房范围"
            v-decorator="['fenfang_rang', {rules: [{ required: false, message: '请选择分房范围' }]}]"
          >
            <a-select-option :value="1">默认纸张</a-select-option>
            <a-select-option :value="2">横向纸张</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="设置支目标题" class="form-wrap">
          <a-select
            placeholder="请选择支目标题"
            v-decorator="['fenzhi_title', {rules: [{ required: false, message: '请选择支目标题' }]}]"
          >
            <a-select-option :value="1">默认纸张</a-select-option>
            <a-select-option :value="2">横向纸张</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="指定起始节点" class="form-wrap">
          <a-input
            suffix=">"
            readOnly
            placeholder="请指定起始节点"
            v-decorator="['start_node', {rules: [{ required: false, message: '请指定起始节点' }]}]"
          />
        </a-form-item>
        <a-form-item label="指定分房世系数" class="form-wrap">
          <a-input
            placeholder="请指定分房世系数"
            v-decorator="['xishu', {rules: [{ required: false, message: '请指定分房世系数' }]}]"
          />
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 24, offset: 0 }" class="form-btn">
          <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
          <a-button type="primary" :loading="confirmLoading" html-type="submit">保存</a-button>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
<script>
export default {
  name: 'Step3AddFenFang',
  data () {
    return {
      visible: true,
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  methods: {
    handleOk () {
      this.$emit('close')
    },
    handleFenFang () {
      this.$emit('close')
    },
    handleSubmit (e) {
      e.preventDefault()
      this.form.validateFields(async (err, values) => {
        if (!err) {
          this.$message.info('保存功能暂未实现')
          this.$emit('close')
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
.add-shixi-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
  .content {
  }
  .form-btn { text-align:center}
}
</style>
