<template>
  <div class="root">
    <tree-node
      v-for="node in nodes"
      :key="node.id"
      :node="node"
      :checked-keys="checkedKeys"
      :last="true"
      :index="0"
      :expanded-keys="expandedKeys"
      :selected-key="selectedKey"
      :no-add="noAdd"
      :no-wife="noWife"
      :no-show-rank="noShowRank"
      :no-show-person-icon="noShowPersonIcon"
      @check="handleCheck"
      @expand="handleExpand"
      @click="handleNodeClick"
      @addBrother="handleAddBrother"
      @addChild="handleAddChild"
    ></tree-node>
  </div>
</template>

<script>
import TreeNode from './TreeNode.vue'

export default {
  name: 'AntTree',
  components: {
    TreeNode
  },
  props: {
    // 树节点数据
    nodes: {
      type: Array,
      default: () => []
    },
    // 是否显示复选框
    checkable: {
      type: Boolean,
      default: false
    },
    // 默认展开的节点
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    // 默认选中的节点
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    selectedKey: {
      type: [Number, String],
      default: ''
    },
    noAdd: {
      type: Boolean,
      default: false
    },
    noWife: {
      type: Boolean,
      default: false
    },
    noShowRank: {
      type: Boolean,
      default: false
    },
    noShowPersonIcon: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      list: [],
      expandedKeys: [...this.defaultExpandedKeys],
      checkedKeys: [...this.defaultCheckedKeys]
    }
  },
  watch: {
    nodes: {
      handler (val) {
        this.list = val
      },
      immediate: true,
      deep: true
    },
    defaultExpandedKeys: {
      handler (val) {
       this.expandedKeys = val
      },
      immediate: true,
      deep: true
    },
    defaultCheckedKeys: {
      handler (val) {
        this.checkedKeys = val
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 处理节点展开/折叠
    handleExpand (key, expanded) {
      const index = this.expandedKeys.indexOf(key)
      if (expanded && index === -1) {
        this.expandedKeys.push(key)
      } else if (!expanded && index !== -1) {
        this.expandedKeys.splice(index, 1)
      }
      this.$emit('expand', this.expandedKeys)
    },
    // 处理复选框选择
    handleCheck (key, checked) {
      if (checked) {
        if (!this.checkedKeys.includes(key)) {
          this.checkedKeys.push(key)
        }
      } else {
        const index = this.checkedKeys.indexOf(key)
        if (index !== -1) {
          this.checkedKeys.splice(index, 1)
        }
      }
      this.$emit('check', this.checkedKeys)
    },
    // 处理节点点击
    handleNodeClick (key, node) {
      this.$emit('click', key, node)
    },
    handleAddBrother (key, node) {
      this.$emit('addBrother', key, node)
    },
    handleAddChild (key, node) {
      this.$emit('addChild', key, node)
    }
  }
}
</script>

<style scoped lang='less'>
.root {
  padding-right: 57px;
  padding-bottom: 20px;
  width: fit-content;
  .node {
    margin-left: 0;
  }
  .node.border {
    border-left: 1px solid #f14d4d;
  }
  .node.last-item {
    border-left: none !important;
  }
}
</style>
