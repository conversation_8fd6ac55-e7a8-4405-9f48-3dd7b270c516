<template>
  <div class="login-out-component">
    <a-popover title="">
      <template slot="content">
        <p class="c-pointer-hover" @click="handleExit">退出</p>
      </template>
      <i class="cus-edit-toolbar-icon icon-edit-toolbarshezhi c-pointer-hover" title="退出" />
    </a-popover>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  name: 'LoginOut',
  data () {
    return {
    }
  },
  methods: {
    ...mapActions(['Logout']),
    handleExit () {
      this.Logout()
      this.$router.push({ path: '/user/login' })
    }
  }
}
</script>
<style lang="less" scoped>
.login-out-component {
  position:fixed;
  bottom: 50px;
  left:38px;
  width:22px;
  z-index:11;

  i { color:#fff}
}
</style>
