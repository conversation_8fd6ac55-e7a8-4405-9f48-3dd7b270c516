<template>
  <div :type="getValue('type')">
    <template v-if="getValue('type') === 2">
      <div class="vline line-top" :style="{ left: getValue('x') + 'px' }" />
      <div class="vline line-bottom" v-if="getValue('hasChildren')" :style="{ left: getValue('x') + 'px' }" />
      <div class="item" :style="{ left: getValue('x') + 'px', bottom: getValue('y') + 'px' }">
        <div class="name">
          <i class="cus-edit-toolbar-icon icon-edit-toolbarcircle2yuanquan f12" />
          <span>{{ getValue('text') }}</span>
        </div>
      </div>
    </template>
    <template v-else-if="getValue('type') === 3">
      <div class="line" :style="{ left: getValue('x') + 'px', bottom: getValue('y') + 'px', width: getValue('width') + 'px' }"></div>
    </template>
  </div>
</template>
<script>
import { get } from 'lodash'
export default {
  name: 'PageType2ComponentItem',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    getValue (field) {
      return get(this.item, field, '')
    }
  }
}
</script>
<style lang="less" scoped>
.vline {
  position: absolute;
  height: 10px;
  width: 2px;
  background-color: #333;
  transform: translateX(8px)
}
.vline.line-top {
  height: 35px;
}
.vline.line-bottom {
  bottom: 0;
  height: calc(100% - 35px);
}
.item{
  position: absolute;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 16px;
  padding-top: 35px;
  box-sizing: border-box;
  white-space: nowrap;
  .name{
    flex-shrink: 0;
    line-height: 1;
    writing-mode: vertical-lr;
    font-size: 0;
    background-color: #fff;
    padding: 0 0 3px;
    span{
      font-size: 16px;
    }
    i{
      font-size: 12px;
    }
  }
}
.line {
  height: 2px;
  position: absolute;
  background-color: #333;
  transform: translateX(8px)
}
</style>
