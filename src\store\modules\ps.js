import {
  saveClanBook,
  saveCoverConfig,
  deleteClanBook,
  getClanBookPrintPlan,
  getBookDetail,
  getBookPrintDetail,
  saveBookPrintContent
} from '@/api/ps'
import storage from 'store'
import { PS_SELECTED_ITEM_TYPE_ZHENG_WEN } from '@/store/modules/ps-action-type'
const initPSStructure = [
  { // 卷首
    id: -1, // 主键ID,-1为新增，>0为修改
    groupId: 0, // 分组ID,1卷首，2正文目录，3世系目录，4附录
    clanId: -1, // 族谱id 【进入第二步编辑内容时 进行初始化修改】
    bookPrintId: -1, // 书籍印刷id 【进入第二步编辑内容时 进行初始化修改】
    level: 0,
    status: 1, // 1: 正常 2: 删除
    parentId: 0 // 0: 表示顶层 >0 表示有父级
  },
  { // 正文目录
    id: -1, // 主键ID,-1为新增，>0为修改
    groupId: 1, // 分组ID,1卷首，2正文目录，3世系目录，4附录
    clanId: -1, // 族谱id 【进入第二步编辑内容时 进行初始化修改】
    bookPrintId: -1, // 书籍印刷id 【进入第二步编辑内容时 进行初始化修改】
    level: 0,
    status: 1, // 1: 正常 2: 删除
    parentId: 0 // 0: 表示顶层 >0 表示有父级
  },
  { // 世系目录
    id: -1, // 主键ID,-1为新增，>0为修改
    groupId: 2, // 分组ID,1卷首，2正文目录，3世系目录，4附录
    clanId: -1, // 族谱id 【进入第二步编辑内容时 进行初始化修改】
    bookPrintId: -1, // 书籍印刷id 【进入第二步编辑内容时 进行初始化修改】
    level: 0,
    status: 1, // 1: 正常 2: 删除
    parentId: 0 // 0: 表示顶层 >0 表示有父级
  },
  { // 附录
    id: -1, // 主键ID,-1为新增，>0为修改
    groupId: 3, // 分组ID,1卷首，2正文目录，3世系目录，4附录
    clanId: -1, // 族谱id 【进入第二步编辑内容时 进行初始化修改】
    bookPrintId: -1, // 书籍印刷id 【进入第二步编辑内容时 进行初始化修改】
    level: 0,
    status: 1, // 1: 正常 2: 删除
    parentId: 0 // 0: 表示顶层 >0 表示有父级
  }
]
const ps = {
  state: {
    step: 1, // 步骤
    currentSelectedBook: null,
    currentSelectedBookDetail: null,
    step2SelectedItemText: PS_SELECTED_ITEM_TYPE_ZHENG_WEN, // 当前选中的目录项(卷首、正文目录、世系目录、附录)
    step2BookStructure: [ ...initPSStructure ], // 数据结构
    step2BookPrintDetail: {} // 保存接口获取的打印设置
  },
  mutations: {
    SET_STEP2_BOOK_PRINT_DETAIL: (state, bookPrintDetail) => {
      state.step2BookPrintDetail = bookPrintDetail
      storage.set('step2BookPrintDetail', bookPrintDetail)
    },
    SET_STEP2_SELECTED_ITEM_TEXT: (state, text) => {
      state.step2SelectedItemText = text
      storage.set('step2SelectedItemText', text)
    },
    SET_SELECTED_BOOK: (state, book) => {
      state.currentSelectedBook = book
      storage.set('currentSelectedBook', book)
    },
    SET_SELECTED_BOOK_DETAIL: (state, bookDetail) => {
      state.currentSelectedBookDetail = bookDetail
      storage.set('currentSelectedBookDetail', bookDetail)
    },
    SET_STEP: (state, step) => {
      state.step = step
      storage.set('step', step)
    },
    SET_STEP2_BOOK_STRUCTURE: (state, params) => {
      const { index, content } = params
      state.step2BookStructure[index] = {
        ...state.step2BookStructure[index],
        ...content
      }
    },
    RESET_STEP2_BOOK_STRUCTURE: (state) => {
      state.step2BookStructure = { ...initPSStructure }
    }
  },
  actions: {
    UpdateSelectedBook ({ commit }, payload) {
      commit('SET_SELECTED_BOOK', payload)
    },
    UpdateStep ({ commit }, payload) {
      commit('SET_STEP', payload)
    },
    UpdateStep2SelectedItemText ({ commit }, payload) {
      commit('SET_STEP2_SELECTED_ITEM_TEXT', payload)
    },
    UpdateStep2BookStructure ({ commit }, payload) {
      commit('SET_STEP2_BOOK_STRUCTURE', payload)
    },
    ResetStep2BookStructure ({ commit }) {
      commit('RESET_STEP2_BOOK_STRUCTURE')
    },
    async SaveClanBook ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        saveClanBook(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async SaveCoverConfig ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        saveCoverConfig(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async DeleteClanBook ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        deleteClanBook(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetClanBookPrintPlan ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        getClanBookPrintPlan(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetBookDetail ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        getBookDetail(payload)
          .then(json => {
            commit('SET_SELECTED_BOOK_DETAIL', json?.data)
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetBookPrintDetail ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        getBookPrintDetail(payload)
          .then(json => {
            commit('SET_STEP2_BOOK_PRINT_DETAIL', json?.data)
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async SaveBookPrintContent ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        saveBookPrintContent(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    }
  }
}

export default ps
