/**
 * 获取指定层级的所有节点 ID
 * @param tree
 * @param startLevel
 * @param endLevel
 * @returns {*[]}
 */
export const getSpecificLevelIdList = (tree, startLevel = 1, endLevel = 3) => {
  const result = []

  // 遍历树节点
  function traverse (node, currentLevel) {
    // 如果节点不存在，直接返回
    if (!node) return

    // 如果当前层级在指定范围内，收集当前节点的 Id
    if (currentLevel >= startLevel && currentLevel <= endLevel) {
      result.push(node.id)
    }

    // 如果存在子节点，则递归遍历子节点
    if (node.children) {
      for (const child of node.children) {
        traverse(child, currentLevel + 1)
      }
    }
  }

  // 从根节点开始遍历
  for (const root of tree) {
    traverse(root, 1)
  }

  return result
}
