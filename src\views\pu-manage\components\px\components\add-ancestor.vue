<template>
  <a-modal
    class="add-ancestor-modal-wrap"
    v-model="visible"
    title="添加始祖节点"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <a-form :form="form" :label-col="{ span: 7 }" :wrapper-col="{ span: 16 }" @submit="handleSubmit">
      <a-form-item label="请输入始祖谱名">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: true, message: '请输入始祖谱名!' }] }]" />
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" html-type="submit" :loading="loading">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import { getHandleName } from '@/utils/nameUtil'
export default {
  name: 'AddAncestor',
  components: {
  },
  data () {
    return {
      visible: true,
      loading: false,
      form: this.$form.createForm(this, { name: 'xxx' })
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  created () {
  },
  mounted () {
  },
  methods: {
    ...mapActions(['AddAncestor', 'RefreshPedigreeMemberList']),
    handleOk () {
    },
    handleSubmit (e) {
      e.preventDefault()
      const { AddAncestor, RefreshPedigreeMemberList } = this
      const that = this
      this.form.validateFields(async (err, values) => {
        if (!err) {
          const { firstName, name, fullName } = getHandleName(values.name)
          const params = {
            'clan_id': this.currentSelectedClanId,
            'pedigree_id': this.currentSelectedPedigreeId,
            'generation': 1,
            'first_name': firstName,
            'name': name,
            'full_name': fullName,
            'ranking': 1,
            'ranking_text': '始祖',
            'relation': '生',
            'sex': '男'
          }
          this.loading = true
          await AddAncestor(params)
          this.loading = false
          that.$emit('close')
          await RefreshPedigreeMemberList()
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
.add-ancestor-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
}
</style>
