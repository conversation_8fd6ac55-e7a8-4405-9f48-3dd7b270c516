<template>
  <div class="wrap">
    <div class="item">
      <div class="img">
        <div class="inner">
          <img :src="type1_1" />
          <div class="info">
            <div class="title" style="font-size: 70px;">
              <div v-for="(str, index) in data?.title" :key="index">{{ str }}</div>
            </div>
            <div class="sub-title">
              <div v-for="(str, index) in data?.sub_title" :key="index">{{ str }}</div>
            </div>
            <div class="volume">
              <div>
                <div v-for="(str, index) in data?.hall" :key="index">{{ str }}</div>
              </div>
              <div class="space"></div>
              <div>
                <div v-for="(str, index) in data?.volume" :key="index">{{ str }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="name">封面</div>
    </div>
    <div class="item">
      <div class="img">
        <div class="inner">
          <img :src="type1_2" />
        </div>
      </div>
      <div class="name">封底</div>
    </div>
  </div>
</template>
<script>
import type1_1 from '@/assets/prints/type1-1.png'
import type1_2 from '@/assets/prints/type1-2.png'
export default {
  name: 'Cover1Preview',
  data () {
    return {
      type1_1,
      type1_2
    }
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  }
}
</script>
<style lang="less" scoped>
.wrap{
  display:flex;
  flex-wrap:wrap;
  .item {
    width: 380px;
    height: 600px;
    margin-left: 20px;
    .img {
      height: 540px;
      .inner {
        width: 760px;
        height: 1080px;
        position: relative;
        transform: scale(.5);
        transform-origin: 0 0;
        img {
          border: none;
          max-width: 100%;
        }
        .info {
          .title{
            position: absolute;
            left: 533px;
            top: 65px;
            width: 146px;
            height: 743px;
            text-align: center;
            font-size: 70px;
            line-height: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
          }
          .sub-title {
            position: absolute;
            left: 650px;
            top: 85px;
            width: 20px;
            height: 743px;
            text-align: center;
            font-size: 20px;
            line-height: 1.3;
          }
          .volume {
            position: absolute;
            left: 544px;
            top: 65px;
            width: 20px;
            height: 723px;
            text-align: center;
            font-size: 20px;
            line-height: 1.3;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
          }
          .space {
            height: 20px;
          }
        }
      }
    }
    .name {
      font-size: 20px;
    }
  }
}
</style>
