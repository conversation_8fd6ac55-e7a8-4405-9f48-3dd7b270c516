<template>
  <div class="flex step2-content-add-btn flex-direction-column">
    <div class="flex flex-direction-row align-center justify-items-center btn">
      <div class="c-pointer-hover mr16" @click="handleAddChapter">+添加章节</div>
      <div class="c-pointer-hover mr16" @click="handleAddPuWen">+添加谱文</div>
      <div class="c-pointer-hover  mr16" @click="handleAddShiXi">+添加世系</div>
      <div class="c-pointer-hover" @click="handleAddYuanLiu">+添加源流</div>
    </div>
    <div class="tips">您还没有正文目录内容，赶快新建吧~</div>
    <add-chapter v-if="showAddChapter" @close="showAddChapter=false" />
    <step3-add-pu-wen v-if="showAddPuWen" @close="showAddPuWen=false" />
    <step3-add-shi-xi v-if="showAddShiXi" @close="showAddShiXi=false" @fenFang="showAddFenFang=true" />
    <step3-add-yuan-liu v-if="showAddYuanLiu" @close="showAddYuanLiu=false" />
    <step3-add-fen-fang v-if="showAddFenFang" @close="showAddFenFang=false" />
  </div>
</template>
<script>
import AddChapter from '@/views/pu-manage/components/ps/components/step2/components/add-chapter'
import Step3AddPuWen from '@/views/pu-manage/components/ps/components/step2/components/add-puwen'
import Step3AddShiXi from '@/views/pu-manage/components/ps/components/step2/components/add-shixi'
import Step3AddYuanLiu from '@/views/pu-manage/components/ps/components/step2/components/add-yuanliu'
import Step3AddFenFang from '@/views/pu-manage/components/ps/components/step2/components/add-fenfang'

export default {
  name: 'Step2EditContentAddBtn',
  components: { Step3AddYuanLiu, Step3AddShiXi, Step3AddPuWen, AddChapter, Step3AddFenFang },
  data () {
    return {
      showAddChapter: false,
      showAddPuWen: false,
      showAddShiXi: false,
      showAddYuanLiu: false,
      showAddFenFang: false
    }
  },
  methods: {
    handleAddChapter () {
      this.showAddChapter = true
    },
    handleAddPuWen () {
      this.showAddPuWen = true
    },
    handleAddShiXi () {
      this.showAddShiXi = true
    },
    handleAddYuanLiu () {
      this.showAddYuanLiu = true
    }
  }
}
</script>
<style lang="less" scoped>
.step2-content-add-btn {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
  text-align:center;
  .btn {
    text-align:center;
    justify-items: center;
    justify-content:center;
    div {
      justify-content:center;
      align-content: center;
      height: 34px;
      width: 100px;
      border:1px solid #1890ff;
      color: #1890ff;
    }
  }
  .tips {
    margin-top: 15px;
    font-size: 14px;
    color: #999;
  }
}
</style>
