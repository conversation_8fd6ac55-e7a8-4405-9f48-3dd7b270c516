import storage from 'store'
import cloneDeep from 'lodash.clonedeep'
import { v4 as uuidv4 } from 'uuid'
import * as initDefaultData from '@/components/Editor/components/initDefaultData'
import {
  addCategory,
  deletedCategory,
  updateCategory,
  addClanArticle,
  updateClanArticle,
  getCategoryList,
  getClanArticleList,
  getClanPrintConfigList
} from '@/api/pw'

const pw = {
  // namespaced: true,
  state: {
    activeEditItemId: '',
    currentPageNum: 1,
    data: [cloneDeep(initDefaultData.TextBoxData)],
    actionType: '',
    actionPayload: {}
  },

  mutations: {
    SET_CurrentPageNum: (state, currentPageNum) => {
      state.currentPageNum = currentPageNum
      storage.set('currentPageNum', currentPageNum)
    },
   SET_ActiveEditItemId: (state, activeEditItemId) => {
      state.activeEditItemId = activeEditItemId
      storage.set('activeEditItemId', activeEditItemId)
    },
    SET_ActionType: (state, actionType, payload) => {
      state.actionType = actionType
      state.actionPayload = payload || {}
      storage.set('actionType', actionType)
      storage.set('payload', payload)
    },
    SET_Data: (state, payload) => {
      state.data = payload
      storage.set('data', payload)
    },
    SET_CurrentData: (state, payload) => {
      state.data = payload
      storage.set('data', payload)
    },
    SET_InsertTextBox: (state, payload) => {
     state.data[state.currentPageNum - 1].items.push(payload)
      state.activeEditItemId = payload.id
    },
    SET_DeleteTextBox: (state) => {
     const items = state.data[state.currentPageNum - 1].items.filter(item => item.id !== state.activeEditItemId)
      state.data[state.currentPageNum - 1].items = cloneDeep(items)
      state.activeEditItemId = ''
    },
    SET_InsertPage: (state, payload) => {
      const data = cloneDeep(initDefaultData.TextBoxData)
      data.items[0].id = `${uuidv4()}`
      data.pageNum = state.data.length + 1
      state.data.push(data)
    }
  },

  actions: {
      UpdateCurrentPageNum ({ commit }, payload) {
        commit('SET_CurrentPageNum', payload)
      },
      UpdateCurrentPageData ({ commit }, payload) {
        commit('SET_CurrentData', payload)
      },
      UpdateActionType ({ commit }, actionType) {
        commit('SET_ActionType', actionType)
     },
    UpdateActiveEditItemId ({ commit }, activeEditItemId) {
      commit('SET_ActiveEditItemId', activeEditItemId)
    },
    InsertTextBox ({ commit }, payload) {
      commit('SET_InsertTextBox', payload)
    },
    DeleteTextBox ({ commit }) {
      commit('SET_DeleteTextBox')
    },
    async AddCategory ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addCategory(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async DeletedCategory ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        deletedCategory(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async UpdateCategory ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        updateCategory(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async AddClanArticle ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addClanArticle(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async UpdateClanArticle ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        updateClanArticle(payload)
          .then(json => {
            resolve(json)
          })
      })
    },
    async GetCategoryList ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        getCategoryList(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetClanArticleList ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        getClanArticleList(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetClanPrintConfigList ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        getClanPrintConfigList(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    InsertPage ({ commit }, payload) {
      commit('SET_InsertPage', payload)
    }
  }
 }
export default pw
