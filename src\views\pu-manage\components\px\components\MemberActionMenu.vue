<template>
  <div class="flex flex-direction-column first-column text-center">
    <!-- 展开/折叠按钮 -->
    <div class="mt8 c-pointer" @click="handleToggleBase">
      <template v-if="showBase || showBaseDetail">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarzhankai-xia" />
      </template>
      <template v-else>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarshouqi" />
      </template>
    </div>

    <!-- 添加远祖 -->
    <div class="mt8 c-pointer" @click="handleAddAncestor">
      <i class="cus-edit-toolbar-icon icon-edit-toolbara-icon_xiongmei" />
      <div>+远祖</div>
    </div>

    <!-- 添加配偶 -->
    <div class="mt8 c-pointer" @click="handleAddWife">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarpeiou" />
      <div>+配偶</div>
    </div>

    <!-- 添加兄妹 -->
    <div class="mt8 c-pointer" @click="handleAddBrother">
      <i class="cus-edit-toolbar-icon icon-edit-toolbara-icon_xiongmei" />
      <div>+兄妹</div>
    </div>

    <!-- 添加儿女 -->
    <div class="mt8 c-pointer" @click="handleAddChild">
      <i class="cus-edit-toolbar-icon icon-edit-toolbara-icon_ernv" />
      <div>+儿女</div>
    </div>

    <!-- 删除 -->
    <div class="mt8 c-pointer" @click="handleDeleteNode">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1" />
      <div>删除</div>
    </div>

    <!-- 过继 -->
    <div class="mt8 c-pointer" @click="handleGuoji">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarfenzhijiedian" />
      <div>过继</div>
    </div>

    <!-- 标记 -->
    <div class="mt8 c-pointer" @click="handleBiaoji">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarstar" />
      <div>标记</div>
    </div>

    <!-- 分房 -->
    <div class="mt8 c-pointer">
      <a-popover title="" placement="leftTop">
        <template slot="content">
          <p class="c-pointer-hover" @click="handleFenFang(1)">分房</p>
          <p class="c-pointer-hover" @click="handleFenFang(2)">分屋</p>
          <p class="c-pointer-hover" @click="handleFenFang(3)">分支</p>
          <p class="c-pointer-hover" @click="handleFenFang(4)">分派</p>
        </template>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarfangzi" />
        <div>分房</div>
      </a-popover>
    </div>

    <!-- 统计 -->
    <div class="mt8 c-pointer" @click="handleTongji">
      <i class="cus-edit-toolbar-icon icon-edit-toolbartongjifenxi" />
      <div>统计</div>
    </div>
  </div>
</template>

<script>
import * as actionTypes from '@/store/modules/px-action-type'

export default {
  name: 'MemberActionMenu',
  props: {
    showBase: {
      type: Boolean,
      default: true
    },
    showBaseDetail: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      actionTypes
    }
  },
  methods: {
    handleToggleBase () {
      this.$emit('toggleBase')
    },
    handleAddAncestor () {
      this.$emit('action', actionTypes.PX_RIGHT_OPTION_ADD_ANCESTOR)
    },
    handleAddWife () {
      this.$emit('action', actionTypes.PX_RIGHT_OPTION_ADD_WIFE)
    },
    handleAddBrother () {
      this.$emit('action', actionTypes.PX_RIGHT_OPTION_ADD_BROTHER)
    },
    handleAddChild () {
      this.$emit('action', actionTypes.PX_RIGHT_OPTION_ADD_CHILD)
    },
    handleDeleteNode () {
      this.$emit('action', actionTypes.PX_RIGHT_OPTION_DELETE_NODE)
    },
    handleGuoji () {
      this.$emit('showModal', 'guoji')
    },
    handleBiaoji () {
      this.$emit('showModal', 'biaoji')
    },
    handleFenFang (type) {
      this.$emit('fenFang', type)
    },
    handleTongji () {
      this.$emit('showModal', 'tongji')
    }
  }
}
</script>

<style lang="less" scoped>
.first-column {
  width: 60px;
  background-color: #fff;
  height: calc(100vh - 70px);
}
</style>
