<template>
  <div>
    <div class="title-bar">体例明细（0/3）</div>
    <div class="list" style="display:none">
      <ps-list-item selected/>
      <ps-list-item />
      <ps-list-item />
    </div>
    <div class="table-empty">
      <div class="flex align-center btn-v">
        <div><a-button>编辑内容</a-button></div>
        <div><i class="cus-edit-toolbar-icon icon-edit-toolbaryoujiantou2" /></div>
        <div><a-button>选择版式</a-button></div>
        <div><i class="cus-edit-toolbar-icon icon-edit-toolbaryoujiantou2" /></div>
        <div><a-button>生成下载</a-button></div>
      </div>
      <div class="tips">您还没有生成家谱体例，赶块按照步骤生成吧~</div>
    </div>
  </div>
</template>
<script>
import PsListItem from '@/views/pu-manage/components/ps/components/list-item'
export default {
  name: 'DetailListContentForPs',
  components: { PsListItem },
  data () {
    return {}
  }
}
</script>
<style lang="less" scoped>
.title-bar {
  font-size: 18px;
  padding: 30px 0;
  text-align:left;
}
.list {
  display:flex;
  flex-direction: column;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5
}
.table-empty{
  border-top:1px solid #e5e5e5;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  border-bottom: 1px solid #e6e6e6;
  .btn-v {
    button {
      border:1px solid #1890ff;
      color: #1890ff;
    }
    i{
      color: #f86e04;
      margin: 0 15px
    }
  }
  .tips {
    margin-top: 10px
  }
}
</style>
