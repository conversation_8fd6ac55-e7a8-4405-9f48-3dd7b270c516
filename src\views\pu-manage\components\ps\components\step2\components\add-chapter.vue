<template>
  <a-modal
    class="add-chapter-modal-wrap"
    v-model="visible"
    title="添加目录"
    @cancel="$emit('close')"
  >
    <a-form :form="form" :label-col="{ span: 7 }" :wrapper-col="{ span: 16 }" @submit="handleSubmit">
      <a-form-item label="目录名称">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: true, message: '请输入目录名称!' }] }]" />
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" html-type="submit" :loading="loading">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import { getUpdateStep2BookStructureIndex } from '@/views/pu-manage/components/ps/components/step2/utils'

export default {
  name: 'AddChapter',
  components: {
  },
  data () {
    return {
      visible: true,
      loading: false,
      form: this.$form.createForm(this, { name: 'xxx' })
    }
  },
  computed: {
    ...mapState({
      step2SelectedItemText: state => state.ps.step2SelectedItemText,
      step2BookStructure: state => state.ps.step2BookStructure,
      currentSelectedBook: state => state.ps.currentSelectedBook,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  created () {
  },
  mounted () {
  },
  methods: {
    ...mapActions(['UpdateStep2BookStructure']),
    handleSubmit (e) {
      e.preventDefault()
      const { step2SelectedItemText, UpdateStep2BookStructure } = this
      this.form.validateFields(async (err, values) => {
        if (!err) {
          const index = getUpdateStep2BookStructureIndex(step2SelectedItemText)
          if (index > -1) {
            UpdateStep2BookStructure({
              index,
              content: { title: values.name }
            })
            this.$emit('close')
          }
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
.add-chapter-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
}
</style>
