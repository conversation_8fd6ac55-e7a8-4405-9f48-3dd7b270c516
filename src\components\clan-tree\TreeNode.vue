<template>
  <div :class="getCurrentClass()">
    <div class="border-left " :class="getStyleNumClass()" />
    <div class="item" style="margin-left: 0px">
      <div class="before">
        <i class="icon" title="无编辑权限" />
      </div>
      <div class="switch cus-edit-toolbar-icon" :class="getExpandIconClass()" @click.stop="toggleExpand"></div>
      <div class="padding" style="width: 89px;">
        <div class="line" :class="getStyleNumClass()"></div>
        <!--        选择图标  -->
        <i
          v-if="showSelectedICon"
          class="cus-edit-toolbar-icon icon-edit-toolbarxuanze c-pointer-hover ml4" />
        <!--        世系  -->
        <span>[{{ node.generation }}世]</span>
      </div>
      <div class="name" :class="selectedKey === node.id ? 'selected' : ''">
        <!--        小人 图标 -->
        <i
          class="cus-edit-toolbar-icon"
          :class="getPersonIconClass(node.sex)"
          v-if="!noShowPersonIcon"
          style="font-size: 16px;" />
        <!--        姓氏 + 名称  -->
        <div v-show="node.writeMode === 2" class="text" @click="handleNodeClick">
          <button class="btn-name">
            <span>{{ node.first_name }}</span>
            <span>{{ node.name }}</span>
          </button>
        </div>
        <div v-if="!noAdd && node.writeMode === 1" class="text mode-1">
          <a-input size="small" v-model="childName" @pressEnter="handleSaveChildren" />
          <i
            class="cus-edit-toolbar-icon cus-edit-toolbar-icon icon-edit-toolbarbaocun c-pointer-hover mr6"
            title="保存该项"
            @click="handleSaveChildren" />
          <i
            class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover"
            @click="handleDelChildren"
            title="删除该项" />
        </div>
        <!--        排行    -->
        <span v-if="!noShowRank">[{{ node.ranking_text }}]</span>
        <!--        配偶    -->
        <div
          v-if="!noWife"
          v-for="(wife, wifeIndex) in (node?.wife || [])"
          class="name wife-item"
          :key="wife.id"
          :class="selectedKey === wife.id ? 'selected wife-selected' : ''"
          :style="{ marginTop: wifeIndex > 0 ? '4px' : '0' }"
          @click="handleWifeClick(wife)">
          <i class="cus-edit-toolbar-icon" :class="getPersonIconClass(wife.sex)" />
          <div class="wife-content">
            <span class="wife-text">{{ wife.full_name || '' }}</span>
          </div>
        </div>
        <div
          v-if="!noAdd && (selectedKey === node.id || (node.wife && node.wife.some(w => w.id === selectedKey)))"
          class="name">
          <i
            class="cus-edit-toolbar-icon icon-edit-toolbarnvce"
            style="color: #f5abab; font-size: 14px;"
            v-show="isAddWife" />
          <a-input
            size="small"
            v-show="isAddWife"
            v-model.trim="wifeName"
            style="width: 80px;margin-right: 10px;"
            @pressEnter="handleSaveWife" />
          <i
            class="cus-edit-toolbar-icon icon-edit-toolbarbaocun c-pointer-hover mr6"
            title="保存该项"
            @click="handleSaveWife"
            v-show="isAddWife" />
          <i
            class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover"
            @click="handleDelWife"
            title="删除该项"
            v-show="isAddWife" />
          <i
            class="cus-edit-toolbar-icon icon-edit-toolbarnvce"
            style="color: #f5abab; font-size: 14px;"
            v-show="!isAddWife" />
          <div class="more-wife" @click="isAddWife = true" v-show="!isAddWife">添加配偶</div>
        </div>
        <div class="name-top" v-if="node.branch_tag">
          <span class="tag">{{ getTagName(node.branch_tag) }}</span>
          <span class="branch">[{{ node.branch_name }}]</span>
        </div>
      </div>
    </div>
    <div
      class="children"
      :class="selectedKey === node.id ? 'selected-node' : ''"
      v-if="expanded && node.children && node.children.length">
      <div style="margin-left: 0px;">
        <tree-node
          v-for="(nd, idx) in node.children"
          :key="nd.id"
          :node="nd"
          :checked-keys="checkedKeys"
          :expanded-keys="expandedKeys"
          :selected-key="selectedKey"
          :index="index + 1"
          :last="idx === node.children.length - 1"
          :fatherNode="node"
          :no-add="noAdd"
          :no-wife="noWife"
          :no-show-rank="noShowRank"
          :no-show-person-icon="noShowPersonIcon"
          @check="$listeners.check"
          @expand="$listeners.expand"
          @click="$listeners.click"
          @wifeClick="$listeners.wifeClick"
          @addBrother="$listeners.addBrother"
          @addChild="$listeners.addChild" />
      </div>
    </div>
    <div class="more-child" v-if="!noAdd && shouldShowAddChild" style="margin-left: 0px;">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarnance" />
      <div class="text" @click="handleAddChild">添加孩子</div>
    </div>
    <div class="more-child more-brother" v-if="!noAdd && shouldShowAddBrother" style="margin-left: 0px;">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarnance" />
      <div class="text" @click="handleAddBrother">添加兄妹</div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import { getHandleName } from '@/utils/nameUtil'
import { getRankText } from '@/utils/rankTextUtil'
import * as actionTypes from '@/store/modules/px-action-type'
import { TagTypeMap } from '@/constant'

export default {
  name: 'TreeNode',
  inheritAttrs: false,
  props: {
    node: {
      type: Object,
      required: true
    },
    fatherNode: {
      type: Object,
      default: () => { }
    },
    expandedKeys: {
      type: Array,
      default: () => []
    },
    checkedKeys: {
      type: Array,
      default: () => []
    },
    index: {
      type: Number,
      default: 0
    },
    last: {
      type: Boolean,
      default: false
    },
    selectedKey: {
      type: [Number, String],
      default: -9999
    },
    showSelectedICon: {
      type: Boolean,
      default: false
    },
    noAdd: {
      type: Boolean,
      default: false
    },
    noWife: {
      type: Boolean,
      default: false
    },
    noShowRank: {
      type: Boolean,
      default: false
    },
    noShowPersonIcon: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      wifeName: '',
      isAddWife: false,
      childName: this.node.first_name,
      expanded: this.expandedKeys.includes(this.node.key),
      checked: false,
      hasBeenExpanded: false, // 记录节点是否曾经被展开过
      isLoadingChildren: false // 记录是否正在加载子节点数据
    }
  },
  computed: {
    ...mapState({
      quickOperationsType: state => state.px.quickOperationsType,
      currentPedigreeMemberList: state => state.px.currentPedigreeMemberList,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    }),
    shouldShowAddChild () {
      // 当选中主支成员时显示添加孩子按钮
      // 当选中配偶时也显示添加孩子按钮（配偶可以添加孩子）
      if (this.selectedKey === this.node.id) {
        // 选中的是主支成员
        return true
      }

      // 检查是否选中了该节点的配偶
      if (this.node.wife && this.node.wife.length > 0) {
        const selectedWife = this.node.wife.find(wife => wife.id === this.selectedKey)
        if (selectedWife) {
          // 选中的是配偶，显示添加孩子按钮
          return true
        }
      }

      return false
    },
    shouldShowAddBrother () {
      // 只有当选中主支成员时才显示添加兄妹按钮
      // 选中配偶时不显示添加兄妹按钮
      if (this.selectedKey === this.node.id) {
        // 选中的是主支成员，显示添加兄妹按钮
        return true
      }

      return false
    }
  },
  watch: {
    expandedKeys: {
      handler (newVal) {
        this.expanded = newVal.includes(this.node.key)
      },
      immediate: true
    },
    // 监听 checkedKeys 的变化，更新当前节点的选中状态
    checkedKeys: {
      handler (newVal) {
        this.checked = newVal.includes(this.node.key)
      },
      immediate: true
    },
    quickOperationsType: {
      handler (newVal) {
        if (newVal && this.node.id === this.selectedKey) {
          this.ResetQuickOperationsType()
          switch (newVal) {
            case actionTypes.PX_RIGHT_OPTION_ADD_BROTHER:
              this.handleAddBrother()
              break
            case actionTypes.PX_RIGHT_OPTION_ADD_CHILD:
              this.handleAddChild()
              break
            case actionTypes.PX_RIGHT_OPTION_ADD_WIFE:
              this.isAddWife = true
              break
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions([
      'AddMemberChild',
      'AddWife',
      'SetLoading',
      'ResetLoading',
      'RefreshPedigreeMemberList',
      'RefreshPedigreeMemberListKeepExpanded',
      'LoadChildrenData',
      'ResetQuickOperationsType'
    ]),
    // 切换展开/折叠
    async toggleExpand () {
      // 如果当前是展开状态，但没有子节点数据，需要先加载数据
      if (this.expanded && (!this.node.children || this.node.children.length === 0) && !this.isLoadingChildren) {
        await this.loadChildrenData()
        this.hasBeenExpanded = true // 记录节点曾经被展开过
        return // 保持展开状态，不切换
      }

      // 如果当前是展开状态且有子节点数据，切换为折叠
      if (this.expanded) {
        this.expanded = false
        this.$emit('expand', this.node.key, this.expanded)
        return
      }

      // 如果当前是折叠状态，要切换为展开
      // 检查是否需要加载子节点数据
      if ((!this.node.children || this.node.children.length === 0) && !this.isLoadingChildren) {
        await this.loadChildrenData()
      }

      this.expanded = true
      this.hasBeenExpanded = true // 记录节点曾经被展开过

      this.$emit('expand', this.node.key, this.expanded)
    },

    async loadChildrenData () {
      // 异步加载子节点数据
      if (this.isLoadingChildren) {
        return // 如果正在加载，直接返回
      }

      try {
        this.isLoadingChildren = true
        const { LoadChildrenData } = this

        // 以当前节点为起点，请求下3个世代的数据
        // generation参数固定为3，表示从当前节点开始向下加载3个世代
        await LoadChildrenData({
          parentNodeId: this.node.id,
          generation: 3
        })
      } catch (error) {
        console.error('加载子节点数据失败:', error)
      } finally {
        this.isLoadingChildren = false
      }
    },
    // 处理节点点击
    async handleNodeClick () {
      // 先发送点击事件
      this.$emit('click', this.node.id, this.node)

      // 检查是否有未展开的下级节点，如果有则自动展开
      await this.autoExpandIfHasChildren()
    },

    // 自动展开有子节点的节点
    async autoExpandIfHasChildren () {
      // 检查节点是否可能有子节点
      const mightHaveChildren = this.couldHaveChildren()

      if (mightHaveChildren) {
        try {
          // 检查是否需要先加载子节点数据
          const needsLoading = this.needsChildrenLoading()

          if (needsLoading) {
            // 保存当前节点信息，用于加载后重新选中
            const currentNodeId = this.node.id
            const currentNode = this.node

            // 如果需要加载数据，先加载再展开
            await this.loadChildrenData()

            // 加载完成后检查是否成功获取到子节点数据
            const hasChildrenAfterLoading = this.node.children && this.node.children.length > 0

            if (hasChildrenAfterLoading) {
              // 加载成功，确保节点处于展开状态
              if (!this.expanded) {
                this.expanded = true
                this.$emit('expand', this.node.key, this.expanded)
              }
              this.hasBeenExpanded = true

              // 重新发送点击事件，确保当前节点保持选中状态
              // 使用 nextTick 确保数据更新完成后再发送事件
              this.$nextTick(() => {
                this.$emit('click', currentNodeId, currentNode)
              })
            }
          } else {
            // 如果已经展开但没有被标记为用户展开过，直接标记
            if (this.expanded && !this.hasBeenExpanded) {
              this.hasBeenExpanded = true
            } else if (!this.expanded) {
              // 如果不需要加载数据且未展开，直接展开
              await this.toggleExpand()
            }
          }
        } catch (error) {
          console.error('自动展开节点失败:', error)
          // 加载失败时不展开节点，避免用户困惑
        }
      }
    },

    // 检查是否需要加载子节点数据
    needsChildrenLoading () {
      // 如果正在加载，不需要重复加载
      if (this.isLoadingChildren) {
        return false
      }

      // 检查是否可能有子节点
      const mightHaveChildren = this.couldHaveChildren()

      // 如果不可能有子节点，不需要加载
      if (!mightHaveChildren) {
        return false
      }

      // 更准确的判断：如果节点已经有真实的子节点数据，不需要加载
      const hasRealChildren = this.node.children && this.node.children.length > 0

      // 如果已经有真实的子节点数据，说明已经加载过了，不需要重复加载
      if (hasRealChildren) {
        return false
      }

      // 只有在没有子节点数据且可能有子节点时才需要加载
      return true
    },
    // 处理配偶点击
    handleWifeClick (wife) {
      // 为配偶对象添加必要的属性，确保能被正确识别为配偶
      const wifeNode = {
        ...wife,
        husband_id: this.node.id, // 标识这是一个配偶节点
        generation: this.node.generation, // 继承丈夫的世代
        father_id: 0 // 配偶的father_id为0，因为配偶不是主支成员
      }
      this.$emit('wifeClick', wife.id, wifeNode)
    },
    getTagName (num) {
      return TagTypeMap[num]
    },
    async handleAddBrother () {
      if (!this.fatherNode) {
        this.$message.error('始祖节点不能添加兄弟节点')
        return
      }
      const { id, generation } = this.fatherNode
      const first_name = this.fatherNode.first_name
      if (!this.fatherNode.children) {
        this.fatherNode.children = []
      }
      const { children } = this.fatherNode
      const maxRank = this.fatherNode.children.length > 0 ? Math.max(...this.fatherNode.children.map(it => it.ranking)) + 1 : 1
      // 使用更安全的临时ID，避免与真实ID冲突
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      children.push(
        {
          'clan_id': this.currentSelectedClanId,
          'pedigree_id': this.currentSelectedPedigreeId,
          'id': tempId,
          'key': tempId,
          'first_name': first_name,
          'name': '',
          'sex': '男',
          'relation': '生',
          'children': [],
          'status': 0,
          'fatherId': id,
          'motherId': -1,
          'uniqueId': tempId,
          'generation': generation + 1,
          'ranking': maxRank,
          'writeMode': 1,
          'main': true,
          'limit': 0,
          'isTemporary': true // 标记为临时节点
        })
      if (children.length && !this.expanded) {
        this.expanded = !this.expanded
      }
      this.$emit('addBrother', this.node.id, this.node)
    },
    async handleAddChild () {
      // 确定父节点信息
      const parentNode = this.node
      let motherId = -1

      // 检查是否选中了配偶
      if (this.node.wife && this.node.wife.length > 0) {
        const selectedWife = this.node.wife.find(wife => wife.id === this.selectedKey)
        if (selectedWife) {
          // 选中的是配偶，设置母亲ID
          motherId = selectedWife.id
        }
      }

      const { id, generation } = parentNode
      const firstName = parentNode.first_name
      if (!parentNode.children) {
        parentNode.children = []
      }
      const { children } = parentNode
      const maxRank = children.length > 0 ? Math.max(...children.map(it => it.ranking)) + 1 : 1
      // 使用更安全的临时ID，避免与真实ID冲突
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      children.push(
        {
          'clan_id': this.currentSelectedClanId,
          'pedigree_id': this.currentSelectedPedigreeId,
          'id': tempId,
          'key': tempId,
          'first_name': firstName,
          'name': '',
          'sex': '男',
          'relation': '生',
          'children': [],
          'status': 0,
          'fatherId': id,
          'motherId': motherId, // 如果选中配偶则设置母亲ID
          'uniqueId': tempId,
          'generation': generation + 1,
          'ranking': maxRank,
          'ranking_text': getRankText(maxRank),
          'writeMode': 1,
          'main': true,
          'limit': 0,
          'living_status': 1,
          'isTemporary': true // 标记为临时节点
        })
      if (children.length && !this.expanded) {
        this.expanded = !this.expanded
      }
      this.$emit('addChild', parentNode.id, parentNode)
    },
    async handleSaveChildren () {
      const { AddMemberChild, SetLoading, ResetLoading } = this
      const { firstName, name, fullName } = getHandleName(this.childName.trim())
      // 调用保存孩子数据方法
      this.node.writeMode = 2
      this.node.name = name
      this.node.full_name = fullName
      this.node.ranking_text = getRankText(this.node.ranking)
      const params = { ...this.node }
      params.first_name = firstName
      SetLoading(true)

      try {
        const result = await AddMemberChild(params)
        if (result?.code === 0) {
          this.$message.success('添加成功')
          // 保持展开状态的刷新，重新获取最新的树数据
          try {
            await this.RefreshPedigreeMemberListKeepExpanded()
          } catch (refreshError) {
            console.error('刷新数据失败:', refreshError)
            this.$message.warning('添加成功，但刷新数据失败，请手动刷新页面')
          }
          // 由于接口不返回新节点数据，暂时不选中特定节点
          // 可以选中父节点或保持当前选中状态
        } else {
          this.$message.error(result?.message || '添加失败')
        }
      } catch (error) {
        console.error('添加节点失败:', error)
        this.$message.error('添加失败')
      } finally {
        ResetLoading()
      }
    },
    async handleSaveWife () {
      const { AddWife, wifeName, SetLoading, ResetLoading } = this
      const { id, generation } = this.node
      const wife = this.node.wife ? this.node.wife : []
      const wifeRank = wife.length > 0 ? Math.max(...wife.map(it => it.ranking)) + 1 : 1
      const { firstName, name, fullName } = getHandleName(wifeName.trim())
      const params = {
        'clan_id': this.currentSelectedClanId,
        'pedigree_id': this.currentSelectedPedigreeId,
        'generation': generation,
        'first_name': firstName,
        'name': name,
        'full_name': fullName,
        'ranking': wifeRank,
        'ranking_text': '长女',
        'relation': '配',
        'sex': '女',
        'husband_id': id
      }
      SetLoading(true)

      try {
        const result = await AddWife(params)
        if (result?.code === 0) {
          this.$message.success('添加配偶成功')
          this.isAddWife = false
          this.wifeName = ''
          // 保持展开状态的刷新，重新获取最新的树数据
          try {
            await this.RefreshPedigreeMemberListKeepExpanded()
          } catch (refreshError) {
            console.error('刷新数据失败:', refreshError)
            this.$message.warning('添加配偶成功，但刷新数据失败，请手动刷新页面')
          }
          // 保持选中当前节点
          this.$emit('click', this.node.id, this.node)
        } else {
          this.$message.error(result?.message || '添加配偶失败')
        }
      } catch (error) {
        console.error('添加配偶失败:', error)
        this.$message.error('添加配偶失败')
      } finally {
        ResetLoading()
      }
    },
    handleDelChildren () {
      const { children } = this.fatherNode
      if (children.length === 1) {
        this.fatherNode.children = []
      } else {
        this.fatherNode.children = children.filter(item => item.id !== this.node.id)
      }
      // this.$emit('addChild', this.node.id, this.node)
    },
    getCurrentClass () {
      let result = ''
      // 默认样式
      result += 'node '
      // 展开样式
      if (this.node.children && this.expanded) {
        result += 'expand '
      }
      // 添加默认border
      result += 'border '
      // 没有子节点样式 - 修改逻辑以支持异步加载
      // 只有当节点明确没有子节点时才添加no-children类
      // 如果节点有children数组且长度为0，但可能还有未加载的子节点，则不添加no-children类
      const hasChildren = this.node.children && this.node.children.length > 0
      const mightHaveChildren = this.couldHaveChildren()

      if (!hasChildren && !mightHaveChildren) {
        result += 'no-children '
      }
      // 最后一个节点样式
      if (this.last) {
        result += 'last-item '
      }
      return result
    },
    couldHaveChildren () {
      // 判断节点是否可能有子节点
      // 优先使用has_child字段来判断，如果为null，则使用children数组的长度来判断
      if (this.node.has_child !== undefined && this.node.has_child !== null) {
        // 更健壮的判断，支持字符串、数字、布尔值
        const hasChild = this.node.has_child
        let result = false

        if (typeof hasChild === 'boolean') {
          result = hasChild
        } else if (typeof hasChild === 'string') {
          result = hasChild === '1' || hasChild.toLowerCase() === 'true'
        } else if (typeof hasChild === 'number') {
          result = hasChild === 1
        }

        return result
      }

      // 如果已经有子节点，直接返回true（不管has_child字段是否为null）
      if (this.node.children && this.node.children.length > 0) {
        return true
      }

      // 如果没有has_child字段或为null，并且没有子节点，默认返回false（不显示展开按钮）
      return false
    },
    getExpandIconClass () {
      // 如果节点曾经被用户主动展开过，根据当前展开状态显示图标
      if (this.hasBeenExpanded) {
        return this.expanded ? 'icon-edit-toolbarjian' : 'icon-edit-toolbarjia'
      }

      // 如果节点从未被用户主动展开过
      // 对于当前展开的节点（可能是默认展开的），如果有子节点则显示减号
      if (this.expanded && (this.node.children && this.node.children.length > 0)) {
        return 'icon-edit-toolbarjian'
      }

      // 对于未展开的节点，使用has_child字段判断是否显示加号
      if (this.node.has_child === 1) {
        return 'icon-edit-toolbarjia'
      }

      // 没有子节点，不显示图标
      return ''
    },
    getStyleNumClass () {
      let result = ''
      if (this.last) {
        result += `style-${this.index === 0 ? '0' : '1'} `
      }
      if (this.selectedKey === this.node.fatherId && this.expanded && this.last) {
        result += 'style-2 '
      }
      return result
    },
    getItemClass () {
      let result = 'node border'
      if (this.last) {
        result += ' last-item'
      }
      if (this.node.children?.length === 0) {
        result += ' no-children'
      }
      return result
    },
    handleDelWife () {
      this.isAddWife = false
    },
    // 根据性别获取人物图标类名
    getPersonIconClass (sex) {
      // 如果是女性，使用wife-icon
      if (sex === '女') {
        return 'icon-edit-toolbarnvce wife-icon'
      }
      // 默认使用男性图标
      return 'icon-edit-toolbarnance'
    }
  }
}
</script>
<style scoped lang='less'>
.before {
  position: absolute;
  width: 80px;
  left: -92px;
}

.before,
.padding {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.switch {
  position: absolute;
  left: -7px;
  z-index: 98;
  flex-shrink: 0;
  margin-right: 5px;
  font-size: 14px;
  background-color: #fff;
  cursor: pointer;
}

.branch {
  color: #f14d4d;
}

.more-wife {
  border: 1px dashed #f5abab;
  color: #f5abab;
  background-color: #fff !important;
  line-height: 24px !important;
  padding: 0 10px;
  box-sizing: border-box;
  cursor: pointer;
}

.wife-item {
  cursor: pointer;
  display: flex;
  align-items: center;

  .wife-icon {
    color: #f5abab;
    font-size: 14px;
    flex-shrink: 0;
  }

  .wife-content {
    background-color: #f2f2f2;
    padding: 0 15px;
    margin-right: 4px;
    line-height: 26px;
    border-radius: 2px;
    min-width: 30px;
    height: 26px;
    display: flex;
    align-items: center;
    white-space: nowrap;

    .wife-text {
      font-size: 12px;
      color: #333;
      font-weight: 400;
    }
  }
}

.padding {
  width: 90px;
  text-align: right;

  .line {
    flex-grow: 1;
    width: 0;
    height: 0;
    border-top: 1px solid #f14d4d;
    position: relative;
  }

  .line.style-0 {
    border-top: none;
  }

  span {
    flex-shrink: 0;
  }
}

.selected-node {
  >div {
    >.node.last-item {
      >.children::after {
        content: '';
        position: absolute;
        top: 44px;
        border-left: 1px dashed #a1c1e6;
        height: calc(100% - 41px);
      }

      >.item:not(.children ~ .item) {
        >.padding::after {
          content: '';
          position: absolute;
          top: 35px;
          left: 0;
          border-left: 1px dashed #a1c1e6;
          height: 9px
        }
      }
    }
  }
}

.name {
  display: flex;
  align-items: center;
  position: relative;

  .cus-edit-toolbar-icon {
    font-size: 16px;
    color: #a1c1e6;
    flex-shrink: 0;
  }

  // 主成员的wife-icon样式
  .wife-icon {
    color: #f5abab;
    font-size: 14px;
    flex-shrink: 0;
  }

  .text {
    background-color: #f2f2f2;
    padding: 0 15px;
    margin-right: 4px;
    line-height: 26px;
    cursor: pointer;
    white-space: nowrap;
    border-radius: 2px;
    min-width: 30px;
    height: 26px;

    .btn-name {
      display: inline;
      padding: 0;
      margin: 0;
      background-color: transparent;
      border: none;
      outline: none;
      color: inherit;
      cursor: pointer;
    }
  }

  .name-top {
    position: absolute;
    left: 22px;
    top: -20px;
    height: 20px;
    line-height: 1;
    display: flex;
    align-items: center;
    width: -moz-fit-content;
    width: fit-content;

    .tag {
      display: block;
      flex-shrink: 0;
      transform: translateY(4px);
      width: 20px;
      height: 20px;
      margin-right: 4px;
      text-align: center;
      line-height: 20px;
      border-radius: 50%;
      border: 1px solid #f14d4d;
      color: #f14d4d;
      background-color: rgba(251, 224, 145, .7);
      font-size: 14px;
    }

    .branch {
      flex-shrink: 0;
      margin-right: 10px;
    }
  }

  .text.mode-1 {
    padding: 0 4px;
    background-color: #fff;

    input {
      width: 70px;
      margin-right: 8px;
    }

    i {
      font-size: 14px;
    }
  }
}

.name.selected .text {
  background-color: #f86e04;
  color: #fff;
}

/* 配偶选中状态样式 */
.name.wife-selected {
  .wife-content {
    background-color: #f86e04 !important;
    color: #fff;

    .wife-text {
      color: #fff !important;
    }
  }
}

.item {
  display: flex;
  align-items: center;
  padding-top: 20px;
  position: relative;
  white-space: nowrap;

  .before {
    transform: translateX(33px);
  }
}

.node {
  position: relative;
  margin-left: 96px;
}

.node.border {
  border-left: 1px solid #f14d4d;
}

.node.no-children>.item .switch {
  display: none;
}

.node.last-item {
  border-left: none !important;
}

.node.last-item .border-left.style-1:before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  left: 0;
  top: 0;
  height: 34px;
  border-left: 1px solid #f14d4d;
}

.node .border-left.style-1:before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  left: 0;
  top: 0;
  height: 34px;
  border-left: 1px solid #f14d4d;
}

.node .border-left.style-1.style-2:before {
  height: 100%;
  border-left: 1px dashed #a1c1e6;
}

.node .border-left.style-1.style-2:after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 33px;
  border-left: 1px solid #f14d4d;
}

.root>.node>.item .switch {
  left: 25px;
}

.more-child {
  padding-left: 96px;
  display: flex;
  color: #a1c1e6;
  cursor: pointer;

  i {
    margin-top: 20px;
  }

  .text {
    margin-top: 20px;
    height: 26px;
    line-height: 24px !important;
    border: 1px dashed #a1c1e6;
    padding: 0 15px;
    box-sizing: border-box;
    background-color: #fff !important;
  }
}

.more-child:before {
  content: "";
  display: block;
  width: 30px;
  height: 30px;
  border-left: 1px dashed #a1c1e6;
  border-bottom: 1px dashed #a1c1e6;
  margin-right: 0;
}

.more-brother:before {
  width: 0;
  height: 30px;
  margin: 0;
  transform: translateY(-13px);
}

.more-brother i,
.more-brother .text {
  transform: translateX(-11px);
}
</style>
