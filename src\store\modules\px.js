import storage from 'store'
import {
  addPedigree,
  updatePedigree,
  deletedPedigree,
  getPedigreeList,
  add<PERSON><PERSON>tor,
  addMemberChild,
  deletedMember,
  addWife,
  getPedigreeMemberList,
  saveMemberInfo,
  getMemberDetail,
  getMemberList,
  moveMember,
  getTagTypeList,
  saveMemberBranch,
  deletedBranch
} from '@/api/px'
const px = {
  state: {
    list: [],
    quickOperationsType: '',
    currentSelectedPedigreeId: '',
    pedigreeMemberListLoading: false,
    currentPedigreeMemberList: null
  },
  mutations: {
    SET_List: (state, payload) => {
      state.list = payload
    },
    SET_CURRENT_SELECTED_PEDIGREE_ID: (state, payload) => {
      state.currentSelectedPedigreeId = payload
      storage.set('SET_CURRENT_SELECTED_PEDIGREE_ID', payload)
    },
    SET_PEDIGREE_MEMBER_LIST_LOADING: (state, payload) => {
      state.pedigreeMemberListLoading = payload
      storage.set('SET_PEDIGREE_MEMBER_LIST_LOADING', payload)
    },
    SET_CURRENT_PEDIGREE_MEMBER_LIST: (state, payload) => {
      state.currentPedigreeMemberList = payload
      storage.set('SET_CURRENT_PEDIGREE_MEMBER_LIST', payload)
    },
    SET_QUICK_OPERATIONS_TYPE: (state, payload) => {
      state.quickOperationsType = payload
    },
    APPEND_CHILDREN_TO_NODE: (state, { parentNodeId, newData }) => {
      // 递归查找父节点并添加子节点数据，支持多世代数据的正确层级构建
      const findAndAppendChildren = (nodes, targetId, children) => {
        if (!nodes || !Array.isArray(nodes)) return false

        for (const node of nodes) {
          if (node.id === targetId) {
            // 找到目标节点，构建多世代的层级结构
            if (!node.children) {
              node.children = []
            }

            // 将新数据按世代分组并构建层级结构
            const buildHierarchy = (dataList, parentNode) => {
              if (!dataList || !Array.isArray(dataList)) return

              // 按世代分组
              const generationGroups = {}
              dataList.forEach(item => {
                const generation = item.generation
                if (!generationGroups[generation]) {
                  generationGroups[generation] = []
                }
                generationGroups[generation].push(item)
              })

              // 按世代顺序处理
              const generations = Object.keys(generationGroups).map(Number).sort((a, b) => a - b)

              generations.forEach(generation => {
                const currentGenerationNodes = generationGroups[generation]

                currentGenerationNodes.forEach(child => {
                  // 检查是否已存在，避免重复
                  const existingChild = parentNode.children.find(existing => existing.id === child.id)
                  if (!existingChild) {
                    // 确保子节点有children数组
                    if (!child.children) {
                      child.children = []
                    }
                    parentNode.children.push(child)
                  }
                })
              })
            }

            buildHierarchy(children, node)
            return true
          }

          // 递归查找子节点
          if (node.children && findAndAppendChildren(node.children, targetId, children)) {
            return true
          }
        }
        return false
      }

      if (newData && Array.isArray(newData)) {
        findAndAppendChildren(state.pedigreeMemberList, parentNodeId, newData)
      }
    }
  },
  actions: {
    SetCurrentPedigreeId ({ commit }, payload) {
      commit('SET_CURRENT_SELECTED_PEDIGREE_ID', payload)
    },
    SetQuickOperationsType ({ commit }, payload) {
      commit('SET_QUICK_OPERATIONS_TYPE', payload)
    },
    ResetQuickOperationsType ({ commit }) {
      commit('SET_QUICK_OPERATIONS_TYPE', '')
    },
    SetLoading ({ commit }, payload) {
      commit('SET_PEDIGREE_MEMBER_LIST_LOADING', payload)
    },
    ResetLoading ({ commit }) {
      commit('SET_PEDIGREE_MEMBER_LIST_LOADING', false)
    },
    async AddPedigree ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addPedigree(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async UpdatePedigree ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        updatePedigree(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetPedigreeList ({ commit, state, rootState }) {
      return new Promise((resolve, reject) => {
        getPedigreeList({ clan_id: rootState.family.currentSelectedClanId })
          .then(json => {
            const list = json.data.list || []
            commit('SET_List', list)
            storage.set('SET_List', list)
            // 取浏览器中存储的pedigreeId
            const _existPedigreeId = localStorage.getItem('SET_CURRENT_SELECTED_PEDIGREE_ID')
            const obj = list.find(item => `${item.id}` === `${_existPedigreeId}`)
            if (_existPedigreeId && obj) {
              commit('SET_CURRENT_SELECTED_PEDIGREE_ID', Number(_existPedigreeId))
            } else {
              if (!state.currentSelectedPedigreeId && list?.length) {
                commit('SET_CURRENT_SELECTED_PEDIGREE_ID', list[0].id)
              }
              if (state.currentSelectedPedigreeId && list?.length) {
              const idList = list?.map(_it => _it.id)
              if (!idList.includes(state.currentSelectedPedigreeId)) {
                commit('SET_CURRENT_SELECTED_PEDIGREE_ID', list[0].id)
              }
            }
            }
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async DeletedPedigree ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        deletedPedigree(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async AddAncestor ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addAncestor(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
     },
    async AddMemberChild ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addMemberChild(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetNodeById ({ commit, state }, id) {
      return new Promise((resolve, reject) => {
        let result = null
        const _list = state.currentPedigreeMemberList ? [state.currentPedigreeMemberList] : []
        const getNodeById = (nodeList) => {
          nodeList.forEach(_item => {
            if (_item.id === id) {
              result = _item
              return false
            } else {
              getNodeById(_item.children || [])
            }
          })
        }
        _list.forEach(_item => {
          if (_item.id === id) {
            result = _item
          } else {
            getNodeById(_item.children || [])
          }
        })
        resolve(result)
      })
     },
    async AddWife ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addWife(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async DeletedMember ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        deletedMember(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetPedigreeMemberList ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        commit('SET_PEDIGREE_MEMBER_LIST_LOADING', true)
        getPedigreeMemberList(payload)
          .then(json => {
            commit('SET_PEDIGREE_MEMBER_LIST_LOADING', false)
            commit('SET_CURRENT_PEDIGREE_MEMBER_LIST', json.data)
            resolve(json)
          })
          .catch((e) => {
            commit('SET_PEDIGREE_MEMBER_LIST_LOADING', false)
            reject(e)
          })
      })
    },
    async RefreshPedigreeMemberList ({ commit, state, rootState, dispatch }, generation = 3) {
      try {
        const payload = {
          clan_id: rootState.family.currentSelectedClanId,
          pedigree_id: state.currentSelectedPedigreeId,
          generation: generation
        }
        const result = await dispatch('GetPedigreeMemberList', payload)
        return result
      } catch (error) {
        console.error('刷新数据失败:', error)
        throw error // 重新抛出错误，让调用方处理
      }
    },
    async RefreshPedigreeMemberListKeepExpanded ({ commit, state, rootState, dispatch }) {
      // 保持展开状态的刷新方法
      // 这个方法会触发数据刷新，但不会重置展开状态
      // 主要用于删除操作后的数据同步
      try {
        const payload = {
          clan_id: rootState.family.currentSelectedClanId,
          pedigree_id: state.currentSelectedPedigreeId,
          generation: 3,
          keepExpanded: true // 标识保持展开状态
        }
        const result = await dispatch('GetPedigreeMemberList', payload)
        return result
      } catch (error) {
        console.error('刷新数据失败:', error)
        throw error // 重新抛出错误，让调用方处理
      }
    },
    async LoadChildrenData ({ commit, state, rootState, dispatch }, { parentNodeId, startGeneration, generationsToLoad }) {
      // 异步加载子节点数据，并拼接到现有树结构中
      // 支持加载多个世代的数据

      try {
        let allNewData = []

        // 循环请求每个世代的数据
        for (let i = 0; i < generationsToLoad; i++) {
          const currentGeneration = startGeneration + i

          // 确保不超过最大世代限制
          if (currentGeneration > 5) {
            console.warn(`世代 ${currentGeneration} 超过最大限制(5)，停止加载`)
            break
          }

          const payload = {
            clan_id: rootState.family.currentSelectedClanId,
            pedigree_id: state.currentSelectedPedigreeId,
            generation: currentGeneration
          }

          // 获取当前世代的数据
          const result = await dispatch('GetPedigreeMemberList', payload)

          if (result && result.data && Array.isArray(result.data)) {
            // 将当前世代的数据添加到总数据中
            allNewData = allNewData.concat(result.data)
          }
        }

        // 将所有新数据拼接到指定父节点下
        if (allNewData.length > 0) {
          commit('APPEND_CHILDREN_TO_NODE', { parentNodeId, newData: allNewData })
        }

        return { data: allNewData }
      } catch (error) {
        console.error('加载子节点数据失败:', error)
        throw error
      }
    },
    async SaveMemberInfo ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        saveMemberInfo(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetMemberDetail ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        getMemberDetail(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetMemberList ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        getMemberList(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async MoveMember ({ commit, state, rootState }, payload) {
      return new Promise((resolve, reject) => {
        moveMember(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetTagTypeList ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        getTagTypeList(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async SaveMemberBranch ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        saveMemberBranch(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async DeletedBranch ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        deletedBranch(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    }
  }
}
export default px
