<template>
  <div class="pw-center">
    <div class="pw-main">
      <div class="page">
        <div class="page-main">
          <div class="page-head">
            <div class="title">
              <div class="clan-title txt">{{ clan?.name }} </div>
              <clan-mark-img type="down" />
              <div class="tips txt">
                <div class="txt-in" style="transform: translateX(-50%) scale(1)">{{ article?.name }}</div>
              </div>
              <div class="page-no txt">{{ getShowPageNum() }} </div>
            </div>
          </div>
          <div class="page-content">
            <div class="item-container" ref="parentDiv" id="parentContainerId">
              <div-edit-item-component
                :parentId="'parentContainerId'"
                v-for="item in (currentPageData?.items || [])"
                :key="item.id"
                :item="item"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import ClanMarkImg from '@/components/clan-mark'
import DivEditItemComponent from '@/views/pu-manage/components/pw/div-edit-item'
import { numberToChinese } from '@/utils/number2ChineseUtils'

export default {
  name: 'EditContentForPW',
  components: {
    DivEditItemComponent,
    ClanMarkImg
  },
  computed: {
    ...mapState({
      currentSelectedGroupId: state => state.family.currentSelectedGroupId,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      dataList: state => state.pw.data,
      currentPageNum: state => state.pw.currentPageNum,
      currentPageData: state => state.pw.data[state.pw.currentPageNum - 1]
    })
  },
  data () {
    return {
      parentRef: this.$refs.parentDiv,
      clan: {}
    }
  },
  props: {
    article: {
      type: Object,
      default: () => {}
    },
    pageNum: {
      type: [Number, String],
      default: 1
    }
  },
  async mounted () {
    if (!this.currentSelectedGroupId) {
      return
    }
    const result = await this.GetCurrentClan()
    this.clan = result
  },
  methods: {
    ...mapActions(['GetCurrentClan']),
    getShowPageNum () {
      return numberToChinese(this.pageNum)
    }
  }
}
</script>
<style lang='less' scoped>
.pw-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 600px;
  overflow: auto;
  justify-items: center;

  .pw-main {
    margin-top: 10px;
    background: #ebeef5;
    overflow: auto;

    .page {
      height: 780px;
      margin: 0 auto;
      padding: 60px 36px;
      box-shadow: 0 7px 21px 0 rgba(0, 0, 0, .12);
      background: #fff;
      transform-origin: 50% 0;
      box-sizing: border-box;

      .page-main {
        display: flex;
        border: 1px solid #000;
        height: 100%;
      }

      .page-head {
        border-right: 1px solid #000;
        box-sizing: border-box;
        padding: 2pt;

        .title {
          display: flex;
          flex-direction: column;
          align-items: center;
          height: 100%;
          width: 100%;
          position: relative;
          box-sizing: border-box;
          border: 1px dashed #000;

          .txt {
            font-size: 24px;
            font-family: 宋体;
            text-align: center;
            width: 32px;
            word-break: break-word;
            padding: 20px 0px;
          }

          .clan-title {
            font-weight: 700;
          }

          .tips {
            position: relative;
            flex: 1;
            display: flex;
            writing-mode: vertical-rl;
            transform-origin: 50% 0;
            word-break: keep-all;
            height: 0;
            overflow: hidden;

            .txt-in {
              position: absolute;
              left: 50%;
              top: 20px;
              transform-origin: center top;
              white-space: nowrap;
            }
          }

          .page-no {
            font-size: 20px;
          }
        }
      }

      .page-content {
        box-sizing: border-box;
        position: relative;
        flex: 1;
        .item-container {
          position: relative;
          height: 100%;
          width: 100%;
        }
      }
    }
  }
}
</style>
