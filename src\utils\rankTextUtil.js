import { isNumber } from 'lodash'
import { numberToChinese } from '@/utils/number2ChineseUtils'

export const getRankText = function (rank, sex) {
  const sexName = sex === '女' ? '女' : '子'
  if (rank === 1) {
    return `长${sexName || '子'}`
  }
  if (rank === 2) {
    return `次${sexName || '子'}`
  }
  if (isNumber(rank)) {
    const name = numberToChinese(rank)
    return `${name}${sexName || '子'}`
  }
  return `${rank}${sexName || '子'}`
}
