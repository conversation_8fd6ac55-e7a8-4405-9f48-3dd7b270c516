<template>
  <div class="px-header">
    <div class="inner">
      <div class="demo" @click="()=>showFamilyBaseInfo=true">
        <span class="tag tag-fanli">管理</span>
        <span class="name line-word">{{ clan?.name }}</span>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarchaxun"></i>
      </div>
      <a-divider type="vertical" style="height: 26px; margin: 0 32px; color: #f86e04;"/>
      <!--      <a-input-search placeholder="输入要查找的族员名字" size="large" style="width:300px;">-->
      <!--        <a-button slot="enterButton">-->
      <!--          <div class=" flex align-center" @click="handleSearch">-->
      <!--            <i class="cus-edit-toolbar-icon icon-edit-toolbarfangdajing"></i>-->
      <!--            搜索-->
      <!--          </div>-->
      <!--        </a-button>-->
      <!--      </a-input-search>-->
      <!--      <span-->
      <!--        class="ml16 flex align-center c-pointer-hover"-->
      <!--        @click="handleToggle('close')"-->
      <!--      >-->
      <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbarCDNjiedian"></i>折叠-->
      <!--      </span>-->
      <!--      <span-->
      <!--        class="ml16 flex align-center c-pointer-hover"-->
      <!--        @click="handleToggle('open')"-->
      <!--      >-->
      <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbarcaidanzhedie"></i>展开-->
      <!--      </span>-->
      <!-- <span
        class="ml16 flex align-center c-pointer-hover"
        @click="handleAdjustShiXi"
      >
        <i class="cus-edit-toolbar-icon icon-edit-toolbarjiacu"></i>调整世系
      </span> -->
      <!--      <span-->
      <!--        class="ml16 flex align-center c-pointer-hover"-->
      <!--        @click="handleMoreFunc"-->
      <!--      >-->
      <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbarshezhi"></i>更多功能-->
      <!--      </span>-->
      <!--      <span-->
      <!--        class="ml16 flex align-center c-pointer-hover"-->
      <!--        @click="handleViewSetting"-->
      <!--      >-->
      <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbaryanjing"></i>视图设置-->
      <!--      </span>-->
      <div class="flex-grow-1  flex flex-direction-row justify-content-end">
        <!--        <span class="ml16 flex align-center c-pointer-hover" @click="handleSave">-->
        <!--          <i class="cus-edit-toolbar-icon icon-edit-toolbarbaocun"></i>保存-->
        <!--        </span>-->
        <!--        <span class="ml16 flex align-center c-pointer-hover" @click="handleMobilePreview">-->
        <!--          <i class="cus-edit-toolbar-icon icon-edit-toolbarshoujiyulan-normal"></i>手机预览-->
        <!--        </span>-->
        <span class="ml16 flex align-center mr16 c-pointer c-pointer-hover" @click="handlePreview">
          <i class="cus-edit-toolbar-icon icon-edit-toolbara-tubiao_paibanyulan"></i>排版预览
        </span>
      </div>
    </div>
    <a-drawer
      title=""
      width="100vw"
      placement="right"
      wrapClassName="clan-preview-drawer"
      :closable="false"
      :visible="showPreview"
      @close="handlePreview"
    >
      <clan-preview :close="handlePreview"/>
    </a-drawer>
    <family-info-component v-if="showFamilyBaseInfo" @close="showFamilyBaseInfo=false"/>
    <tiao-zheng-shi-xi-modal v-if="showAdjustShiXiModal" :selectedNode="selectedNode" @close="showAdjustShiXiModal=false" />
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import ClanPreview from '@/views/pu-manage/components/px/clan-preview'
import FamilyInfoComponent from '@/views/components/family-info'
import TiaoZhengShiXiModal from '@/views/pu-manage/components/px/components/tiaozhengshixi-modal'

export default {
  name: 'PxHeader',
  components: {
    TiaoZhengShiXiModal,
    FamilyInfoComponent,
    ClanPreview
  },
  data () {
    return {
      showPreview: false,
      showFamilyBaseInfo: false,
      showAdjustShiXiModal: false,
      clan: {}
    }
  },
  props: {
    selectedNode: {
      type: Object,
      default: () => {}
    }
  },
  created () {
    // 监听自定义事件
    this.$on('custom-event', (data) => {
      console.log('事件被触发了，传递的数据是:', data)
    })
  },
  computed: {
    ...mapState({
      currentSelectedGroupId: state => state.family.currentSelectedGroupId
    })
  },
  async mounted () {
    if (!this.currentSelectedGroupId) {
      return
    }
    const result = await this.GetCurrentClan()
    this.clan = result
  },
  methods: {
    ...mapActions(['GetCurrentClan']),
    handlePreview () {
      this.showPreview = !this.showPreview
    },
    handleAdjustShiXi () {
      this.showAdjustShiXiModal = true
    },
    handleToggle (type) {
      this.$message.info('该功能暂未实现')
      if (type === 'open') {
        this.$emit('open')
      } else {
        this.$emit('close')
      }
    },
    handleSearch () {
      this.$message.info('该功能暂未实现')
    },
    handleMoreFunc () {
      this.$message.info('该功能暂未实现')
    },
    handleViewSetting () {
      this.$message.info('该功能暂未实现')
    },
    handleSave () {
      this.$message.info('该功能暂未实现')
    },
    handleMobilePreview () {
      this.$message.info('该功能暂未实现')
    }
  }
}
</script>
<style scoped lang='less'>
.px-header{
  position:relative;
  height: 60px;
  z-index:1;

  .inner{
    height: 60px;
    left: 80px;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    background-color: #fff;

    .demo {
      width: 195px;
      height: 52px;
      background:  #f0f2f5 url(~@/assets/nav_level_3.png) no-repeat 50%;
      background-size: cover;
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 15px;
      padding-right: 10px;
      box-sizing: border-box;
      margin-left: 5px;
      border-radius: 2px;
      overflow: hidden;
      cursor: pointer;
      z-index: 1;
      .tag{
        position: absolute;
        left: 0;
        top: 0;
        width: 80px;
        height: 16px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        z-index: 10;
        transform: rotate(-45deg);
        transform-origin: 42% 267%;
        font-size: 12px
      }
      .tag-fanli {
        background: #b91212;
      }
      .line-word {
        font-size:24px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .left {
    flex-shrink: 0;

    .line{
      display: inline-block;
      height: 22px;
      border-left: 1px solid #ccc;
      margin-left: 30px
    }
    .search {
      position: relative;
      z-index: 0;
      width: 280px;
      height: 36px
    }
  }
}
.clan-preview-drawer {
  /deep/ .ant-drawer-wrapper-body {
    overflow:hidden;
    .ant-drawer-body {
      padding: 0;
    }
  }
}
</style>
