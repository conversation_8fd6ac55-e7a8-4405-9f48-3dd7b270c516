<template>
  <div class="container">
    <div class="preview-left">
      <div v-for="(page, idx) in pageList" :key="idx">
        <page-type-one v-if="page.pageType === 1" :page="page" />
        <page-type-two v-else-if="page.pageType === 2" :page="page" />
        <page-type-three v-else-if="page.pageType === 3" :page="page" />
        <br />
      </div>
    </div>
    <div class="preview-right">
      <right :close="close"/>
    </div>
  </div>
</template>
<script>
import PageTypeOne from './components/page-type-1'
import PageTypeTwo from './components/page-type-2'
import PageTypeThree from './components/page-type-3'
import mockPreviewData from './mock-preview-data.json'
import Right from '@/components/clan-preview/components/common/right'
export default {
  name: 'ClanPreviewDetailPage',
  components: {
    Right,
    PageTypeOne,
    PageTypeTwo,
    PageTypeThree
  },
  data () {
    const initData = mockPreviewData.data
    const pagesList = initData.pages
    return {
      pageList: pagesList
    }
  },
  props: {
    close: {
      type: Function,
      default: () => {}
    }
  }
}
</script>
<style lang="less" scoped>
.container {
  display: flex;
  background-color: #fff6ec;
  .preview-left {
    padding: 0 30px;
    position: relative;
    flex: 1;
    padding: 0 0 0 20px;
    height: 100vh;
    overflow: auto;
  }
  .preview-right {
    flex-shrink: 0;
    width: 290px;
    min-height: 100%;
    margin-right: 5px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
  }
}
</style>
