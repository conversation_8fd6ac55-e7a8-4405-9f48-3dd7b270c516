<template>
  <div :class="getCurrentClass()">
    <div class="border-left " :class="getStyleNumClass()" />
    <div class="item" style="margin-left: 0px">
      <div class="before">
        <i class="icon" title="无编辑权限" />
      </div>
      <div class="switch cus-edit-toolbar-icon" :class="expanded ? 'icon-edit-toolbarjian':'icon-edit-toolbarjia'" @click.stop="toggleExpand"></div>
      <div class="padding" style="width: 89px;">
        <div class="line" :class="getStyleNumClass()"></div>
        <!--        选择图标  -->
        <i v-if="fatherNode" class="cus-edit-toolbar-icon icon-edit-toolbarxuanze c-pointer-hover ml4" style="font-size: 20px;" />
      </div>
      <div class="name" :class="selectedKey === node.id ?'selected':''">
        <!--        姓氏 + 名称  -->
        <div v-show="node.writeMode === 2" class="text" @click="handleNodeClick">
          <button class="btn-name" v-if="fatherNode">
            <span>{{ node.first_name }}</span>
            <span>{{ node.name }}</span>
          </button>
        </div>
        <!--        世系  -->
        <span>[{{ node.generation }}世]</span>
        <div v-if="!noAdd && node.writeMode === 1" class="text mode-1">
          <a-input size="small" v-model="childName" @pressEnter="handleSaveChildren"/>
          <i class="cus-edit-toolbar-icon cus-edit-toolbar-icon icon-edit-toolbarbaocun c-pointer-hover mr6" title="保存该项" @click="handleSaveChildren" />
          <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover" @click="handleDelChildren" title="删除该项"/>
        </div>
        <!--        排行    -->
        <span v-if="!noShowRank">[{{ node.ranking_text }}]</span>
        <!--        妻子    -->
        <div v-if="!noWife" v-for="wife in (node?.wife || [])" class="name" :key="wife.id">
          <i class="cus-edit-toolbar-icon icon-edit-toolbarnvce" style="color: #f5abab"/>
          {{ wife.full_name || '' }}
        </div>
        <div v-if="!noAdd && selectedKey === node.id" class="name">
          <a-input size="small" v-show="isAddWife" v-model.trim="wifeName" style="width: 80px" @pressEnter="handleSaveWife"/>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarnvce" style="color: #f5abab"/>
          <div class="more-wife" @click="isAddWife=true">添加配偶</div>
        </div>
        <div class="name-top" v-if="node.branch_tag">
          <span class="tag">{{ getTagName(node.branch_tag) }}</span>
          <span class="branch">[{{ node.branch_name }}]</span>
        </div>
      </div>
    </div>
    <div class="children" v-if="expanded && node.children && node.children.length">
      <div style="margin-left: 0px;">
        <tree-node
          v-for="(nd, idx) in node.children"
          :key="nd.id"
          :node="nd"
          :checked-keys="checkedKeys"
          :expanded-keys="expandedKeys"
          :selected-key="selectedKey"
          :index="index + 1"
          :last="idx === node.children.length - 1"
          :fatherNode="node"
          :no-add="noAdd"
          :no-wife="noWife"
          :no-show-rank="noShowRank"
          :no-show-person-icon="noShowPersonIcon"
          @check="$listeners.check"
          @expand="$listeners.expand"
          @click="$listeners.click"
          @addBrother="$listeners.addBrother"
          @addChild="$listeners.addChild"
        />
      </div>
    </div>
    <div class="more-child" v-if="!noAdd && selectedKey === node.id" style="margin-left: 0px;">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarnance" />
      <div class="text" @click="handleAddChild">添加孩子</div>
    </div>
    <div class="more-child more-brother" v-if="!noAdd && selectedKey === node.id" style="margin-left: 0px;">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarnance" />
      <div class="text" @click="handleAddBrother">添加兄妹</div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import { getHandleName } from '@/utils/nameUtil'
import { getRankText } from '@/utils/rankTextUtil'
import * as actionTypes from '@/store/modules/px-action-type'
import { TagTypeMap } from '@/constant'

export default {
  name: 'TreeNode',
  inheritAttrs: false,
  props: {
    node: {
      type: Object,
      required: true
    },
    fatherNode: {
      type: Object,
      default: () => {}
    },
    expandedKeys: {
      type: Array,
      default: () => []
    },
    checkedKeys: {
      type: Array,
      default: () => []
    },
    index: {
      type: Number,
      default: 0
    },
    last: {
      type: Boolean,
      default: false
    },
    selectedKey: {
      type: [Number, String],
      default: -9999
    },
    showSelectedICon: {
      type: Boolean,
      default: false
    },
    noAdd: {
      type: Boolean,
      default: false
    },
    noWife: {
       type: Boolean,
      default: false
    },
    noShowRank: {
       type: Boolean,
      default: false
    },
    noShowPersonIcon: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      wifeName: '',
      isAddWife: false,
      childName: this.node.first_name,
      expanded: this.expandedKeys.includes(this.node.key),
      checked: false
    }
  },
  computed: {
    ...mapState({
      quickOperationsType: state => state.px.quickOperationsType,
      currentPedigreeMemberList: state => state.px.currentPedigreeMemberList,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  watch: {
    expandedKeys: {
      handler (newVal) {
        this.expanded = newVal.includes(this.node.key)
      },
      immediate: true
    },
    // 监听 checkedKeys 的变化，更新当前节点的选中状态
    checkedKeys: {
      handler (newVal) {
        this.checked = newVal.includes(this.node.key)
      },
      immediate: true
    },
    quickOperationsType: {
      handler (newVal, oldVal) {
        if (newVal && this.node.id === this.selectedKey) {
          this.ResetQuickOperationsType()
          switch (newVal) {
            case actionTypes.PX_RIGHT_OPTION_ADD_BROTHER:
              this.handleAddBrother()
              break
            case actionTypes.PX_RIGHT_OPTION_ADD_CHILD:
              this.handleAddChild()
              break
            case actionTypes.PX_RIGHT_OPTION_ADD_WIFE:
              this.isAddWife = true
              break
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions([
      'AddMemberChild',
      'AddWife',
      'SetLoading',
      'ResetLoading',
      'RefreshPedigreeMemberList',
      'ResetQuickOperationsType'
    ]),
    // 切换展开/折叠
    toggleExpand () {
      this.expanded = !this.expanded
      this.$emit('expand', this.node.key, this.expanded)
    },
    // 处理节点点击
    handleNodeClick () {
      this.$emit('click', this.node.id, this.node)
    },
    getTagName (num) {
      return TagTypeMap[num]
    },
    async handleAddBrother () {
      if (!this.fatherNode) {
        this.$message.error('始祖节点不能添加兄弟节点')
        return
      }
      const { id, generation } = this.fatherNode
      const first_name = this.fatherNode.first_name
      if (!this.fatherNode.children) {
        this.fatherNode.children = []
      }
      const { children } = this.fatherNode
      const cDate = Date.now()
      const maxRank = this.fatherNode.children.length > 0 ? Math.max(...this.fatherNode.children.map(it => it.ranking)) + 1 : 1
      children.push(
        {
          'clan_id': this.currentSelectedClanId,
          'pedigree_id': this.currentSelectedPedigreeId,
          'id': cDate,
          'key': cDate,
          'first_name': first_name,
          'name': '',
          'sex': '男',
          'relation': '生',
          'children': [],
          'status': 0,
          'fatherId': id,
          'motherId': -1,
          'uniqueId': -cDate,
          'generation': generation + 1,
          'ranking': maxRank,
          'writeMode': 1,
          'main': true,
          'limit': 0
        })
      if (children.length && !this.expanded) {
        this.expanded = !this.expanded
      }
      this.$emit('addBrother', this.node.id, this.node)
    },
    async handleAddChild () {
      const { id, generation } = this.node
      const firstName = this.node.first_name
      if (!this.node.children) {
        this.node.children = []
      }
      const { children } = this.node
      const maxRank = children.length > 0 ? Math.max(...children.map(it => it.ranking)) + 1 : 1
      const cDate = Date.now()
      children.push(
        {
          'clan_id': this.currentSelectedClanId,
          'pedigree_id': this.currentSelectedPedigreeId,
          'id': cDate,
          'key': cDate,
          'first_name': firstName,
          'name': '',
          'sex': '男',
          'relation': '生',
          'children': [],
          'status': 0,
          'fatherId': id,
          'motherId': -1,
          'uniqueId': -cDate,
          'generation': generation + 1,
          'ranking': maxRank,
          'ranking_text': getRankText(maxRank),
          'writeMode': 1,
          'main': true,
          'limit': 0
      })
      if (children.length && !this.expanded) {
        this.expanded = !this.expanded
      }
      this.$emit('addChild', this.node.id, this.node)
    },
    async handleSaveChildren (e) {
      const { AddMemberChild, RefreshPedigreeMemberList, SetLoading, ResetLoading } = this
      const { firstName, name, fullName } = getHandleName(this.childName.trim())
      // 调用保存孩子数据方法
      this.node.writeMode = 2
      this.node.name = name
      this.node.full_name = fullName
      this.node.ranking_text = getRankText(this.node.ranking)
      const params = { ...this.node }
      params.first_name = firstName
      SetLoading(true)
      const result = await AddMemberChild(params)
      const info = result.data?.info
      ResetLoading()
      // 选中该节点
      info && this.$emit('click', info.id, info)
      await RefreshPedigreeMemberList()
    },
    async handleSaveWife (e) {
      const { AddWife, wifeName, RefreshPedigreeMemberList, SetLoading, ResetLoading } = this
      const { id, generation } = this.node
      const wife = this.node.wife ? this.node.wife : []
      const wifeRank = wife.length > 0 ? Math.max(...wife.map(it => it.ranking)) + 1 : 1
      const params = {
         'clan_id': this.currentSelectedClanId,
         'pedigree_id': this.currentSelectedPedigreeId,
         'generation': generation,
         'first_name': wifeName,
         'name': wifeName,
         'full_name': wifeName,
         'ranking': wifeRank,
         'ranking_text': wifeRank,
         'relation': '生',
         'sex': '女',
         'husband_id': id
       }
      SetLoading(true)
      await AddWife(params)
      ResetLoading()
      this.isAddWife = false
      this.$emit('click', this.node.id, this.node)
      await RefreshPedigreeMemberList()
    },
    handleDelChildren () {
      const { children } = this.fatherNode
      if (children.length === 1) {
        this.fatherNode.children = []
      } else {
        this.fatherNode.children = children.filter(item => item.id !== this.node.id)
      }
      // this.$emit('addChild', this.node.id, this.node)
    },
    getCurrentClass () {
      let result = ''
      // 默认样式
      result += 'node '
      // 展开样式
      if (this.node.children && this.expanded) {
        result += 'expand '
      }
      // 添加默认border
      result += 'border '
      // 没有子节点样式
      if (!this.node.children || (this.node.children && this.node.children.length === 0)) {
        result += 'no-children '
      }
      // 最后一个节点样式
      if (this.last) {
        result += 'last-item '
      }
      return result
    },
    getStyleNumClass () {
      let result = ''
      if (this.last) {
        result += `style-${this.index === 0 ? '0' : '1'} `
      }
      if (this.selectedKey === this.node.fatherId && this.expanded && this.last) {
        result += 'style-2 '
      }
      return result
    },
    getItemClass () {
      let result = 'node border'
      if (this.last) {
        result += ' last-item'
      }
      if (this.node.children?.length === 0) {
        result += ' no-children'
      }
      return result
    }
  }
}
</script>
<style scoped lang='less'>
.before{
  position: absolute;
  width: 80px;
  left: -92px;
}
.before, .padding {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.switch {
  position: absolute;
  left: -10px;
  z-index: 98;
  flex-shrink: 0;
  margin-right: 5px;
  font-size: 16px;
  background-color: #fff;
  cursor: pointer;
}
.branch{
  color: #f14d4d;
}
.more-wife {
  border: 1px dashed #f5abab;
  color: #f5abab;
  background-color: #fff !important;
  line-height: 24px !important;
  padding: 0 10px;
  box-sizing: border-box;
  cursor:pointer;
}
.padding {
  width: 90px;
  text-align: right;

  .line {
    flex-grow: 1;
    width: 0;
    height: 0;
    border-top: 1px solid #f14d4d;
    position: relative;
  }
  .line.style-0 {
    border-top: none;
  }
  span {
    flex-shrink: 0;
  }
}
.name {
  display: flex;
  align-items: center;
  position: relative;
  font-size: 12px;
  .text {
    background-color: #f2f2f2;
    padding: 0 15px;
    margin-right: 4px;
    line-height: 26px;
    cursor: pointer;
    white-space: nowrap;
    border-radius: 2px;
    min-width: 30px;
    height: 26px;
    .btn-name {
      display: inline;
      padding: 0;
      margin: 0;
      background-color: transparent;
      border: none;
      outline: none;
      color: inherit;
      cursor: pointer;
    }
  }
  .name-top {
    position: absolute;
    left: 22px;
    top: -20px;
    height: 20px;
    line-height: 1;
    display: flex;
    align-items: center;
    width: -moz-fit-content;
    width: fit-content;
    .tag {
      display: block;
      flex-shrink: 0;
      transform: translateY(4px);
      width: 20px;
      height: 20px;
      margin-right: 4px;
      text-align: center;
      line-height: 20px;
      border-radius: 50%;
      border: 1px solid #f14d4d;
      color: #f14d4d;
      background-color: rgba(251, 224, 145, .7);
      font-size: 16px;
    }
    .branch {
      flex-shrink: 0;
      margin-right: 10px;
    }
  }
  .text.mode-1 {
    padding: 0 4px;
    background-color:#fff;
    input { width: 70px; margin-right: 8px;}
    i { font-size: 14px;}
  }
}
.name.selected .text {
  background-color: #f86e04;
  color: #fff;
}
.item{
  display: flex;
  align-items: center;
  padding-top: 20px;
  position: relative;
  .before {
    transform: translateX(33px);
  }
}
.node{
  position: relative;
  margin-left: 100px;
}
.node.border{
  border-left: 1px solid #f14d4d;
}
.node.no-children>.item .switch {
  display: none;
}
.node.last-item{
  border-left: none !important;
}
.node.last-item .border-left.style-1:before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  left: 0;
  top: 0;
  height: 34px;
  border-left: 1px solid #f14d4d;
}
.node .border-left.style-1:before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  left: 0;
  top: 0;
  height: 34px;
  border-left: 1px solid #f14d4d;
}
.node .border-left.style-1.style-2:before {
  height: 100%;
  border-left: 1px dashed #a1c1e6;
}
.node .border-left.style-1.style-2:after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 33px;
  border-left: 1px solid #f14d4d;
}

.root>.node>.item .switch{
  left: 25px;
}
.more-child {
  padding-left: 100px;
  display: flex;
  color: #a1c1e6;
  cursor: pointer;
  i{
    margin-top: 20px;
  }
  .text {
    margin-top: 20px;
    height: 26px;
    line-height: 24px !important;
    border: 1px dashed #a1c1e6;
    padding: 0 15px;
    box-sizing: border-box;
    background-color: #fff !important;
  }
}
.more-child:before {
  content: "";
  display: block;
  width: 30px;
  height: 30px;
  border-left: 1px dashed #a1c1e6;
  border-bottom: 1px dashed #a1c1e6;
  margin-right: 0;
}
.more-brother:before {
  width: 0;
  height: 30px;
  margin: 0;
  transform: translateY(-13px);
}
.more-brother i, .more-brother .text {
  transform: translateX(-11px);
}
</style>
