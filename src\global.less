@import '~ant-design-vue/es/style/themes/default.less';
@import './assets/theme.less';

html,
body,
#app,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout.layout-basic {
  height: 100vh;
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
}

ul,
ol {
  list-style: none;
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}
// 数据列表 操作
.table-operator {
  margin-bottom: 18px;

  button {
    margin-right: 8px;
  }
}
// 数据列表 搜索条件
.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-right: 0;
      margin-bottom: 24px;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        width: auto;
        padding-right: 8px;
        line-height: 32px;
      }

      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-y {
  overflow-y: auto;
}

.overflow-x {
  overflow-x: auto;
}

.line-word {
  white-space: nowrap;
}

.line-word,
.line-word-2 {
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-word-2 {
  display: box;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.f12 { font-size: 12px; }
.f14 { font-size: 14px; }
.f16 { font-size: 16px; }
.f18 { font-size: 18px; }
.f20 { font-size: 20px; }
.f22 { font-size: 22px; }
.f24 { font-size: 24px; }
.f26 { font-size: 26px; }
.f28 { font-size: 28px; }
.f30 { font-size: 30px; }

.c-999 { color: #999; }
.c-not-allowed { cursor: not-allowed; }
.c-pointer { cursor: pointer; }

.c-pointer-hover:hover {
  color: @primary-color;
  cursor: pointer;
}

.primary {
  color: @primary-color;
}
.bd-primary { border-color: @primary-color; }

.bg-primary {
  background-color: @bg-background-color;
}

.hidden {
  display: none !important;
}
.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-direction-row {
  flex-direction: row;
}

.flex-direction-column {
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.align-center {
  align-items: center;
}

.justify-items-center {
  justify-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-content-end {
  justify-content: end;
}

.justify-content-space-between {
  justify-content: space-between;
}

.flex-grow-1 {
  flex-grow: 1;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}
.mt4 { margin-top: 4px; }
.mt6 { margin-top: 6px; }
.mt8 { margin-top: 8px; }
.mt10 { margin-top: 10px; }
.mt12 { margin-top: 12px; }
.mt14 { margin-top: 14px; }
.mt16 { margin-top: 16px; }
.ml2 { margin-left: 2px; }
.ml4 { margin-left: 4px; }
.ml6 { margin-left: 6px; }
.ml8 { margin-left: 8px; }
.ml10 { margin-left: 10px; }
.ml12 { margin-left: 12px; }
.ml14 { margin-left: 14px; }
.ml16 { margin-left: 16px; }
.ml18 { margin-left: 18px; }
.ml20 { margin-left: 20px; }
.ml22 { margin-left: 22px; }
.ml24 { margin-left: 24px; }
.mr4 { margin-right: 4px; }
.mr6 { margin-right: 6px; }
.mr8 { margin-right: 8px; }
.mr10 { margin-right: 10px; }
.mr12 { margin-right: 12px; }
.mr14 { margin-right: 14px; }
.mr16 { margin-right: 16px; }
.mb2 { margin-bottom: 2px; }
.mb4 { margin-bottom: 4px; }
.mb6 { margin-bottom: 6px; }
.mb8 { margin-bottom: 8px; }
.mb10 { margin-bottom: 10px; }
.mb12 { margin-bottom: 12px; }
.mb14 { margin-bottom: 14px; }
.mb16 { margin-bottom: 16px; }
.mb18 { margin-bottom: 18px; }
.mb20 { margin-bottom: 20px; }
.w100p { width: 100%; }
.w100 { width: 100px; }

img {
  max-width: 100%;
  border: none;
}

.ant-layout-sider {
  position: fixed;
}

.ant-layout.sidemenu {
  margin-left: 80px;
}

.ant-layout-header {
  display: none;
}

.ant-layout-footer {
  display: none;
}

.ant-pro-basicLayout-content {
  margin: 0 0 2px;
}
@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}
