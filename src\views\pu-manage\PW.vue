<template>
  <div class="flex pu-wen-container">
    <left-content @update="handleUpdate"/>
    <div class="flex-1">
      <tool-bar v-if="currentSelectedClanId"/>
      <div class="flex flex-direction-row" v-if="currentSelectedClanId">
        <div class="flex-1 justify-items-center">
          <edit-content :key="currentSelectedArticleId + currentPageNum" :page-num="currentPageNum" :article="currentSelectedArticle"/>
        </div>
        <div style="width:200px; background-color:#e5e5e5">
          <pw-right-content :key="currentSelectedArticleId" />
        </div>
      </div>
      <a-empty v-show="!currentSelectedClanId" />
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import cloneDeep from 'lodash.clonedeep'
import { v4 as uuidv4 } from 'uuid'
import LeftContent from '@/views/pu-manage/components/pw/left-content'
import ToolBar from '@/components/Editor/components/toolbar'
import EditContent from '@/views/pu-manage/components/pw/edit-content'
import PwRightContent from '@/views/pu-manage/components/pw/right-content'
import * as initDefaultData from '@/components/Editor/components/initDefaultData'
import { EventBus } from '@/utils/eventBus'
import { ToolbarEvents } from '@/constant/event-types'
export default {
  name: 'PWIndexPage',
  components: {
    PwRightContent,
    EditContent,
    LeftContent,
    ToolBar
  },
  data () {
    return {
      currentSelectedCategoryId: '',
      currentSelectedCategory: {},
      currentSelectedArticleId: '',
      currentSelectedArticle: {}
    }
  },
  computed: {
    ...mapState({
      dataList: state => state.pw.data,
      currentPageNum: state => state.pw.currentPageNum,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  mounted () {
    EventBus.$on(ToolbarEvents.SAVE, this.handleSave)
  },
  unmounted () {
    EventBus.$off(ToolbarEvents.SAVE)
  },
  methods: {
    ...mapActions(['UpdateCurrentPageNum', 'UpdateCurrentPageData', 'UpdateClanArticle']),
    toHome () {
      this.$router.push({ path: '/' })
    },
    handleUpdate (item) {
      const { UpdateCurrentPageNum, UpdateCurrentPageData } = this
      const { article, category } = item
      if (article) {
        this.currentSelectedArticleId = article.id
        this.currentSelectedArticle = article
        UpdateCurrentPageNum(1)
        // 更新data
        const _data = cloneDeep(initDefaultData.TextBoxData)
        _data.articleId = item.id
        _data.items[0].id = `${uuidv4()}`
        UpdateCurrentPageData([_data])
      }
      if (category) {
        this.currentSelectedCategoryId = category.id
        this.currentSelectedCategory = category
      }
    },
    async handleSave () {
      const { UpdateClanArticle, currentSelectedArticle } = this
      if (!currentSelectedArticle?.id) {
        this.$message.error('请先选择谱文')
        return
      }
      if (!this.currentSelectedClanId) {
        this.$message.error('请先新建家族')
        return
      }
      const saveData = cloneDeep(this.dataList)
      saveData.forEach(page => {
        page.items.forEach(item => {
          const { id } = item
          let result = sessionStorage.getItem(id)
          if (result) {
            result = JSON.parse(result)
            const { boxStyle: { left, top, width, height }, text } = result
            if (left) {
              item.left = left
            }
            if (top) {
              item.top = top
            }
            if (width) {
              item.width = width
            }
            if (height) {
              item.height = height
            }
            if (text) {
              item.content = text
            }
          }
        })
      })
      this.$loading.show('保存中')
      const res = await UpdateClanArticle({
        id: currentSelectedArticle.id,
        pages: JSON.stringify(saveData),
        clan_id: this.currentSelectedClanId
      })
      this.$loading.hide()
      if (res && res.code === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.message || '保存失败')
      }
    }
  }
}
</script>
<style lang='less' scoped>
.pu-wen-container{
  background-color:#ebeef5;
  overflow-y: hidden;
}
</style>
