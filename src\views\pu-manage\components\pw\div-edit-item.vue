<template>
  <div
    v-drag
    ref="box"
    :id="item.id"
    :style="boxStyle"
    class="draggable-editable-box"
    :class="getClass()"
    @mousedown="startDrag"
    @mousemove="enterEditMode"
  >
    <div class="content">
      <div class="resize-handle tl" v-show="item.id === activeEditItemId" @mousedown="startResize"/>
      <div class="resize-handle tc" v-show="item.id === activeEditItemId" @mousedown="startResize"/>
      <div class="resize-handle tr" v-show="item.id === activeEditItemId" @mousedown="startResize"/>
      <div class="resize-handle ml" v-show="item.id === activeEditItemId" @mousedown="startResize"/>
      <div class="resize-handle mr" v-show="item.id === activeEditItemId" @mousedown.stop="startResize" />
      <div class="resize-handle bl" v-show="item.id === activeEditItemId" @mousedown="startResize"/>
      <div class="resize-handle bc" v-show="item.id === activeEditItemId" @mousedown="startResize"/>
      <div class="resize-handle br" v-show="item.id === activeEditItemId" @mousedown="startResize" />
      <div contenteditable="true" class="edit-text" :class="isVertical() ? 'vertical-mode':''" ref="editDiv">请输入</div>
    </div>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import cloneDeep from 'lodash.clonedeep'
import * as types from '@/components/Editor/components/types'
import * as initData from './initDefaultData'

/**
 * 保存到本地
 * @param id 必填
 */
const saveLocalStorage = (id, data) => {
  if (!id) {
    return
  }
  const { position, type, info } = data || {}
  let saveInfo = null
  const { left, top, width, height } = position || {}
  const getInfo = sessionStorage.getItem(id)
  if (getInfo) {
    saveInfo = JSON.parse(getInfo)
  } else {
    saveInfo = {
      boxStyle: {},
      type: '', // 默认为空 表示水平模式
      text: ''
    }
  }
  if (top) {
    saveInfo.boxStyle.top = parseInt(top)
  }
  if (left) {
    saveInfo.boxStyle.left = parseInt(left)
  }
  if (width) {
    saveInfo.boxStyle.width = parseInt(width)
  }
  if (height) {
    saveInfo.boxStyle.height = parseInt(height)
  }
  if (info) {
    saveInfo.text = info
  }
  if (type) {
    saveInfo.type = type
  }
  sessionStorage.setItem(id, JSON.stringify(saveInfo))
}

export default {
  name: 'DivEditItemComponent',
  props: {
    parentId: {
      type: String,
      default () {
        return undefined
      }
    },
    item: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  directives: {
    'drag': {
      inserted (el, binding, node) {
        requestAnimationFrame(() => {
          const oDiv = el // 当前元素
          const pDDiv = document.getElementById(node.context.parentId)
          const rangWidth = pDDiv?.clientWidth // 父元素宽度，即 移动范围
          const rangHeight = pDDiv?.clientHeight // 父元素高度
          // 禁止选择网页上的文字
          document.onselectstart = () => { return false }
          oDiv.onmousedown = (e) => {
            // 鼠标按下，计算当前元素距离可视区的距离
            const disX = e.clientX - oDiv.offsetLeft
            const disY = e.clientY - oDiv.offsetTop
            const dWdith = oDiv.clientWidth
            const dHeight = oDiv.clientHeight
            const dMoveLeft = rangWidth - dWdith
            const dMoveTop = rangHeight - dHeight

            document.onmousemove = (e) => {
              // 通过事件委托，计算移动的距离
              const left = e.clientX - disX
              const top = e.clientY - disY
              // 如果元素的移动位置大于窗口位置，则不再移动
              if (left > dMoveLeft) {
                oDiv.style.left = `${dMoveLeft}px`
              } else {
                oDiv.style.left = `${left}px`
              }
              if (left < 0) {
                oDiv.style.left = `0px`
              }
              if (top > dMoveTop) {
                oDiv.style.top = `${dMoveTop}px`
              } else {
                oDiv.style.top = `${top}px`
              }
              if (top < 0) {
                oDiv.style.top = `0px`
              }
            }
            document.onmouseup = (e) => {
              const { left, top } = oDiv.style
              node.context.left = parseInt(left)
              node.context.top = parseInt(top)
              saveLocalStorage(oDiv.id, { position: { left, top, width: dWdith, height: dHeight }, type: oDiv.className })
              document.onmousemove = null
              document.onmouseup = null
            }
            // return false不加的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
            return false
          }
        })
      }
    }
  },
  data () {
    let initDataClone = {}
    switch (this.item.type) {
      case types.TextBox:
        initDataClone = cloneDeep(initData.TextBoxDataHorizontal)
        break
      case types.TextBoxVertical:
        initDataClone = cloneDeep(initData.TextBoxDataVertical)
        break
      default:
        initDataClone = cloneDeep(initData.TextBoxDataVertical)
        break
    }
    return { initDataClone }
  },
  computed: {
    boxStyle () {
      return {
        left: `${this.left}px`,
        top: `${this.top}px`,
        width: `${this.width}px`,
        height: `${this.height}px`
      }
    },
    ...mapState({
      actionType: state => state.pw.actionType,
      actionPayload: state => state.pw.actionPayload,
      activeEditItemId: state => state.pw.activeEditItemId
    })
  },
  watch: {
    actionType (val) {
      if (val === 'cancel') {
        this.cancelEdit()
      }
      if (val === 'add-img') {
        this.$refs.editDiv.innerHTML = "<img src='http://localhost:8000/img/photo.05cc0248.png' />"
      }
    }
  },
  mounted () {
    if (this.item.id) {
      let str = sessionStorage.getItem(this.item.id)
      if (str) {
        try {
          str = JSON.parse(str)
          if (str.boxStyle) {
            const { left, top, width, height } = str.boxStyle
            if (left) {
              this.$refs.box.style.left = `${left}px`
            }
            if (top) {
              this.$refs.box.style.top = `${top}px`
            }
            if (width) {
              this.$refs.box.style.width = `${width}px`
            }
            if (height) {
              this.$refs.box.style.height = `${height}px`
            }
          }
          if (str.text) {
            this.$refs.editDiv.innerHTML = str.text
          }
        } catch (e) {
          console.log(e)
        }
      }
    }
  },
  methods: {
    ...mapActions(['UpdateActiveEditItemId']),
    isVertical () {
      return this.item?.type === types.TextBoxVertical
    },
    getClass () {
      let result = ''
      if (this.item?.id === this.activeEditItemId) {
        result = ''
      } else {
        result = 'no-border'
      }
      if (this.item?.type === types.TextBoxVertical) {
        result += ' vertical-mode'
      }
      return result
    },
    startDrag (e) {
      const { UpdateActiveEditItemId } = this
      e.preventDefault()
      UpdateActiveEditItemId(this.item.id)
    },
    enterEditMode () {
      this.isEditing = true
      this.isDragging = true
      const editableDiv = this.$refs.editDiv
      const range = document.createRange()
      const selection = window.getSelection()
      range.selectNodeContents(editableDiv)
      range.collapse(false)
      selection.removeAllRanges()
      selection.addRange(range)
      editableDiv.focus()

      editableDiv.addEventListener('input', () => {
        saveLocalStorage(this.item.id, { info: this.$refs.editDiv.innerHTML, type: this.$refs.box.className })
        if (this.height < editableDiv.offsetHeight) {
          this.height = editableDiv.offsetHeight
        }
      })
    },
    cancelEdit () {
      this.isEditing = false
      this.isDragging = false
      this.$refs.editDiv.blur()
    },
    // 开始
    startResize (e) {
      e.stopPropagation()
      const box = this.$refs.box
      const handle = e.target
      const parentContainer = document.getElementById(this.parentId)
      const parentWidth = parentContainer?.offsetWidth
      const parentHeight = parentContainer?.offsetHeight
      const isTopDrag = handle.className.includes('resize-handle tc')
      const isTopLeftDrag = handle.className.includes('resize-handle tl')
      const isTopRightDrag = handle.className.includes('resize-handle tr')
      const isLeftDrag = handle.className.includes('resize-handle ml')
      const isRightDrag = handle.className.includes('resize-handle mr')
      const isBottomDrag = handle.className.includes('resize-handle bc')
      const isBottomLeftDrag = handle.className.includes('resize-handle bl')
      const isBottomRightDrag = handle.className.includes('resize-handle br')
      handle.onmousedown = (e) => {
        e.stopPropagation()
        const Ev = e
        const Ex = Ev.clientX // 点击鼠标时储存x轴
        const Ey = Ev.clientY // 点击鼠标时储存y轴
        const dw = box.offsetWidth // 存储默认的div的宽度。
        const dh = box.offsetHeight // 存储默认的div的高度。
        const minWidth = 80
        const minHeight = 80
        // const maxChangeWidth = parentWidth - dw - this.left
        const maxChangeHeight = parentHeight - dh - this.top
        const maxToLeftChangeWidth = this.left
        const maxToRightChangeWidth = parentWidth - dw - this.left
        const maxToTopChangeHeight = this.top
        document.onmousemove = (e) => {
          const iE = e
           if (isTopLeftDrag) {
             const deltaX = iE.clientX - Ex
             const deltaY = iE.clientY - Ey
             box.style.width = (dw - deltaX) + 'px'
             box.style.left = (this.left + deltaX) + 'px'
             box.style.height = (dh - deltaY) + 'px'
             box.style.top = (this.top + deltaY) + 'px'
             if (deltaX < 0 && (-deltaX) >= maxToLeftChangeWidth) {
               box.style.width = (dw + maxToLeftChangeWidth) + 'px'
               box.style.left = '0px'
             }
             if (deltaY < 0 && (-deltaY) >= maxToTopChangeHeight) {
               box.style.height = (dh + maxToTopChangeHeight) + 'px'
               box.style.top = '0px'
             }
             if (deltaX > 0 && (dw - deltaX) <= minWidth) {
               box.style.width = `${minWidth}px`
               box.style.left = (this.left + dw - minWidth) + 'px'
             }
             if (deltaY > 0 && (dh - deltaY) <= minHeight) {
               box.style.height = `${minHeight}px`
               box.style.top = (this.top + dh - minHeight) + 'px'
             }
           } else if (isTopRightDrag) {
             const deltaX = iE.clientX - Ex
             const deltaY = iE.clientY - Ey
             box.style.width = (dw + deltaX) + 'px'
             box.style.height = (dh - deltaY) + 'px'
             box.style.top = (this.top + deltaY) + 'px'
             if (deltaX < 0 && Math.abs(dw + deltaX) <= minWidth) {
               box.style.width = (minWidth) + 'px'
             }
             if (deltaY < 0 && (-deltaY) >= maxToTopChangeHeight) {
               box.style.height = (dh + maxToTopChangeHeight) + 'px'
               box.style.top = '0px'
             }
             if (deltaX > 0 && (deltaX) >= maxToRightChangeWidth) {
               box.style.width = `${dw + maxToRightChangeWidth}px`
             }
             if (deltaY > 0 && Math.abs(dh - deltaY) <= minHeight) {
               box.style.height = `${minHeight}px`
               box.style.top = (this.top + dh - minHeight) + 'px'
             }
           } else if (isLeftDrag) {
             const deltaX = iE.clientX - Ex
             box.style.width = (dw - deltaX) + 'px'
             box.style.left = (this.left + deltaX) + 'px'
             if (deltaX < 0 && (-deltaX) >= maxToLeftChangeWidth) {
               box.style.width = (dw + maxToLeftChangeWidth) + 'px'
               box.style.left = '0px'
             }
             if (deltaX > 0 && (dw - deltaX) <= minWidth) {
               box.style.width = `${minWidth}px`
               box.style.left = (this.left + dw - minWidth) + 'px'
             }
           } else if (isRightDrag) {
             const deltaX = iE.clientX - Ex
             // 设置新的宽度和右边位置
             box.style.width = (dw + deltaX) + 'px'
             if (deltaX > 0 && Math.abs(deltaX) >= maxToRightChangeWidth) {
               box.style.width = (dw + maxToRightChangeWidth) + 'px'
             }
             if (deltaX < 0 && Math.abs((dw + deltaX)) <= minWidth) {
               box.style.width = `${minWidth}px`
             }
           } else if (isTopDrag) {
             const deltaY = iE.clientY - Ey
             box.style.height = (dh - deltaY) + 'px'
             box.style.top = (this.top + deltaY) + 'px'
             if (deltaY < 0 && (-deltaY) >= maxToTopChangeHeight) {
               box.style.height = (dh + maxToTopChangeHeight) + 'px'
               box.style.top = '0px'
             }
             if (deltaY > 0 && (dh - deltaY) <= minHeight) {
               box.style.height = `${minHeight}px`
               box.style.top = (this.top + dh - minHeight) + 'px'
             }
           } else if (isBottomDrag) {
             const deltaY = iE.clientY - Ey
             box.style.height = (dh + deltaY) + 'px'
             if (deltaY > 0 && Math.abs(deltaY) >= maxChangeHeight) {
               box.style.height = (dh + maxChangeHeight) + 'px'
             }
             if (deltaY < 0 && Math.abs((dh + deltaY)) <= minHeight) {
               box.style.height = `${minHeight}px`
             }
           } else if (isBottomLeftDrag) {
             const deltaX = iE.clientX - Ex
             const deltaY = iE.clientY - Ey
             box.style.width = (dw - deltaX) + 'px'
             box.style.left = (this.left + deltaX) + 'px'
             box.style.height = (dh + deltaY) + 'px'
             if (deltaY > 0 && Math.abs(deltaY) >= maxChangeHeight) {
               box.style.height = (dh + maxChangeHeight) + 'px'
             }
             if (deltaY < 0 && Math.abs((dh + deltaY)) <= minHeight) {
               box.style.height = `${minHeight}px`
             }
             if (deltaX < 0 && (-deltaX) >= maxToLeftChangeWidth) {
               box.style.width = (dw + maxToLeftChangeWidth) + 'px'
               box.style.left = '0px'
             }
             if (deltaX > 0 && (dw - deltaX) <= minWidth) {
               box.style.width = `${minWidth}px`
               box.style.left = (this.left + dw - minWidth) + 'px'
             }
           } else if (isBottomRightDrag) {
             const deltaX = iE.clientX - Ex
             const deltaY = iE.clientY - Ey
             box.style.width = (dw + deltaX) + 'px'
             box.style.height = (dh + deltaY) + 'px'
             if (deltaX > 0 && Math.abs(deltaX) >= maxToRightChangeWidth) {
               box.style.width = (dw + maxToRightChangeWidth) + 'px'
             }
             if (deltaY > 0 && Math.abs(deltaY) >= maxChangeHeight) {
               box.style.height = (dh + maxChangeHeight) + 'px'
             }
             if (deltaX < 0 && Math.abs(dw + deltaX) <= minWidth) {
               box.style.width = (minWidth) + 'px'
             }
             if (deltaY < 0 && Math.abs(dh + deltaY) <= minHeight) {
               box.style.height = (minHeight) + 'px'
             }
           }
           // else {
           //   if (iE.clientY === 0) {
           //     // 向左移动
           //     box.style.width = dw + (iE.clientX - Ex) + 'px'
           //   } else if (iE.clientX === 0) {
           //     // 向右移动
           //     box.style.height = dh + (iE.clientY - Ey) + 'px'
           //   } else if (iE.clientY !== 0 && iE.clientX !== 0) {
           //     // 同时移动
           //     box.style.width = dw + (iE.clientX - Ex) + 'px'
           //     box.style.height = dh + (iE.clientY - Ey) + 'px'
           //     const changeWidth = iE.clientX - Ex
           //     const changeHeight = iE.clientY - Ey
           //     if (changeWidth > maxChangeWidth) {
           //       box.style.width = dw + maxChangeWidth + 'px'
           //     }
           //     if (dw + (iE.clientX - Ex) <= minWidth) {
           //       box.style.width = `${minWidth}px`
           //     }
           //     if (changeHeight > maxChangeHeight) {
           //       box.style.height = dh + maxChangeHeight + 'px'
           //     }
           //   } else if (box.offsetWidth) {
           //     // 当缩小到一定的范围内不能在缩小
           //     box.style.width = '20px'
           //     box.style.height = '20px'
           //   }
           // }
          }
        document.onmouseup = () => {
          // 更新最后的位置
          const { left, top, width, height } = box.style
          this.left = parseInt(left)
          this.top = parseInt(top)
          this.width = parseInt(width)
          this.height = parseInt(height)
          saveLocalStorage(this.$refs.box.id, { position: { left: `${left}px`, top: `${top}px`, width: `${width}`, height: `${height}px` } })
          // 鼠标离开时清除事件
          document.onmousemove = null
        }
      }
    }
  }
}
</script>
<style scoped lang="less">
  .draggable-editable-box {
    position: absolute;
    background-color: transparent;
    border: 1px dashed #ff6200;
    cursor: move;
    //padding: 10px;
    left: 0;
    top: 0;
    width: 200px;
    height: 100px;
    box-sizing: border-box;
    user-select: none;

    .content {
       position:relative;
       width: 100%;
       height: 100%;
      //padding: 5px; /* 内边距 */
      //overflow-y: auto; /* 超出内容时显示滚动条 */
      //white-space: pre-wrap; /* 保留换行符并自动换行 */
      //box-sizing: border-box; /* 包含内边距和边框在总高度中 */

      .resize-handle {
        position: absolute;
        width: 6px;
        height: 6px;
        border: 1px solid red;
        background-color:#fff;
        left: 0;
        top: 0;
      }
      .tl {
        left: -3px;
        top: -3px;
        cursor: nwse-resize;
      }
      .tc {
        left: calc(50% - 3px);
        top: -3px;
        cursor: ns-resize;
      }
      .tr {
        left: calc(100% - 3px);
        top: -3px;
        cursor: nesw-resize;
      }
      .ml {
        left: -3px;
        top: calc(50% - 3px);
        cursor: ew-resize;
      }
      .mr {
        left: calc(100% - 3px);
        top: calc(50% - 3px);
        cursor: ew-resize;
      }
      .bl {
        left: -3px;
        top: calc(100% - 3px);
        cursor: nesw-resize;
      }
      .bc {
        left: calc(50% - 3px);
        top: calc(100% - 3px);
        cursor: ns-resize;
      }
      .br {
        left: calc(100% - 3px);
        top: calc(100% - 3px);
        cursor: nwse-resize;
      }
      .edit-text {
        //padding: 10px;
        outline: none;
        font-size:29px;
        .placeholder {
          color: #d7d7d7;
        }
        img {
          height: 100%;
          width: 100%;
        }
      }
      .vertical-mode {
        text-align: center;
        writing-mode: vertical-rl;
        font-size: 29px;
        letter-spacing: 3px; color: rgb(0, 0, 0);
      }
    }
  }
  .no-border { border: 0}
  .draggable-editable-box.vertical-mode {
    width: 80px;
    height: 650px;
  }
</style>
<style lang='less'>
.edit-text {
  img {
    height: 100%;
    width: 100%;
  }
}
</style>
