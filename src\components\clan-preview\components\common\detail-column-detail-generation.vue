<template>
  <div type="1">
    <div class="generation" :style="{ left: x + 'px' }">
      <div>
        <span v-for="(item, index) in txt.split('')" :key="index">{{ item }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'DetailColumnDetailGeneration',
  props: {
    txt: {
      type: String,
      default: ''
    },
    x: {
      type: Number,
      default: 0
    }
  }
}
</script>
<style lang="less" scoped>
.generation {
  position: absolute;
  top: 25px;
  width: 28px;
  border: 1px solid #333;
  border-radius: 5px;
  padding: 2px;
  height: calc(100% - 50px);
  font-size: 16px;

  div {
    width: 100%;
    height: 100%;
    background-color: #333;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    overflow: hidden;
  }
}
</style>
