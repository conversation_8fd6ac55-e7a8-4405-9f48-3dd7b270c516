<template>
  <div class="second-column" v-show="showBase">
    <div class="base-header">
      {{ activeNodeInfo?.husband_id ? '配偶信息' : '基本信息' }}
      <i class="cus-edit-toolbar-icon icon-edit-toolbarzhedie1 c-pointer-hover" @click="handleShowBaseMore" />
    </div>
    <div class="base-content-wrapper">
      <a-spin :spinning="loadingDetail">
        <div class="base-content">
          <div class="photo">
            <div class="default" />
            <div class="close">
              <i class="cus-edit-toolbar-icon icon-edit-toolbaricon-mianxing_fuzhi_guanbi" />
            </div>
            <div class="tips">点击更换</div>
          </div>
          <div class="flex flex-direction-row align-center form-item">
            <div class="title">姓氏</div><a-input v-model="firstName" @blur="handleSaveMemberInfo('first_name')" />
          </div>
          <div class="flex flex-direction-row align-center form-item">
            <div class="title">名字</div><a-input v-model="name" @blur="handleSaveMemberInfo('name')" />
          </div>
          <div class="flex flex-direction-row align-center form-item">
            <div class="title">常用名</div><a-input v-model="realName" @blur="handleSaveMemberInfo('real_name')" />
          </div>
          <div class="flex flex-direction-row align-center form-item">
            <div class="title">性别</div>
            <a-select
              v-model="sex"
              style="width: 190px"
              class="select-no-border"
              :disabled="!!activeNodeInfo?.husband_id"
              @change="handleSaveMemberInfo('sex')">
              <a-select-option value="男">男</a-select-option>
              <a-select-option value="女">女</a-select-option>
            </a-select>
          </div>
          <div class="flex flex-direction-row align-center form-item" v-if="!activeNodeInfo?.husband_id">
            <div class="title">父母</div>
            <a-select style="width: 190px;" class="select-no-border" v-model="fatherId" @change="handleSaveMemberInfo('father_id')">
              <a-select-option :value="item.id" :key="item.id" v-for="item in getParentsList">{{ item.name }}</a-select-option>
            </a-select>
          </div>
          <div class="flex flex-direction-row align-center form-item" v-if="activeNodeInfo?.husband_id">
            <div class="title">配偶关系</div>
            <a-select style="width: 190px;" class="select-no-border" v-model="wifeRelation" @change="handleSaveMemberInfo('relation')">
              <a-select-option v-for="relation in wifeConstantList" :key="relation" :value="relation">
                {{ relation }}
              </a-select-option>
            </a-select>
          </div>
          <div class="flex flex-direction-row align-center form-item" v-if="activeNodeInfo?.husband_id">
            <div class="title">父亲</div>
            <a-input
              style="width: 190px;"
              v-model="fatherName"
              placeholder="请输入父亲姓名"
              @blur="handleSaveMemberInfo('father_name')"
              @pressEnter="handleSaveMemberInfo('father_name')" />
          </div>
          <div class="flex flex-direction-row align-center form-item" v-if="!activeNodeInfo?.husband_id">
            <div class="title">排行</div>
            <a-select
              v-model="rankingText"
              style="width: 190px"
              class="select-no-border"
              @change="handleSaveMemberInfo('rankingText')">
              <a-select-option value="始祖" v-if="'始祖' === largeRankingText">始祖</a-select-option>
              <a-select-option :value="getRankingText(item)" v-for="item in rankingOptionsList" :key="item">{{
                getRankingText(item, sex) }}</a-select-option>
              <a-select-option :value="sex === '女' ? '之女' : '之子'">{{ sex === '女' ? '之女' : '之子' }}</a-select-option>
            </a-select>
          </div>
          <div class="flex flex-direction-row align-center form-item">
            <div class="title">生于</div><a-input
              v-model="birthday"
              placeholder="请输入"
              @blur="handleSaveMemberInfo('birthday')" />
          </div>
          <div class="flex flex-direction-row align-center form-item">
            <div class="title">状态</div>
            <a-select
              v-model="livingStatus"
              style="width: 190px"
              class="select-no-border"
              @change="handleSaveMemberInfo('living_status')">
              <a-select-option :value="1">在世</a-select-option>
              <a-select-option :value="2">去世</a-select-option>
            </a-select>
          </div>
          <div class="flex flex-direction-row align-center form-item" v-show="livingStatus === 2">
            <div class="title">卒于</div><a-input
              v-model="deathTime"
              placeholder="请输入"
              @blur="handleSaveMemberInfo('death_time')" />
          </div>
          <div class="flex flex-direction-row align-center form-item" v-show="livingStatus === 2">
            <div class="title">享年</div>
            <a-select
              v-model="ageAtDeath"
              style="width: 190px"
              class="select-no-border"
              @change="handleSaveMemberInfo('age_at_death')">
              <a-select-option :value="age" v-for="age in ageOptionList" :key="age">{{ age }}</a-select-option>
            </a-select>
          </div>
          <div class="flex flex-direction-row align-center form-item" v-show="livingStatus === 2">
            <div class="title">葬地</div><a-input
              v-model="cemeteryAddress"
              @blur="handleSaveMemberInfo('cemetery_address')" />
          </div>
          <wife-children-info v-if="selectedNode && selectedNode.is_main && !activeNodeInfo?.husband_id" :selectedNode="selectedNode" @refresh="handleRefreshDetail" />
        </div>
      </a-spin>
    </div>
  </div></template>

<script>
import { mapState, mapActions } from 'vuex'
import { getRankText } from '@/utils/rankTextUtil'
import { wifeConstantList } from '@/constant/wife-constant'
import WifeChildrenInfo from './wife-children-info'

export default {
  name: 'MemberBaseInfo',
  components: {
    WifeChildrenInfo
  },
  props: {
    showBase: {
      type: Boolean,
      default: true
    },
    activeNodeInfo: {
      type: Object,
      default: () => ({})
    },
    selectedNode: {
      type: Object,
      default: () => ({})
    },
    selectedNodeId: {
      type: [String, Number],
      default: ''
    },
    rawDetail: {
      type: Object,
      default: () => ({})
    },
    father: {
      type: Object,
      default: () => ({})
    },
    loadingDetail: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      firstName: '',
      name: '',
      realName: '',
      sex: '',
      fatherName: '',
      fatherId: '',
      rankingText: '',
      livingStatus: 1,
      birthday: '',
      deathTime: '',
      ageAtDeath: 100,
      cemeteryAddress: '',
      wifeRelation: wifeConstantList[0] || '娶', // 配偶关系
      largeRankingText: '',
      rankingOptionsList: Array(20).fill(0).map((_, index) => index + 1),
      ageOptionList: Array(151).fill(0).map((_, index) => index),
      wifeConstantList: wifeConstantList // 配偶关系选项列表
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    }),
    getParentsList () {
      const { id = -1, name, wife = [] } = this.father || {}
      const showName = name || '-'
      const wifeList = wife || []
      const options = [{ id, name: showName }]
      wifeList.forEach(item => {
        options.push({
          id: item.id,
          name: `${showName}|${item.name}`
        })
      })
      return options
    }
  },
  watch: {
    activeNodeInfo: {
      handler (newVal) {
        if (newVal) {
          this.updateFormData(newVal)
        }
      },
      immediate: true,
      deep: true
    },
    rawDetail: {
      handler (newVal) {
        if (newVal?.info) {
          this.updateFormData(newVal.info)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    ...mapActions(['SaveMemberInfo']),
    handleShowBaseMore () {
      this.$emit('toggleBaseMore')
    },
    getRankingText (num, sex) {
      return getRankText(num, sex)
    },
    updateRankingTextBySex () {
      // 当性别改变时，自动更新排行文本
      const currentRanking = this.rankingText

      // 如果当前排行是"始祖"，不需要更新
      if (currentRanking === '始祖') {
        return
      }

      // 处理"长子"/"长女"的转换
      if (currentRanking === '长子' && this.sex === '女') {
        this.rankingText = '长女'
      } else if (currentRanking === '长女' && this.sex === '男') {
        this.rankingText = '长子'
      } else if (currentRanking && currentRanking.includes('子') && this.sex === '女') {
           // 处理"次子"/"次女"等的转换
        this.rankingText = currentRanking.replace('子', '女')
      } else if (currentRanking && currentRanking.includes('女') && this.sex === '男') {
        this.rankingText = currentRanking.replace('女', '子')
      } else if (currentRanking === '之子' && this.sex === '女') {
         // 处理"之子"/"之女"的转换
        this.rankingText = '之女'
      } else if (currentRanking === '之女' && this.sex === '男') {
        this.rankingText = '之子'
      }
    },
    updateFormData (nodeInfo) {
      if (nodeInfo) {
        this.firstName = nodeInfo.first_name || ''
        this.name = nodeInfo.name || ''
        this.realName = nodeInfo.real_name || ''
        // 如果是配偶节点，性别固定为女性
        this.sex = nodeInfo.husband_id ? '女' : (nodeInfo.sex || '男')
        this.fatherName = nodeInfo.father_name || ''
        this.fatherId = nodeInfo.father_id || -1
        this.rankingText = nodeInfo.ranking_text || nodeInfo.rankingText || ''
        this.largeRankingText = nodeInfo.ranking_text || nodeInfo.rankingText || ''
        this.birthday = nodeInfo.birthday || ''
        this.deathTime = nodeInfo.death_time || ''
        this.ageAtDeath = nodeInfo.age_at_death || 100
        this.cemeteryAddress = nodeInfo.cemetery_address || ''
        this.livingStatus = nodeInfo.living_status || 1
        this.wifeRelation = nodeInfo.relation || wifeConstantList[0] || '娶'
      }
    },
    async handleSaveMemberInfo (type) {
      const { SaveMemberInfo } = this
      const currentId = this.activeNodeInfo?.id || this.selectedNodeId
      if (!currentId) {
        this.$message.error('请选择要保存的节点')
        return
      }
      const info = this.rawDetail?.info
      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId,
        id: currentId
      }

      // 根据类型设置参数并检查是否有变化
      switch (type) {
        case 'first_name':
          params.first_name = this.firstName
          if (info && info.first_name === this.firstName) return
          break
        case 'name':
          params.name = this.name
          if (info && info.name === this.name) return
          break
        case 'real_name':
          params.real_name = this.realName
          if (info && info.real_name === this.realName) return
          break
        case 'sex':
          params.sex = this.sex
          if (info && info.sex === this.sex) return
          // 性别改变时，自动更新排行文本
          this.updateRankingTextBySex()
          // 同时在接口中包含更新后的排行文本
          params.rankingText = this.rankingText
          break
        case 'rankingText':
          params.rankingText = this.rankingText
          if (info && info.rankingText === this.rankingText) return
          break
        case 'age_at_death':
          params.age_at_death = this.ageAtDeath
          if (info && info.age_at_death === this.ageAtDeath) return
          break
        case 'cemetery_address':
          params.cemetery_address = this.cemeteryAddress
          if (info && info.cemetery_address === this.cemeteryAddress) return
          break
        case 'birthday':
          params.birthday = this.birthday
          if (info && info.birthday === this.birthday) return
          break
        case 'relation':
          params.relation = this.wifeRelation
          if (info && info.relation === this.wifeRelation) return
          break
        case 'father_name':
          params.father_name = this.fatherName
          if (info && info.father_name === this.fatherName) return
          break
        case 'living_status':
          params.living_status = this.livingStatus
          if (info && info.living_status === this.livingStatus) return
          break
        case 'death_time':
          params.death_time = this.deathTime
          if (info && info.death_time === this.deathTime) return
          break
        case 'father_id':
          params.father_id = this.fatherId
          if (info && info.father_id === this.fatherId) return
          break
      }

      this.$emit('update:loadingDetail', true)
      try {
        const res = await SaveMemberInfo(params)
        if (res.code === 0) {
          this.$message.success('保存成功')
          // 通知父组件更新世系树数据，本地数据会通过updateTreeData事件同步更新
          this.$emit('updateTreeData', {
            nodeId: currentId,
            updatedData: params,
            fieldType: type
          })
        } else {
          this.$message.error(res.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存失败:', error)
      } finally {
        this.$emit('update:loadingDetail', false)
      }
    },
    handleRefreshDetail () {
      // 处理WifeChildrenInfo组件的refresh事件
      // 这里仍然需要刷新详情，因为WifeChildrenInfo修改的是配偶和子女信息
      this.$emit('refreshDetail')
    }
  }
}
</script>

<style lang="less" scoped>
.second-column {
  padding:4px 0 0 4px;
  width: 240px;
  height: calc(100vh - 70px);
  display: flex;
  flex-direction: column;

  .base-header {
    background-color: #fff;
    padding: 10px 0 10px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    .icon-edit-toolbarzhedie1 {
      padding: 0 10px;
    }
  }

  .base-content-wrapper {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }

  .base-content {
    margin-top: 4px;
    background-color: #fff;
    padding: 6px;

    .photo {
      height: 134px;
      width: 100px;
      position: relative;
      box-sizing: border-box;
      padding: 8px;
      margin: 0 auto;
      margin-bottom: 5px;

      .default {
        background: #f0f2f5 url(~@/assets/photo.png) no-repeat center center;
        height: 100%;
        width: 100%;
        background-size: cover;
      }

      .tips {
        position: absolute;
        bottom: 0;
        right: 0;
        left: 0;
        background: rgba(0, 0, 0, .4);
        color: #fff;
        font-size: 12px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        cursor: pointer;
      }

      .close {
        position: absolute;
        height: 16px;
        width: 16px;
        background: #fff;
        border-radius: 50%;
        right: 0;
        top: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        z-index: 1;

        .cus-edit-toolbar-icon {
          color: #f76d02;
        }
      }
    }

    .form-item {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      margin: 8px 0;

      .title {
        text-align: center;
        height: 32px;
        width: 80px;
        align-content: center;
        border-right: 1px solid #dcdfe6;
        background-color: #f5f7fa;
        font-size: 13px;
        flex-shrink: 0;
      }

      /deep/ .ant-input {
        border: 0;
        font-size: 13px;
      }

      /deep/ .ant-select {
        border: 0;
        font-size: 13px;

        .ant-select-arrow-icon {
          font-size: 13px !important;
        }
      }
    }

    .bind-account {
      height: 34px;
      justify-content: center;
      border: 1px solid #f86e04;
      color: #f86e04;
      margin: 20px 0;
    }

    .bind-account:hover {
      background-color: #f86e04;
      color: #fff;
    }
  }
}
</style>
