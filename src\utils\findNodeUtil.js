/**
 * 根据 id 查找节点
 * @param tree
 * @param id
 * @returns {null|*}
 */
export const findNodeById = (tree, id) => {
  // 参数 tree 是树状结构的数据，id 是要查找的节点的 id
  // 遍历树结构，查找 id 匹配的节点
  for (const node of tree) {
    // 如果当前节点的 id 匹配，返回该节点
    if (node.id === id) {
      return node
    }
    // 如果当前节点有子节点，递归查找子节点
    if (node.children && node.children.length > 0) {
      const foundNode = findNodeById(node.children, id)
      if (foundNode) {
        return foundNode
      }
    }
  }
  // 如果遍历完整棵树都没找到，返回 null
  return null
}
