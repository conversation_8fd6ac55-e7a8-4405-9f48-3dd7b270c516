<template>
  <a-modal
    :title="title"
    :visible="visible"
    :width="900"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="保存"
    cancelText="取消"
  >
    <div>设庄功能暂未实现</div>
  </a-modal>
</template>
<script>
export default {
  name: 'ShezhuangModal',
  data  () {
    return {
      title: '设庄1',
      visible: true,
      confirmLoading: false
    }
  },
  props: {
    selectedNode: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    selectedNode: {
      handler (val) {
        console.log(val)
        this.title = `设庄: ${val.name}`
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleOk () {
      this.confirmLoading = true
      setTimeout(() => {
        this.confirmLoading = false
        this.visible = false
      }, 1000)
    },
    handleCancel () {
      this.$emit('close')
    },
    getShowName () {
      const { nodeInfo } = this
      return nodeInfo.name
    },
    getParent (data) {
      const { nodeInfo } = this
      return nodeInfo.parent
    },
    handleTabChange (type) {
      const { nodeInfo } = this
      switch (type) {
        case 'first_name':
        case 'name':
        case 'real_name':
        case 'sex':
          nodeInfo[type] = this.nodeInfo[type]
      }
    }
  }
}
</script>
<style lang="less" scoped>
</style>
