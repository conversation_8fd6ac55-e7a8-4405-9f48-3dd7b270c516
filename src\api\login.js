import request from '@/utils/request'

const userApi = {
  Login: '/web/user/webAccountLogin',
  Logout: '/auth/logout',
  ForgePassword: '/auth/forge-password',
  Register: '/web/user/webUserRegister',
  twoStepCode: '/auth/2step-code',
  SendSms: '/account/sms',
  SendSmsErr: '/account/sms_err',
  // get my info
  UserInfo: '/user/info',
  UserMenu: '/user/nav'
}

/**
 * 用户注册
 * @param {object} parameter - 请求体
 * @param {string} parameter.username - 用户名
 * @param {string} parameter.password - 密码(加密后的字符串)
 * @param {string} parameter.mobile - 手机号
 * @returns {Promise<{id: number, username: string, token: string, expires: number}>}
 */
export function register (parameter) {
  return request({
    url: userApi.Register,
    method: 'post',
    data: parameter
  })
}

/**
 * 登录函数
 * @param {object} parameter - 请求体
 * @param {string} parameter.username - 用户名
 * @param {string} parameter.password - 密码(加密后的字符串)
 * @returns {Promise<{id: number, username: string, token: string, expires: number}>}
 */
export function login (parameter) {
  return request({
    url: userApi.Login,
    method: 'post',
    data: parameter
  })
}

/**
 * 获取短信验证码
 * @param {object} parameter - 请求参数
 * @returns {Promise<object>}
 */
export function getSmsCaptcha (parameter) {
  return request({
    url: userApi.SendSms,
    method: 'post',
    data: parameter
  })
}

/**
 * 获取当前用户信息
 * @returns {Promise<object>}
 */
export function getInfo () {
  return request({
    url: userApi.UserInfo,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取当前用户导航菜单
 * @returns {Promise<object>}
 */
export function getCurrentUserNav () {
  return request({
    url: userApi.UserMenu,
    method: 'get'
  })
}

/**
 * 登出
 * @returns {Promise<object>}
 */
export function logout () {
  return request({
    url: userApi.Logout,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取用户两步验证状态
 * @param {object} parameter - 请求参数
 * @returns {Promise<object>}
 */
export function get2step (parameter) {
  return request({
    url: userApi.twoStepCode,
    method: 'post',
    data: parameter
  })
}
