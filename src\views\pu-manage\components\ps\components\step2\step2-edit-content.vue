<template>
  <div class="step2-edit-content-wrap flex">
    <div class="flex flex-1 left flex-direction-column">
      <step3-edit-content-item :selected="step2SelectedItemText==='卷首'" title="卷首" sub-title="如前言、序、谱论、凡例、编修名录等">
        <info-chapter />
        <info-pu-wen />
        <info-shi-xi />
        <info-son-chapter />
        <step2-edit-content-add-btn v-if="1===2"/>
      </step3-edit-content-item>
      <step3-edit-content-item :selected="step2SelectedItemText==='正文目录'" title="正文目录" sub-title="如源流、碑文、恩荣录、祠堂、坟茔、字辈等">
        <step2-edit-content-add-btn />
      </step3-edit-content-item>
      <step3-edit-content-item :selected="step2SelectedItemText==='世系目录'" title="世系目录" sub-title="添加世系表、世系分支等">
        <step2-edit-content-add-btn />
      </step3-edit-content-item>
      <step3-edit-content-item :selected="step2SelectedItemText==='附录'" title="附录" sub-title="如家规家训、名人传记、风俗礼仪、文献杂记、领谱字号、后记等">
        <step2-edit-content-add-btn />
      </step3-edit-content-item>
    </div>
    <div class="right" :class="showDetail?'open':''">
      <right-quick-opt @click="handleClick" @toggle="handleToggle"/>
      <div class="flex flex-direction-column detail" v-if="showDetail">
        <div class="tabs flex flex-direction-row">
          <div class="item" :class="showRightType===1?'on':''" @click="showRightType=1">谱系分支</div>
          <div class="item" :class="showRightType===2?'on':''" @click="showRightType=2">谱文</div>
        </div>
        <RightPuXiFenZhi v-if="showRightType===1"/>
        <RightPuWen v-if="showRightType===2"/>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import RightQuickOpt from '@/views/pu-manage/components/ps/components/step3/opt'
import Step2EditContentAddBtn from '@/views/pu-manage/components/ps/components/step2/add-btn'
import Step3EditContentItem from '@/views/pu-manage/components/ps/components/step2/components/item-content'
import InfoChapter from '@/views/pu-manage/components/ps/components/step2/components/info-chapter'
import InfoPuWen from '@/views/pu-manage/components/ps/components/step2/components/info-puwen'
import InfoShiXi from '@/views/pu-manage/components/ps/components/step2/components/info-shixi'
import InfoSonChapter from '@/views/pu-manage/components/ps/components/step2/components/info-zizhangjie'
import RightPuXiFenZhi from '@/views/pu-manage/components/ps/components/step2/components/right-puxifenzhi'
import RightPuWen from '@/views/pu-manage/components/ps/components/step2/components/right-puwen'
import * as actionTypes from '@/store/modules/ps-action-type'

export default {
  name: 'Step2EditContent',
  components: {
    RightPuWen,
    RightPuXiFenZhi,
    InfoSonChapter,
    InfoShiXi,
    InfoPuWen,
    InfoChapter,
    Step3EditContentItem,
    Step2EditContentAddBtn,
    RightQuickOpt
  },
  data () {
    return {
      showDetail: true,
      showRightType: 1
    }
  },
  computed: {
    ...mapState({
      step2SelectedItemText: state => state.ps.step2SelectedItemText,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedBook: state => state.ps.currentSelectedBook
    })
  },
  mounted () {
    const { currentSelectedClanId, currentSelectedBook, UpdateStep2BookStructure } = this
    if (currentSelectedBook && currentSelectedBook.id) {
      const bookId = currentSelectedBook.id
      this.GetBookPrintDetail({ clan_id: currentSelectedClanId, book_id: bookId })
      UpdateStep2BookStructure({ index: 0, content: { clanId: currentSelectedClanId, bookPrintId: bookId } })
      UpdateStep2BookStructure({ index: 1, content: { clanId: currentSelectedClanId, bookPrintId: bookId } })
      UpdateStep2BookStructure({ index: 2, content: { clanId: currentSelectedClanId, bookPrintId: bookId } })
      UpdateStep2BookStructure({ index: 3, content: { clanId: currentSelectedClanId, bookPrintId: bookId } })
    }
  },
  methods: {
    ...mapActions([
      'GetBookPrintDetail',
      'UpdateStep2BookStructure'
    ]),
    handleToggle (isShow) {
      this.showDetail = isShow
    },
    handleClick (type) {
      switch (type) {
        case actionTypes.PS_RIGHT_OPTION_RETURN:
          this.$emit('return')
          break
        case actionTypes.PS_RIGHT_OPTION_SAVE:
          console.log('ddd save')
          break
      }
    }
  }
}
</script>
<style lang='less' scoped>
.step2-edit-content-wrap {
  background-color:#f7f8fa;
  text-align:left;
  //min-width: 1200px;
  //overflow-x: auto;
  .left {
    height:calc(100vh - 100px);
    min-width: 850px;
    overflow-y: auto;
  }
  .right {
    display:flex;
    padding-left:10px;
    min-width:0px;
    &.open {
      min-width: 580px;
    }
    .opt {
      width: 80px;
    }
    .detail {
      flex:1;
      background-color: #f7f8fa;
      padding-left: 4px;
      overflow-y: auto;
      .tabs {
        flex-shrink: 0;
        display: flex;
        height: 52px;
        font-size: 16px;
        .item {
          display: flex;
          align-items: center;
          justify-content: center;
          flex: 1;
          background-color: #f7f7f7;
          cursor: pointer;
          color: #666;
          &.on {
            color: #333;
            background-color: #fff;
          }
        }
      }
    }
  }
}
</style>
