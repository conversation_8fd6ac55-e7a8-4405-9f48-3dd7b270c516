<template>
  <div :class="toggleClass()">
    <div title="收起" class="toggle-btn" @click="handleToggle">
      <i>{{ visible ? '<' : '>' }}</i>
      <div class="hint">
        <div>点</div>
        <div>击</div>
        <div>{{ visible ? '收' : '展' }}</div>
        <div>{{ visible ? '起' : '开' }}</div>
      </div>
    </div>
    <div v-if="visible">
      <div class="demo" @click="showFamilyBaseInfo=true">
        <span class="tag tag-fanli">管理</span>
        <span class="name line-word">{{ clan?.name }}</span>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarchaxun"></i>
      </div>
      <div class="operate">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarzengjiawenzhang" title="新建谱文" @click="handleArticle()" />
        <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou1" title="下移" @click="handleMove('up')"/>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou" title="上移"@click="handleMove('up')"/>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarfuzhi" title="复制" @click="handleCopy()"/>
        <br />
        <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1" title="删除" @click="handleTopDelete()"/>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarmigrate" title="文章迁移" @click="handleQianYi()"/>
      </div>
      <a-divider style="margin: 12px 0"/>
      <a-spin :spinning="groupListLoading">
        <div class="catalog-li">
          <div v-for="item in groupList" :key="item.id" class="catalog-item">
            <div class="title" :class="selectedCategoryId === item.id?' selected':''" @click="toggleCategory(item.id)">
              <div>
                <i class="cus-edit-toolbar-icon" :class="openCategoryIdList.includes(item.id) ? 'icon-edit-toolbardakai':'icon-edit-toolbarwenjianjia'" style="font-size:16px;margin:0"/>
                {{ item.name }}
              </div>
              <div>
                <i
                  class="cus-edit-toolbar-icon icon-edit-toolbarxiugai dynamic-icon"
                  style="font-size:16px;margin:0"
                  @click="(e) => handleEdit(item, e)"
                />
                <a-popconfirm title="确定删除类目?" placement="bottom" @confirm="remove(item.id)">
                  <i
                    class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 dynamic-icon"
                    style="font-size:16px;margin:0"
                    @click="(e) => handleDelete(item.id, e)"
                  />
                </a-popconfirm>
              </div>
            </div>
            <div class="article-item" v-show="openCategoryIdList.includes(item.id)">
              <div
                v-for="article in item.article_info"
                :key="article.id"
                class="c-pointer-hover mt4 flex justify-content-space-between"
                :class="selectedArticleId === article.id ? ' selected':''"
                @click="handleArticleClick(article, item)"
              >
                <div>{{ article.name }}</div>
                <div>
                  <i
                    class="cus-edit-toolbar-icon icon-edit-toolbarxiugai dynamic-icon"
                    style="font-size:16px;margin:0"
                    @click="(e) => handleArticleEdit(article, e)"
                  />
                  <a-popconfirm title="确定删除谱文?" placement="bottom" @confirm="(e) => removeArticle(article.id, e)">
                    <i
                      class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 dynamic-icon"
                      style="font-size:16px;margin:0"
                      @click="(e) => handleDeleteArticle(article.id, e)"
                    />
                  </a-popconfirm>
                </div>
              </div>
              <div class="add-btn mt4" @click="handleAddArticle(item)">新建谱文</div>
            </div>
          </div>
          <a-empty v-show="groupList?.length===0" />
        </div>
      </a-spin>
      <div class="add-category">
        <div @click="handleAddCategory()"><i class="cus-edit-toolbar-icon icon-edit-toolbarzengjiawenjianjia" />新建类目</div>
      </div>
    </div>
    <add-category v-if="addCategoryModal" @close="addCategoryModal=false" @refresh="getList" />
    <add-article v-if="addArticleModal" :category="selectedAddArticleCategory" @close="addArticleModal=false" @refresh="getList"/>
    <family-info-component v-if="showFamilyBaseInfo" @close="showFamilyBaseInfo=false"/>
  </div>
</template>
<script>
import { Modal } from 'ant-design-vue'
import { mapActions, mapState } from 'vuex'
import AddCategory from '@/views/pu-manage/components/pw/components/add-category'
import AddArticle from '@/views/pu-manage/components/pw/components/add-article'
import FamilyInfoComponent from '@/views/components/family-info'
export default {
  name: 'LeftContentForPW',
  components: { FamilyInfoComponent, AddCategory, AddArticle },
  data () {
    return {
      visible: true,
      showFamilyBaseInfo: false,
      addCategoryModal: false,
      addArticleModal: false,
      selectedAddArticleCategory: null,
      openCategoryIdList: [],
      selectedCategoryId: '',
      selectedArticleId: '',
      groupListLoading: false,
      groupList: [],
      editItem: null,
      clan: {}
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedGroupId: state => state.family.currentSelectedGroupId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  async mounted () {
    await this.getList()
    if (!this.currentSelectedGroupId) {
      return
    }
    const result = await this.GetCurrentClan()
    this.clan = result
  },
  methods: {
    ...mapActions(['GetClanArticleList', 'DeletedCategory', 'UpdateCategory', 'GetCurrentClan', 'UpdateClanArticle']),
    toggleCategory (id) {
      if (this.openCategoryIdList.includes(id)) {
        this.openCategoryIdList = this.openCategoryIdList.filter(item => item !== id)
      } else {
        this.openCategoryIdList.push(id)
      }
    },
    handleArticleClick (article, category) {
      this.selectedCategoryId = category.id
      this.selectedArticleId = article.id
      this.$emit('update', { article, category })
    },
    handleAddCategory () {
      if (!this.currentSelectedClanId) {
        this.$message.info('请先添加家族')
        return
      }
      this.addCategoryModal = true
    },
    handleArticle () {
      if (!this.currentSelectedClanId) {
        this.$message.info('请先添加家族')
        return
      }
      this.addArticleModal = true
    },
    async handleMove (type) {
      if (!this.currentSelectedClanId) {
        this.$message.info('请选择要移动的文章')
        return
      }
      this.$message.info('移动信息待完善')
      switch (type) {
        case 'up':
          break
        case 'down':
          break
      }
    },
    async handleCopy () {
      if (!this.currentSelectedClanId) {
        this.$message.info('请选择要复制的文章')
        return
      }
      this.loading = true
      // 进行copy逻辑
      this.loading = false
    },
    async handleTopDelete () {
      if (!this.currentSelectedClanId) {
        this.$message.info('请选择要删除的文章')
        return
      }
      this.loading = true
      // 进行delete逻辑
      this.loading = false
    },
    handleQianYi () {
      if (!this.currentSelectedClanId) {
        this.$message.info('请选择要迁移的文章')
        return
      }
      this.loading = true
      // 进行迁移逻辑
      this.loading = false
    },
    handleEdit (item, e) {
      e.stopPropagation()
      const { UpdateCategory } = this
      const that = this
      Modal.confirm({
        title: '修改谱文类目',
        icon: null,
        content: <a-input ref='editInput' id='editInput' />,
        async onOk () {
          const name = document.getElementById('editInput').value.trim()
          if (!name) {
            that.$message.error('请输入类目名称')
            return
          }
          const res = await UpdateCategory({
            category_id: item.id,
            name: name,
            clan_id: that.currentSelectedClanId
          })
          if (res && res.code === 0) {
            that.$message.success('修改成功')
            await that.getList()
          } else {
            that.$message.error(res.message || '修改失败')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
      this.$nextTick(() => {
        this.$refs.editInput.value = item.name
      })
    },
    async handleDelete (item, e) {
      e.stopPropagation()
    },
    async remove (id) {
      const { DeletedCategory, currentSelectedClanId } = this
      const res = await DeletedCategory({ category_id: id, clan_id: currentSelectedClanId })
      if (res && res.code === 0) {
        this.$message.success('删除成功')
        await this.getList()
      } else {
        this.$message.error(res.message || '删除失败')
      }
    },
    async handleAddArticle (category) {
      if (!this.currentSelectedClanId) {
        this.$message.info('请先添加家族')
        return
      }
      this.selectedAddArticleCategory = category
      this.addArticleModal = true
    },
    async removeArticle (id, e) {
      e.stopPropagation()
      console.log('id', id)
    },
    async handleArticleEdit (article, e) {
      e.stopPropagation()
      const { UpdateClanArticle } = this
      const that = this
      Modal.confirm({
        title: '修改谱文标题',
        icon: null,
        content: <a-input ref='editInput' id='articleInput' />,
        async onOk () {
          const name = document.getElementById('articleInput').value.trim()
          if (!name) {
            that.$message.error('请输入谱文标题')
            return
          }
          const res = await UpdateClanArticle({
            id: article.id,
            name: name,
            clan_id: that.currentSelectedClanId
          })
          if (res && res.code === 0) {
            that.$message.success('修改成功')
            await that.getList()
          } else {
            that.$message.error(res.message || '修改失败')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
      this.$nextTick(() => {
        this.$refs.editInput.value = article.name
      })
    },
    async handleDeleteArticle (articleId, e) {
      e.stopPropagation()
      console.log('id', articleId)
      this.$message.info('删除信息待完善')
    },
    async getList () {
      const { GetClanArticleList, currentSelectedClanId } = this
      if (!currentSelectedClanId) {
        return
      }
      this.groupListLoading = true
      const res = await GetClanArticleList({ clan_id: currentSelectedClanId })
      this.groupListLoading = false
      if (res.code === 0) {
        const _data = res.data.list
        this.groupList = _data
        this.handleDefaultSelected(_data)
      }
    },
    // 处理默认选中
    handleDefaultSelected (data) {
      if (data?.length > 0) {
        const { openCategoryIdList, selectedCategoryId, selectedArticleId } = this
        if (!selectedCategoryId && !selectedArticleId && openCategoryIdList.length === 0) {
          data.forEach(item => {
            if (item.article_info?.length && !this.selectedCategoryId) {
              this.selectedCategoryId = item.id
              this.selectedArticleId = item.article_info[0].id
              this.openCategoryIdList.push(item.id)
              const category = item
              const article = item.article_info[0]
              this.$emit('update', { article, category })
            }
          })
        }
      }
    },
    handleToggle () {
      this.visible = !this.visible
    },
    toggleClass () {
      return this.visible ? 'left-content' : 'left-content close'
    }
  }
}
</script>
<style lang='less' scoped>

.left-content {
  width: 200px;
  height: calc(100vh - 4px);
  background-color: #fff;
  position:relative;
  transition: width 300ms ease;
  .toggle-btn{
    position:absolute;
    top:50%;
    transform:translateY(-50%);
    width:18px;
    height:160px;
    display:flex;
    flex-direction:column;
    align-items:center;
    justify-content:center;
    background-color:#fff;
    color:#666;
    cursor:pointer;
    transition:all .2s;
    z-index:10;
    -webkit-user-select:none;
    -moz-user-select:none;
    user-select:none;
    left:100%;
    border-radius:0 100px 100px 0;
    .hint{ display:none;}
    &:hover {
      background-color: #f86e04;
      color: #fff;
      .hint { display: block}
    }
  }

  .demo {
    width: 195px;
    height: 52px;
    background:  #f0f2f5 url(~@/assets/nav_level_3.png) no-repeat 50%;
    background-size: cover;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 15px;
    padding-right: 10px;
    box-sizing: border-box;
    margin-left: 5px;
    border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    z-index: 1;
    .tag{
      position: absolute;
      left: 0;
      top: 0;
      width: 80px;
      height: 16px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      z-index: 10;
      transform: rotate(-45deg);
      transform-origin: 42% 267%;
      font-size: 12px
    }
    .tag-fanli {
      background: #b91212;
    }
    .line-word {
      font-size:24px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .custom-collapse {
    border: 0;
    background-color:#fff;
    /deep/ .ant-collapse-item {
      border: 0;
    }
  }
}
.catalog-li {
  display:flex;
  flex-direction:column;
  width:100%;
  font-size:12px;
  .catalog-item {
    .title {
      padding: 0 4px;
      cursor:pointer;
      font-size:16px;
      display:flex;
      flex-direction:row;
      justify-items: flex-end;
      justify-content: space-between;
      .dynamic-icon {
        display:none;
      }
    }
    .title:hover {
      .dynamic-icon {
        display:inline-block;
      }
    }
    .title.selected { color: #ff7926;}
    .article-item {
      padding: 6px 4px;
      background-color:#f5f5f5;
      .selected { color: #ff7926;}
      .add-btn {
          border: 1px dashed #f86e04;
          border-radius: 2px;
          width: 110px;
          height: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ff7926;
          cursor: pointer;
      }
      .justify-content-space-between {
        .cus-edit-toolbar-icon {
          visibility:hidden;
        }
      }
      .justify-content-space-between:hover {
        .cus-edit-toolbar-icon {
          visibility:visible;
        }
      }
    }
  }
}
.add-category {
  margin-top:12px;
  margin-left:8px;
  display:flex;
  width: 60%;
  border: 1px dashed #f86e04;
  border-radius:2px;
  cursor:pointer;
  color: #ff7926;
  i {
    font-size: 14px;
  }
}
.left-content.close{
  width: 0;
  transition: width 300ms ease;
}
.operate {
  display:table;
}
.line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #f76d02;
  margin-right: 6px
}
.cus-edit-toolbar-icon {
  font-size: 28px;
  margin: 0 10px;
}
.cus-edit-toolbar-icon:hover {
  color: #f86e04;
  cursor: pointer;
}
</style>
