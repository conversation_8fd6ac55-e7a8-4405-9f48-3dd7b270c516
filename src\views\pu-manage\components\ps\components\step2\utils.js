import {
  PS_SELECTED_ITEM_TYPE_FU_LU,
  PS_SELECTED_ITEM_TYPE_JUAN_SHOU,
  PS_SELECTED_ITEM_TYPE_SHI_XI,
  PS_SELECTED_ITEM_TYPE_ZHENG_WEN
} from '@/store/modules/ps-action-type'

export function getUpdateStep2BookStructureIndex (step2SelectedItemText) {
  let index = -1
  switch (step2SelectedItemText) {
    case PS_SELECTED_ITEM_TYPE_JUAN_SHOU:
      index = 0
      break
    case PS_SELECTED_ITEM_TYPE_ZHENG_WEN:
      index = 1
      break
    case PS_SELECTED_ITEM_TYPE_SHI_XI:
      index = 2
      break
    case PS_SELECTED_ITEM_TYPE_FU_LU:
      index = 3
      break
    default:
      break
  }
  return index
}
