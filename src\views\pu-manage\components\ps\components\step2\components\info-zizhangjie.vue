<template>
  <div class="info-son-chapter flex">
    <div class="folder">
      <i class="cus-edit-toolbar-icon icon-edit-toolbardakai" />
    </div>
    <div class="flex-1">
      <div class="flex justify-content-space-between align-center">
        <div>
          <span class="f15">子章节</span>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-pointer-hover"/>
        </div>
        <div>
          <a-select style="width:160px" v-model="outputSetting" :dropdownMatchSelectWidth="false">
            <a-select-option :value="item.id" :key="item.id" v-for="item in chapterOutputSetting">{{ item.name }}</a-select-option>
          </a-select>
          <span class="ml8 c-pointer-hover"><i class="cus-edit-toolbar-icon icon-edit-toolbarjia mr8"/>谱文</span>
          <span class="ml8 c-pointer-hover"><i class="cus-edit-toolbar-icon icon-edit-toolbarjia mr8"/>谱系</span>
          <span class="ml8 c-pointer-hover"><i class="cus-edit-toolbar-icon icon-edit-toolbarjia mr8"/>源流</span>
        </div>
      </div>
    </div>
    <div class="remove">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover" />
    </div>
  </div>
</template>
<script>
import { chapterOutputSetting } from '@/constant/output-setting'

export default {
  name: 'InfoSonChapter',
  data () {
    return {
      outputSetting: '1',
      chapterOutputSetting
    }
  }
}
</script>
<style lang="less" scoped>
.info-son-chapter {
  position:relative;
  margin-left: 30px;
  height: 42px;
  align-items:center;
  border: 1px dashed #fff;
  &:before {
    content: "";
    position: absolute;
    height: 22px;
    width: 0;
    top: -2px;
    left: -8px;
    border-left: 1px solid #ccc;
  }
  &:after {
    content: "";
    position: absolute;
    height: 0;
    width: 8px;
    left:-8px;
    top: 20px;
    border-top: 1px solid #ccc;
  }
  &:hover {
    background: linear-gradient(135deg, #fff7f0, #fff);
    border-color: red;
    .remove {
      .cus-edit-toolbar-icon {
        visibility:visible;
      }
    }
  }
  .folder {
    width: 20px;
  }
  .remove {
    width: 50px;
    text-align:center;
    .cus-edit-toolbar-icon {
      visibility: hidden;
    }
  }
}
</style>
