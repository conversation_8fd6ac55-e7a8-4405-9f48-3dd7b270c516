<template>
  <div data-v-d2e510fe="" data-v-e10d99d4="" id="tree-box" class="tree-box">
    <div data-v-d2e510fe="" class="tree">
      <div data-v-d2e510fe="" class="tree-root">
        <div data-v-d2e510fe="" class="history">
          <div data-v-1f9e3c72="" data-v-d2e510fe="" class="root" id="clan-tree-root">
            <div data-v-1f9e3c72="" class="node expand border last-item">
              <div data-v-1f9e3c72="" class="border-left style-0"></div>
              <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                <div data-v-1f9e3c72="" class="before"><!----><!----><i
                  data-v-1f9e3c72=""
                  title="无编辑权限"
                  class="icon"></i><!----><!----></div>
                <div data-v-1f9e3c72="" class="switch h-icon-jianhao1"></div>
                <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                  <div data-v-1f9e3c72="" class="line style-0"><!----></div><span data-v-1f9e3c72="">[0世]</span>
                </div>
                <div data-v-1f9e3c72="" id="tree-input-638787051812367135" class="name selected"><i
                                                                                                   data-v-1f9e3c72=""
                                                                                                   title="Ctrl+点击可切换过世状态"
                                                                                                   class="icon h-icon-nanxing dead"></i>
                  <div data-v-1f9e3c72="" class="text"><button
                    data-v-1f9e3c72=""
                    id="name-638787051812367135"
                    class="btn-name"><!----><span data-v-1f9e3c72="">谭</span><span
                      data-v-1f9e3c72=""></span><!----></button></div><!---->
                  <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                </div><span data-v-1f9e3c72="">[始祖]</span><span data-v-1f9e3c72=""><!----></span>
                <div data-v-1f9e3c72="" class="name"><i data-v-1f9e3c72="" class="icon h-icon-nvxing2"></i>
                  <div data-v-1f9e3c72="" class="text more-wife">+添加配偶</div>
                </div>
              </div>
              <div data-v-1f9e3c72="" class="children">
                <div data-v-1f9e3c72="" class="" style="margin-left: 0px;">
                  <div data-v-1f9e3c72="" class="node expand border current">
                    <div data-v-1f9e3c72="" class="border-left style-2"></div>
                    <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                      <div data-v-1f9e3c72="" class="before"><!----><!----><i
                        data-v-1f9e3c72=""
                        title="无编辑权限"
                        class="icon"></i><!----><!----></div>
                      <div data-v-1f9e3c72="" class="switch h-icon-jianhao1"></div>
                      <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                        <div data-v-1f9e3c72="" class="line"><!----></div><span data-v-1f9e3c72="">[1世]</span>
                      </div>
                      <div data-v-1f9e3c72="" id="tree-input-638787051812367136" class="name"><i
                                                                                                data-v-1f9e3c72=""
                                                                                                title="Ctrl+点击可切换过世状态"
                                                                                                class="icon h-icon-nanxing"></i>
                        <div data-v-1f9e3c72="" class="text"><button
                          data-v-1f9e3c72=""
                          id="name-638787051812367136"
                          class="btn-name"><!----><span data-v-1f9e3c72="">谭</span><span
                            data-v-1f9e3c72="">世岐</span><!----></button></div><!---->
                        <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                      </div><span data-v-1f9e3c72="">[长子]</span><span data-v-1f9e3c72=""><!----></span><!---->
                    </div>
                    <div data-v-1f9e3c72="" class="children">
                      <div data-v-1f9e3c72="" class="" style="margin-left: -1px;">
                        <div data-v-1f9e3c72="" class="node expand border no-children">
                          <div data-v-1f9e3c72="" class="border-left"></div>
                          <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                            <div data-v-1f9e3c72="" class="before"><!----><!----><i
                              data-v-1f9e3c72=""
                              title="无编辑权限"
                              class="icon"></i><!----><!----></div>
                            <div data-v-1f9e3c72="" class="switch h-icon-jianhao1"></div>
                            <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                              <div data-v-1f9e3c72="" class="line"><!----></div><span data-v-1f9e3c72="">[2世]</span>
                            </div>
                            <div data-v-1f9e3c72="" id="tree-input-638787051812367137" class="name"><i
                                                                                                      data-v-1f9e3c72=""
                                                                                                      title="Ctrl+点击可切换过世状态"
                                                                                                      class="icon h-icon-nanxing"></i>
                              <div data-v-1f9e3c72="" class="text"><button
                                data-v-1f9e3c72=""
                                id="name-638787051812367137"
                                class="btn-name"><!----><span
                                  data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">邦</span><!----></button></div>
                              <!---->
                              <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                            </div><span data-v-1f9e3c72="">[长子]</span><span data-v-1f9e3c72=""><!----></span><!---->
                          </div><!----><!----><!---->
                        </div>
                        <div data-v-1f9e3c72="" class="node expand border last-item">
                          <div data-v-1f9e3c72="" class="border-left style-1"></div>
                          <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                            <div data-v-1f9e3c72="" class="before"><!----><!----><i
                              data-v-1f9e3c72=""
                              title="无编辑权限"
                              class="icon"></i><!----><!----></div>
                            <div data-v-1f9e3c72="" class="switch h-icon-jianhao1"></div>
                            <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                              <div data-v-1f9e3c72="" class="line"><!----></div><span data-v-1f9e3c72="">[2世]</span>
                            </div>
                            <div data-v-1f9e3c72="" id="tree-input-638787051812367138" class="name"><i
                                                                                                      data-v-1f9e3c72=""
                                                                                                      title="Ctrl+点击可切换过世状态"
                                                                                                      class="icon h-icon-nanxing"></i>
                              <div data-v-1f9e3c72="" class="text"><button
                                data-v-1f9e3c72=""
                                id="name-638787051812367138"
                                class="btn-name"><!----><span
                                  data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">国瑚</span><!----></button></div>
                              <!---->
                              <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                            </div><span data-v-1f9e3c72="">[次子]</span><span data-v-1f9e3c72=""><!----></span><!---->
                          </div>
                          <div data-v-1f9e3c72="" class="children">
                            <div data-v-1f9e3c72="" class="" style="margin-left: 0px;">
                              <div data-v-1f9e3c72="" class="node border">
                                <div data-v-1f9e3c72="" class="border-left"></div>
                                <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                                  <div data-v-1f9e3c72="" class="before"><!----><!----><i
                                    data-v-1f9e3c72=""
                                    title="无编辑权限"
                                    class="icon"></i><!----><!----></div>
                                  <div data-v-1f9e3c72="" class="switch h-icon-jiahao1"></div>
                                  <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                                    <div data-v-1f9e3c72="" class="line"><!----></div><span
                                      data-v-1f9e3c72="">[3世]</span>
                                  </div>
                                  <div data-v-1f9e3c72="" id="tree-input-638787051812367139" class="name"><i
                                                                                                            data-v-1f9e3c72=""
                                                                                                            title="Ctrl+点击可切换过世状态"
                                                                                                            class="icon h-icon-nanxing"></i>
                                    <div data-v-1f9e3c72="" class="text"><button
                                      data-v-1f9e3c72=""
                                      id="name-638787051812367139"
                                      class="btn-name"><!----><span
                                        data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">克龙</span><!----></button>
                                    </div><!---->
                                    <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                                  </div><span data-v-1f9e3c72="">[长子]</span><span
                                    data-v-1f9e3c72=""><!----></span><!---->
                                </div><!----><!----><!---->
                              </div>
                              <div data-v-1f9e3c72="" class="node border">
                                <div data-v-1f9e3c72="" class="border-left"></div>
                                <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                                  <div data-v-1f9e3c72="" class="before"><!----><!----><i
                                    data-v-1f9e3c72=""
                                    title="无编辑权限"
                                    class="icon"></i><!----><!----></div>
                                  <div data-v-1f9e3c72="" class="switch h-icon-jiahao1"></div>
                                  <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                                    <div data-v-1f9e3c72="" class="line"><!----></div><span
                                      data-v-1f9e3c72="">[3世]</span>
                                  </div>
                                  <div data-v-1f9e3c72="" id="tree-input-638787051812367141" class="name"><i
                                                                                                            data-v-1f9e3c72=""
                                                                                                            title="Ctrl+点击可切换过世状态"
                                                                                                            class="icon h-icon-nanxing"></i>
                                    <div data-v-1f9e3c72="" class="text"><button
                                      data-v-1f9e3c72=""
                                      id="name-638787051812367141"
                                      class="btn-name"><!----><span
                                        data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">克环</span><!----></button>
                                    </div><!---->
                                    <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                                  </div><span data-v-1f9e3c72="">[次子]</span><span
                                    data-v-1f9e3c72=""><!----></span><!---->
                                </div><!----><!----><!---->
                              </div>
                              <div data-v-1f9e3c72="" class="node border last-item">
                                <div data-v-1f9e3c72="" class="border-left style-1"></div>
                                <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                                  <div data-v-1f9e3c72="" class="before"><!----><!----><i
                                    data-v-1f9e3c72=""
                                    title="无编辑权限"
                                    class="icon"></i><!----><!----></div>
                                  <div data-v-1f9e3c72="" class="switch h-icon-jiahao1"></div>
                                  <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                                    <div data-v-1f9e3c72="" class="line"><!----></div><span
                                      data-v-1f9e3c72="">[3世]</span>
                                  </div>
                                  <div data-v-1f9e3c72="" id="tree-input-638787051812367145" class="name"><i
                                                                                                            data-v-1f9e3c72=""
                                                                                                            title="Ctrl+点击可切换过世状态"
                                                                                                            class="icon h-icon-nanxing"></i>
                                    <div data-v-1f9e3c72="" class="text"><button
                                      data-v-1f9e3c72=""
                                      id="name-638787051812367145"
                                      class="btn-name"><!----><span
                                        data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">克虎</span><!----></button>
                                    </div><!---->
                                    <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                                  </div><span data-v-1f9e3c72="">[三子]</span><span
                                    data-v-1f9e3c72=""><!----></span><!---->
                                </div><!----><!----><!---->
                              </div>
                            </div>
                          </div><!----><!---->
                        </div>
                      </div>
                    </div><!----><!---->
                  </div>
                  <div data-v-1f9e3c72="" class="node expand border current last-item">
                    <div data-v-1f9e3c72="" class="border-left style-1 style-2"></div>
                    <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                      <div data-v-1f9e3c72="" class="before"><!----><!----><i
                        data-v-1f9e3c72=""
                        title="无编辑权限"
                        class="icon"></i><!----><!----></div>
                      <div data-v-1f9e3c72="" class="switch h-icon-jianhao1"></div>
                      <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                        <div data-v-1f9e3c72="" class="line"><!----></div><span data-v-1f9e3c72="">[1世]</span>
                      </div>
                      <div data-v-1f9e3c72="" id="tree-input-638787051812367149" class="name"><i
                                                                                                data-v-1f9e3c72=""
                                                                                                title="Ctrl+点击可切换过世状态"
                                                                                                class="icon h-icon-nanxing"></i>
                        <div data-v-1f9e3c72="" class="text"><button
                          data-v-1f9e3c72=""
                          id="name-638787051812367149"
                          class="btn-name"><!----><span data-v-1f9e3c72="">谭</span><span
                            data-v-1f9e3c72="">本华</span><!----></button></div><!---->
                        <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                      </div><span data-v-1f9e3c72="">[次子]</span><span data-v-1f9e3c72=""><!----></span><!---->
                    </div>
                    <div data-v-1f9e3c72="" class="children">
                      <div data-v-1f9e3c72="" class="" style="margin-left: 0px;">
                        <div data-v-1f9e3c72="" class="node expand border last-item">
                          <div data-v-1f9e3c72="" class="border-left style-1"></div>
                          <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                            <div data-v-1f9e3c72="" class="before"><!----><!----><i
                              data-v-1f9e3c72=""
                              title="无编辑权限"
                              class="icon"></i><!----><!----></div>
                            <div data-v-1f9e3c72="" class="switch h-icon-jianhao1"></div>
                            <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                              <div data-v-1f9e3c72="" class="line"><!----></div><span data-v-1f9e3c72="">[2世]</span>
                            </div>
                            <div data-v-1f9e3c72="" id="tree-input-638787051812367150" class="name"><i
                                                                                                      data-v-1f9e3c72=""
                                                                                                      title="Ctrl+点击可切换过世状态"
                                                                                                      class="icon h-icon-nanxing"></i>
                              <div data-v-1f9e3c72="" class="text"><button
                                data-v-1f9e3c72=""
                                id="name-638787051812367150"
                                class="btn-name"><!----><span
                                  data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">大昌</span><!----></button></div>
                              <!---->
                              <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                            </div><span data-v-1f9e3c72="">[长子]</span><span data-v-1f9e3c72=""><!----></span><!---->
                          </div>
                          <div data-v-1f9e3c72="" class="children">
                            <div data-v-1f9e3c72="" class="" style="margin-left: 0px;">
                              <div data-v-1f9e3c72="" class="node border">
                                <div data-v-1f9e3c72="" class="border-left"></div>
                                <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                                  <div data-v-1f9e3c72="" class="before"><!----><!----><i
                                    data-v-1f9e3c72=""
                                    title="无编辑权限"
                                    class="icon"></i><!----><!----></div>
                                  <div data-v-1f9e3c72="" class="switch h-icon-jiahao1"></div>
                                  <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                                    <div data-v-1f9e3c72="" class="line"><!----></div><span
                                      data-v-1f9e3c72="">[3世]</span>
                                  </div>
                                  <div data-v-1f9e3c72="" id="tree-input-638787051812367151" class="name"><i
                                                                                                            data-v-1f9e3c72=""
                                                                                                            title="Ctrl+点击可切换过世状态"
                                                                                                            class="icon h-icon-nanxing"></i>
                                    <div data-v-1f9e3c72="" class="text"><button
                                      data-v-1f9e3c72=""
                                      id="name-638787051812367151"
                                      class="btn-name"><!----><span
                                        data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">景璋</span><!----></button>
                                    </div><!---->
                                    <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                                  </div><span data-v-1f9e3c72="">[长子]</span><span
                                    data-v-1f9e3c72=""><!----></span><!---->
                                </div><!----><!----><!---->
                              </div>
                              <div data-v-1f9e3c72="" class="node border no-children">
                                <div data-v-1f9e3c72="" class="border-left"></div>
                                <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                                  <div data-v-1f9e3c72="" class="before"><!----><!----><i
                                    data-v-1f9e3c72=""
                                    title="无编辑权限"
                                    class="icon"></i><!----><!----></div>
                                  <div data-v-1f9e3c72="" class="switch h-icon-jiahao1"></div>
                                  <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                                    <div data-v-1f9e3c72="" class="line"><!----></div><span
                                      data-v-1f9e3c72="">[3世]</span>
                                  </div>
                                  <div data-v-1f9e3c72="" id="tree-input-638787051812367156" class="name"><i
                                                                                                            data-v-1f9e3c72=""
                                                                                                            title="Ctrl+点击可切换过世状态"
                                                                                                            class="icon h-icon-nanxing"></i>
                                    <div data-v-1f9e3c72="" class="text"><button
                                      data-v-1f9e3c72=""
                                      id="name-638787051812367156"
                                      class="btn-name"><!----><span
                                        data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">景珍</span><!----></button>
                                    </div><!---->
                                    <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                                  </div><span data-v-1f9e3c72="">[次子]</span><span
                                    data-v-1f9e3c72=""><!----></span><!---->
                                </div><!----><!----><!---->
                              </div>
                              <div data-v-1f9e3c72="" class="node border last-item no-children">
                                <div data-v-1f9e3c72="" class="border-left style-1"></div>
                                <div data-v-1f9e3c72="" class="item" style="margin-left: 0px;">
                                  <div data-v-1f9e3c72="" class="before"><!----><!----><i
                                    data-v-1f9e3c72=""
                                    title="无编辑权限"
                                    class="icon"></i><!----><!----></div>
                                  <div data-v-1f9e3c72="" class="switch h-icon-jiahao1"></div>
                                  <div data-v-1f9e3c72="" class="padding" style="width: 89px;">
                                    <div data-v-1f9e3c72="" class="line"><!----></div><span
                                      data-v-1f9e3c72="">[3世]</span>
                                  </div>
                                  <div data-v-1f9e3c72="" id="tree-input-638787051812367157" class="name"><i
                                                                                                            data-v-1f9e3c72=""
                                                                                                            title="Ctrl+点击可切换过世状态"
                                                                                                            class="icon h-icon-nanxing"></i>
                                    <div data-v-1f9e3c72="" class="text"><button
                                      data-v-1f9e3c72=""
                                      id="name-638787051812367157"
                                      class="btn-name"><!----><span
                                        data-v-1f9e3c72="">谭</span><span data-v-1f9e3c72="">景瑚</span><!----></button>
                                    </div><!---->
                                    <div data-v-1f9e3c72="" class="name-top"><!----><!----><!----></div>
                                  </div><span data-v-1f9e3c72="">[三子]</span><span
                                    data-v-1f9e3c72=""><!----></span><!---->
                                </div><!----><!----><!---->
                              </div>
                            </div>
                          </div><!----><!---->
                        </div>
                      </div>
                    </div><!----><!---->
                  </div>
                </div>
              </div>
              <div data-v-1f9e3c72="" class="more-child" style="margin-left: 0px;"><i
                                                                                     data-v-1f9e3c72=""
                                                                                     class="icon h-icon-nanxing"></i>
                <div data-v-1f9e3c72="" class="text">添加孩子</div>
              </div>
              <div data-v-1f9e3c72="" class="more-child more-brother" style="margin-left: 0px;"><i
                                                                                                  data-v-1f9e3c72=""
                                                                                                  class="icon h-icon-nanxing"></i>
                <div data-v-1f9e3c72="" class="text">添加兄妹</div>
              </div>
            </div>
          </div>
        </div>
      </div><!---->
      <div
        data-v-7c780bba=""
        data-v-d2e510fe=""
        class="member-tree-contextmenu"
        style="left: 0px; top: 0px; display: none;">
        <div data-v-7c780bba="" class="item">
          <div data-v-7c780bba="">复制人物</div>
        </div>
        <div data-v-7c780bba="" class="item disabled">
          <div data-v-7c780bba="">粘贴人物</div>
        </div>
      </div>
    </div>
  </div></template>

<style lang='less' scoped>
.tree-box[data-v-d2e510fe] {
  height: 100vh;
  width: 100vw;
  border-left: 10px solid #f7f8fa;
  background-color: #fff;
  overflow: hidden;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative
}

.tree-box .history[data-v-d2e510fe] {
  top: 32px;
  flex-shrink: 0;
  height: 30px;
  width: 100%;
  box-sizing: border-box
}

.tree-box .history .history-inner[data-v-d2e510fe] {
  position: absolute;
  text-align: right;
  right: 15px;
  top: 40px
}

.tree-box .history .history-inner span[data-v-d2e510fe] {
  color: #409eff;
  cursor: pointer
}

.tree-box .history .history-inner label[data-v-d2e510fe] {
  color: #e6a23c;
  cursor: pointer
}

.tree-box .tree[data-v-d2e510fe] {
  overflow: scroll;
  width: 100%;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative
}

.tree-box .tree .tree-root[data-v-d2e510fe] {
  width: -moz-fit-content;
  width: fit-content
}

.tree-box .input[data-v-d2e510fe] {
  flex-shrink: 0
}

.generations[data-v-d2e510fe] {
  background-color: #f9f5f1;
  width: 100%;
  height: 32px;
  flex-shrink: 0;
  overflow: hidden;
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  z-index: 100;
  margin-bottom: 10px
}

.generations .item[data-v-d2e510fe] {
  width: 99px;
  flex-shrink: 0;
  text-align: center;
  line-height: 32px;
  border-left: 1px dashed #eaceac;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  color: #99908a
}

.generations .item[data-v-d2e510fe]:first-child {
  border-left-color: transparent
}

.generations .item.current[data-v-d2e510fe] {
  background-color: #c7a27c;
  color: #fff;
  border-radius: 2px;
  position: relative;
  border-left-color: transparent
}

.generations .item.current[data-v-d2e510fe]:after {
  content: "";
  display: block;
  position: absolute;
  right: -1px;
  top: 0;
  width: 1px;
  height: 100%;
  background-color: #f9f5f1
}

[data-v-74fc0cf0] .el-dialog__body {
  padding: 20px
}

.info-r[data-v-74fc0cf0] {
  position: relative;
  width: 825px;
  height: 100%;
  margin-left: 5px;
  transition: all .5s ease;
  overflow: hidden
}

.info-r.simple[data-v-74fc0cf0] {
  width: 240px
}

.info-r.full[data-v-74fc0cf0] {
  width: 825px
}

.info-r.mapping[data-v-74fc0cf0] {
  width: auto!important
}

.info-r.close[data-v-74fc0cf0] {
  width: 0!important
}

.info-r .info-r-inner[data-v-74fc0cf0] {
  position: absolute;
  left: 0;
  top: 0;
  width: 825px;
  height: 100%
}

.info-r .info-r-inner.simple[data-v-74fc0cf0] {
  overflow-y: scroll;
  width: 240px;
  background-color: #fff
}

.info-r .tabs[data-v-74fc0cf0] {
  display: flex;
  height: 39px;
  margin-bottom: 2px
}

.info-r .tabs .item[data-v-74fc0cf0] {
  margin-right: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 87px;
  background-color: #f2f3f5;
  cursor: pointer
}

.info-r .tabs .item.on[data-v-74fc0cf0] {
  background-color: #fff
}

.info-r .tabs .blank[data-v-74fc0cf0] {
  flex-grow: 1
}

.info-r .tabs .icon[data-v-74fc0cf0] {
  line-height: 39px;
  cursor: pointer;
  margin-right: 20px
}

.info-r .info-content[data-v-74fc0cf0] {
  background-color: #fff;
  height: calc(100% - 102px);
  overflow-x: hidden
}

.message-clan-members {
  top: 65px!important;
  bottom: auto!important;
  left: auto!important;
  right: 80px!important;
  transform: translate(0);
  min-width: 120px
}
</style>
