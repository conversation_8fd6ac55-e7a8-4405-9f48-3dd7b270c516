<template>
  <div class="h-right">
    <div class="title">
      <div class="name">莲山谱牒范例</div>
      <i class="c-pointer cus-edit-toolbar-icon icon-edit-toolbarcuo" @click="handleClose" />
    </div>
    <div class="format">
      <div class="name">预览版式：欧式</div>
      <div class="direction">预览开向：右开</div>
    </div>
    <div class="desc">
      <div class="desc-title">预览说明：</div>
      <div class="text">
        <div >谱系预览仅支持使用版式[欧式]预览</div>
        <div >需阅读完整谱书请使用[谱书]制作生成，支持<b data-v-22dec738="">欧式、苏式、牒记式、现代式、宝塔式。</b></div>
      </div>
      <div>
        <a class="button el-link el-link--default">
          <span class="el-link--inner">制作谱书</span>
        </a>
      </div>
    </div>
    <div class="menu">
      <div class="item">
        <div class="cell menu-title">
          <i class="icon h-icon-tubiao_xu c-ff7926" />
          <span class="txt">大纲目录</span>
        </div>
        <div class="submenu">
          <div class="cell">
            <i class="icon h-icon-puwenneirong c-ff7926" />
            <span class="txt">检索表</span>
          </div>
          <div class="cell">
            <i class="icon h-icon-puxi c-ff7926" />
            <span class="txt">世系图</span>
          </div>
          <div class="cell">
            <i class="icon h-icon-puxi c-ff7926" />
            <span class="txt">世系表</span>
          </div>
        </div>
      </div>
    </div>
    <div class="back">
      <a class="button el-link el-link--default">
        <span class="el-link--inner">
          <i class="h-icon-chexiao1" />返回谱系</span>
      </a>
    </div>
    <div class="nav">
      <div class="btn"><i class="el-icon-arrow-left" />下页</div>
      <div class="page">1/17</div>
      <div class="text">前往</div>
      <div class="number"><input type="number" class="input" /></div>
      <div class="text to">跳转</div>
      <div class="btn">上页<i class="el-icon-arrow-right" /></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    close: {
      type: Function,
      default: () => {}
    }
  },
  methods: {
    handleClose () {
      this.close?.()
      // this.$emit('custom-event', '这是一个自定义事件')
    }
  }
}
</script>
<style lang='less' scoped>
.h-right {
  flex-shrink: 0;
  width: 290px;
  height: 100vh;
  margin-right: 5px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .title {
    display: flex;
    border-bottom: 1px solid #eee;
    padding: 10px 5px 10px 10px;
    .name {
      flex-grow: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 16px;
      color: #000
    }
    .icon{
      cursor: pointer;
      flex-shrink: 0;
      font-size: 18px;
      padding: 4px
    }
  }
  .format {
    padding: 10px;
    border-bottom: 1px solid #eee;
    .name {
      font-size: 16px;
      color: #000;
      padding-bottom: 5px
    }
    .direction {
      font-size: 14px;
      color: #999
    }
  }
  .desc {
    color: #999;
    padding: 10px;
    text-align: justify;
    border-bottom: 1px solid #eee;

    .desc-title{
      color: #000;
      font-size: 16px;
      padding-bottom: 5px
    }

    .text {
      line-height: 1.5;
      padding-bottom: 10px;

      b {
        font-weight: 700;
        color: #333
      }
    }
  }
  .button {
    color: #ff7926;
    line-height: 1;
    padding: 4px 0;
    border-radius: 20px;
    border: 1px solid #ff7926;
    margin: 0 auto;
    display: block;
    width: 86px;
    text-align: center
  }

  .menu{
    overflow-x: hidden;
    flex-grow: 1;
    line-height: 30px;

    item:first-child {
      padding-top: 10px
    }
    .menu-title {
      font-size: 16px;
      color: #000;

      .icon {
        font-size: 20px
      }
    }
    .icon {
      font-size: 18px
    }
    .icon.h-icon-edit {
      cursor: pointer
    }
    .cell {
      padding: 0 10px;
      display: flex;

      .txt {
        flex: 1;
        margin: 0 6px;
        cursor: pointer
      }
    }
    .submenu .cell {
      padding-left: 30px;
      color: #666
    }
  }

  .back{
    padding: 10px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee
  }
  .nav {
    display: flex;
    align-items: center;
    line-height: 1;
    padding: 10px 10px;
    font-size: 14px;

    .btn {
      border: 1px solid #eee;
      padding: 4px;
      flex-shrink: 0;
      cursor: pointer;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none
    }

    .page{
      color: #ff7926;
      flex-grow: 1;
      padding-left: 4px
    }

    .text{
      flex-shrink: 0;
      padding-right: 3px
    }

    .number {
      flex-shrink: 0;
      width: 34px;

      .input{
        width: 100%;
        border: 1px solid #eee;
        height: 26px;
        line-height: 26px;
        box-sizing: border-box;
        text-align: center
      }
    }

    .to{
      color: #ff7926;
      cursor: pointer;
      padding: 0 8px 0 3px
    }
  }
}
</style>
