<template>
  <div class="item" :class="selected?'on':''">
    <div class="pic"><img src="../../../../../assets/pushu_tili_img.png" alt=""></div>
    <div class="info">
      <div class="book-type mb6">
        <span class="binding mr10">古籍线装</span>
        <span>欧式</span>
        <span>（标准版）</span>
        <span>+六世</span>
        <span>+筒子页</span>
        <span class="config ml10 c-pointer-hover">[查看设置]</span>
      </div>
      <div class="book-size  mb2">
        <span>谱书尺寸[宽*高]：18.5*29（厘米）</span>
        <span class="ml24">页面数：240</span>
      </div>
      <div class="book-summary mb2">
        <span>世系图人数：1493</span>
        <span class="ml20">世系表人数：1493</span>
        <span class="ml20">字数：10997</span>
      </div>
      <div class="book-time">2025-05-05 11:32:01</div>
    </div>
    <div class="opt">
      <div class="read mb20 c-pointer-hover"><i class="cus-edit-toolbar-icon icon-edit-toolbaryanjing"/>阅读</div>
      <div class="download c-pointer-hover"><i class="cus-edit-toolbar-icon icon-edit-toolbarxiazai"/>下载/打印校对稿</div>
    </div>
    <div class="print">
      <a-button type="primary"><i class="cus-edit-toolbar-icon icon-edit-toolbarPrint"/>打印谱书</a-button>
    </div>
    <div class="publish">
      发布谱书<a-switch class="ml4"/>
    </div>
    <div class="delete">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover"/>
    </div>
  </div>
</template>
<script>
export default {
  name: 'PsListItem',
  props: {
    selected: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="less" scoped>
.item {
  flex:1;
  display:flex;
  padding:20px 0;
  align-items:center;
  &.on {
    box-shadow: inset 0 0 0 2px #f86e04
  }
  &:hover {
    .delete {
      i{
        visibility:visible;
        font-size: 20px;
      }
    }
  }
  .pic{
    width:100px;
  }
  .info {
    flex:1;
    display:flex;
    flex-direction:column;
    justify-content:left;
    text-align:left;
    .book-type {
      font-size: 18px;
      color: #333;
      .binding{
        display: inline-flex;
        font-size: 12px;
        box-shadow: inset 0 0 0 1px #0053b5;
        border-radius: 2px;
        color: #0053b5;
        padding: 2px 5px;
      }
      .config {
        font-size: 16px;
        color: #999;
        cursor: pointer;
      }
    }
    .book-size {
    }
    .book-summary {
    }
    .book-time {}
  }
  .opt {
    width: 120px;
    display:flex;
    flex-direction:column;
    text-align:left;
    font-size:14px;
    i{
      color: #1890ff;
    }
    .read{
    }
    .download{}
  }
  .print{
    width: 120px;
    button{
      border-radius:20px;
    }
  }
  .publish {
    width: 120px;
  }
  .delete {
    width: 120px;
    i{ visibility:hidden}
  }
}
</style>
