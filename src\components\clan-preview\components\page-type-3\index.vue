<template>
  <div class="page preview-page p2 even" style="">
    <div class="content content-1">
      <div class="head">
        <div class="column name">
          <clan-mark-img type="down" class-str="mark mark-top" />
          <div v-for="(item, index) in getValue('head.name')" :key="index">{{ item }}</div>
          <clan-mark-img type="up" class-str="mark mark-top" />
        </div>
        <div class="column volume">
          <span v-for="(item, index) in getValue('head.volume')" :key="index">{{ item }}</span>
        </div>
        <div class="column title">
          <div>
            <div v-for="(item, index) in getValue('head.title')" :key="index">{{ item }}</div>
          </div>
          <div>
            <div v-for="(item, index) in getValue('head.other')" :key="index">{{ item }}</div>
          </div>
        </div>
        <div class="page-index">{{ getValue( 'pageIndex', false) + 1 }}</div>
      </div>
      <div>
        <div class="body detail">
          <div class="column" v-for="(column, idx) in getValue('columns', false)" :key="idx">
            <template v-for="(item, indexItem) in column.itemData">
              <detail-generation v-if="item.type === 1" :txt="item.text" :x="item.x" :key="indexItem"/>
              <detail-item v-else :item="item" :key="indexItem"/>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { get } from 'lodash'
import ClanMarkImg from '@/components/clan-mark'
import DetailGeneration from '@/components/clan-preview/components/common/detail-column-detail-generation'
import DetailItem from '@/components/clan-preview/components/common/detail-column-detail-item'
export default {
  name: 'ClanPreviewDetailPage',
  components: { ClanMarkImg, DetailGeneration, DetailItem },
  props: {
    page: {
      type: Object,
      default: () => {}
    },
    name: {
      type: String,
      default: ''
    },
    indexList: {
      type: Array,
      default: () => []
    },
    pageList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    getValue (field, transform = true) {
      console.log(this.page, field)
      console.log(get(this.page, field, ''))
      return transform ? get(this.page, field, '').split('') : get(this.page, field, '')
    }
  }
}
</script>
<style lang='less' scoped>
.page {
  margin: 0 auto;
  width: 448px;
  height: 706px;
  background-color: #fff;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.preview-page {
  position: relative;
  box-sizing: border-box;
  background-size: cover;
  transform-style: preserve-3d;
  width: 448px;
  height: 666px;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 20px;
  font-family: makefont, 宋体;
  page-break-after: avoid;
  .content {
    height: 622px;
    border: 2px solid #333;
    display: flex;
    //margin: 40px 40px 0;
    overflow: hidden;

    .head {
      flex-shrink: 0;
      width: 42px;
      box-sizing: border-box;
      padding: 2px;
      height: 100%;
      display: flex;
      flex-direction: column;
      border-right: 2px solid #333;
      font-size: 18px;
      .column {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 3px;
        padding: 10px 0;
      }
      .name {
        font-size: 20px;
        padding: 0;
        flex-shrink: 0;
        overflow: hidden;
        .mark {
          display: block;
          width: 100%;
          height: auto;
        }
        .mark-top {
          transform: translateY(-1px);
        }
        .mark-bottom {
          transform: translateY(2px);
        }
      }
      .volume {
        height: 80px;
        justify-content: center;
        flex-shrink: 0;
      }
      .title {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
      }
    }
    .body {
      flex-grow: 1;
      width: 408px;
      height: 100%;
      box-sizing: border-box;
    }
    .body.detail {
      .column {
        position: relative;
        height: 20%;
        border-top: 1px solid #333;
        box-sizing: border-box;
      }
    }
  }
  .page-index {
    position: absolute;
    width: 42px;
    text-align: center;
    left: 0px;
    bottom: 14px;
    flex-shrink: 0;
  }
}
.even:before {
  content: "";
  display: block;
  position: absolute;
  right: 0;
  width: 0;
  height: 100%;
  box-shadow: 0 0 3px 1px #ddd
}
.odd:before {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  width: 0;
  height: 100%;
  box-shadow: 0 0 3px 1px #ddd
}
</style>
