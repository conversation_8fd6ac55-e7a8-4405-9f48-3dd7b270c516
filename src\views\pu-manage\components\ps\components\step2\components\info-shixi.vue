<template>
  <div class="info-shixi flex">
    <div class="content-1 flex">
      <div class="mt8">
        <i class="cus-edit-toolbar-icon icon-edit-toolbaryuandian"/>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarwenzhang" />
      </div>
      <div class="flex-1 mt8 content">
        <div class="item">
          <span>世系标题：世系标题</span>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-pointer-hover" @click="showEditTitle=true"/>
        </div>
        <div class="item">
          <span>支目标题：支目标题</span>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-pointer-hover" @click="showZhuMuTitle=true"/>
        </div>
        <div class="item">
          <span>起始族员：[谭一][3世]克环</span>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-pointer-hover"/>
        </div>
        <div class="item">
          <span>结束人物：[谭一][5世]万佐</span>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-pointer-hover"/>
        </div>
      </div>
      <div class="more-options">
        <a-checkbox class="ml8">检索表</a-checkbox>
        <a-checkbox>世系图</a-checkbox>
        <a-checkbox>世系表</a-checkbox>
        <a-checkbox>追溯先祖</a-checkbox>
        <div>
          <button>更多选项</button>
        </div>
      </div>
      <div class="remove mt8">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover" />
      </div>
    </div>
    <EditTitle v-if="showEditTitle" @close="showEditTitle=false"/>
    <EditZhiMuTitle v-if="showZhuMuTitle" @close="showZhuMuTitle=false"/>
  </div>
</template>
<script>
import EditTitle from './edit-title'
import EditZhiMuTitle from './edit-zhimu-title'
export default {
  name: 'InfoShiXi',
  components: { EditZhiMuTitle, EditTitle },
  data () {
    return {
      showEditTitle: false,
      showZhuMuTitle: false
    }
  }
}
</script>
<style lang='less' scoped>
.info-shixi {
  padding-left: 30px;
  .content-1 {
    position:relative;
    width: 100%;
    border: 1px dashed #ccc;
    &:hover {
      background: linear-gradient(135deg, #fff7f0, #fff);
      border-color: red;
      .remove {
        .cus-edit-toolbar-icon {
          visibility:visible;
        }
      }
    }
    &:before {
      content: '';
      position:absolute;
      height: 100%;
      top: 0;
      left: -8px;
      border-left: 1px solid #ccc;
    }
    &:after {
      content: "";
      position: absolute;
      height: 0;
      width: 8px;
      left: -8px;
      top: 20px;
      border-top: 1px solid #ccc;
    }
    .content {
      display:flex;
      flex-wrap:wrap;
      .item {
        min-width: 400px;
        height:40px;
      }
    }
    .more-options {
      border-left: 1px solid #ccc;
      width: 180px;
    }
    .remove {
      width: 50px;
      text-align:center;
      .cus-edit-toolbar-icon {
        visibility: hidden;
      }
    }
  }
}
</style>
