<template>
  <div class="item" :class="selected ? 'active':''" @click="handleClick">
    <div class="head">
      <div class="title">
        <span class="f20">{{ title }}</span>
        <span class="f14"> ({{ subTitle }})</span>
      </div>
      <div class="toggle-btn" @click="toggle">
        <span class="flex align-center c-pointer-hover" v-show="!isCollapse">收起<i class="cus-edit-toolbar-icon icon-edit-toolbarfangxiangxiangshang-shuangjiantou" style="font-size: 18px"/></span>
        <span class="flex align-center c-pointer-hover" v-show="isCollapse">展开<i class="cus-edit-toolbar-icon icon-edit-toolbarxiangxiashuangjiantou" style="font-size: 18px"/></span>
      </div>
    </div>
    <div class="content" :class="isCollapse?'collapse':'open'">
      <slot v-show="isCollapse"></slot>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  name: 'Step3EditContentItem',
  data () {
    return {
      isCollapse: false
    }
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    subTitle: {
      type: String,
      default: ''
    },
    selected: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    ...mapActions(['UpdateStep2SelectedItemText']),
    handleClick () {
      this.UpdateStep2SelectedItemText(this.title)
    },
    toggle () {
      this.isCollapse = !this.isCollapse
    }
  }
}
</script>
<style  lang="less" scoped>
.item {
  width: 100%;
  padding-top: 10px;
  background-color: #f7f8fa;
  &.active {
    .title {
      color: #f86e04 !important;
    }
  }
  .head {
    position:relative;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    padding: 0 15px;
    background-color: #e5e5e5;
    border-bottom: 4px solid #e5e5e5;
    box-sizing: border-box;
    z-index: 20;
    .toggle-btn {
      position: absolute;
      right: 0;
      top: 0;
      width: 70px;
      height: 100%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-color: #f7f8fa;
      color: #666;
      &:before{
        content: "";
        position: absolute;
        right: 100%;
        top: 0;
        border-top: 0 solid transparent;
        border-bottom: 44px solid transparent;
        border-right: 44px solid #f7f8fa;
      }
    }
  }
  .content {
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-top: 0;
    padding-top:12px;
    &.collapse {
      height: 0;
      overflow: hidden;
    }
    &.open {
      height: unset;
    }
  }
}
</style>
