<template>
  <a-modal
    class="add-group"
    v-model="visible"
    title="分组管理"
    :confirmLoading="submitting"
    @ok="handleOk"
    @cancel="$emit('close-modal')"
  >
    <a-spin :spinning="loading">
      <div class="flex opt align-center">
        <div class="flex-1 align-center">
          <i class="cus-edit-toolbar-icon icon-edit-toolbarjia c-pointer-hover" @click="handleAdd"/>
          <span class="c-pointer-hover" @click="handleAdd">新增分组</span>
        </div>
        <div class="flex">
          <div class="c-pointer-hover" @click="handleMove('up')">
            <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou c-pointer-hover" />
            <span class="c-pointer-hover">上移</span>
          </div>
          <div class="c-pointer-hover" @click="handleMove('down')">
            <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou1 c-pointer-hover" />
            <span class="c-pointer-hover">下移</span>
          </div>
        </div>
      </div>
      <div class="content mt12">
        <div class="item" :class="item.id === selectedId ? 'selected':''" v-for="item in list" :key="item.id" @click="selectedId=item.id">
          <a-input type="text" v-if="item.editing" v-model.trim="item.group_name" placeholder="请输入" :maxLength="20" />
          <span class="flex-1" v-else>{{ item.group_name }}</span>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarduigou c-pointer-hover ml12 f28" v-if="item.editing" @click="handleSaveEdit(item)" />
          <i class="cus-edit-toolbar-icon icon-edit-toolbarcuo c-pointer-hover ml12 f28" v-if="item.editing" @click="handleSaveEdit(item)" />
          <i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-pointer-hover" v-if="!item.editing" @click="handleEdit(item)" />
          <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover delete" v-if="!item.editing" @click="handleDelete(item.id)" />
        </div>
        <div class="item input-item" v-if="isEdit">
          <a-input type="text" v-model.trim="groupName" placeholder="请输入" :maxLength="20"/>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarduigou c-pointer-hover ml12 f28" @click="save" />
          <i class="cus-edit-toolbar-icon icon-edit-toolbarcuo c-pointer-hover ml12 f28" @click="handleCancel" />
        </div>
        <div class="empty" v-if="list.length === 0 && !isEdit"> <a-empty /></div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import cloneDeep from 'lodash.clonedeep'

export default {
  name: 'AddGroup',
  data () {
    return {
      visible: true,
      groupName: '',
      isEdit: false,
      loading: false,
      submitting: false,
      selectedId: -999,
      list: []
    }
  },
  computed: {
    ...mapState({
      groups: state => state.family.groups,
      hasLoadedGroup: state => state.family.hasLoadedGroup
    })
  },
  watch: {
    groups (val, oldVal) {
      this.list = cloneDeep(val).sort((a, b) => a.sort_order - b.sort_order)
    }
  },
  async mounted () {
    this.loading = true
    // 只有在未加载过数据时才调用API，避免重复请求
    if (!this.hasLoadedGroup) {
      await this.GroupClanList()
    }
    this.loading = false
  },
  methods: {
    ...mapActions(['SaveGroup', 'GroupClanList']),
    check () {
      const editList = this.list.filter(item => item.editing)
      if (editList.length > 0) {
        this.$message.error('请先保存正在编辑信息')
        return false
      }
      if (this.isEdit) {
        this.$message.error('请先保存分组')
        return false
      }
      return true
    },
    handleAdd () {
      if (this.check()) {
        this.isEdit = true
      }
    },
    handleOk () {
      if (this.list.length > 0) {
        if (this.submitting) {
          this.$message.success('处理中请稍后')
          return
        }
        this.submitting = true
        this.SaveGroup({ list: this.list }).then(json => {
          this.submitting = false
          if (json.code === 0) {
            this.GroupClanList()
            this.$message.success('保存成功')
            this.visible = false
            this.$emit('close-modal')
          } else {
            this.$message.success(json.message)
          }
        })
      } else {
        this.$message.error('请录入分组')
      }
    },
    save () {
      if (this.groupName) {
        const result = this.list.filter(item => item.group_name === this.groupName)
        if (result.length > 0) {
          this.$message.error('分组名称重复')
          return
        }
        this.isEdit = false
        const orderList = this.list.map(item => item.sort_order) || []
        // const idList = this.list.map(item => item.id)
        const order = orderList.length > 0 ? Math.max(...orderList) : 0
        // const idExist = Math.max(...idList) || 1
        // const id = orderList.length > 0 ? idExist + 1 : 1
        const name = this.groupName
        const maxOrder = order + 1
        this.list.push({
          id: 0,
          group_name: name,
          sort_order: maxOrder,
          status: 1
        })
        this.groupName = ''
      } else {
        this.$message.error('分组名称不能为空')
      }
    },
    handleCancel () {
      this.isEdit = false
      this.groupName = ''
    },
    handleSaveEdit (item) {
      item.editing = false
      this.list = cloneDeep(this.list)
    },
    handleEdit (item) {
      if (this.check()) {
        item.editing = true
        this.list = cloneDeep(this.list)
      }
    },
    handleDelete (id) {
      const newList = this.list.filter(item => item.id !== id)
      this.list = cloneDeep(newList)
    },
    handleMove (type) {
      const index = this.list.findIndex(item => item.id === this.selectedId)
      const list = this.list
       if (type === 'up') {
         if (index > 0) {
           [list[index - 1], list[index]] = [list[index], list[index - 1]]
         }
       } else {
         if (index < list.length - 1) {
           [list[index + 1], list[index]] = [list[index], list[index + 1]]
         }
       }
       list.forEach((t, index) => {
         t.sort_order = index + 1
       })
       this.list = cloneDeep(list)
    }
  }
}
</script>

<style scoped lang='less'>
.add-group {
  .opt {
    font-size: 15px;
  }
  .content {
     display:flex;
     flex-direction:column;
     width:100%;
     border:1px solid #e5e5e5;
     max-height: 200px;
     overflow-y: auto;
    .item {
      flex-shrink: 0;
      width:100%;
      height: 50px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      box-sizing: border-box;
      border-bottom: 1px dashed #e5e5e5;
      cursor: pointer;
      input {
        height: 42px;
        width: 200px;
      }
      .delete{
        visibility:hidden;
      }
    }
    .item.selected {
      background-color: #f5f5f5;
      .delete{
        visibility:visible;
      }
    }
    .item:hover{
      background-color: #f5f5f5;
      .delete{
        visibility:visible;
      }
    }
    .item:last-child {
      border-bottom: none;
    }
    .input-item {
      position:relative;
      width:100%;
      display:flex;

      .delete {
        position:absolute;
        right: 12px;
      }
    }
  }
}
</style>
