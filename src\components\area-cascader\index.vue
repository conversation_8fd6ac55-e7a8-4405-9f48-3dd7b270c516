<template>
  <a-cascader
    :field-names="{ label: 'name', value: 'name', children: 'child' }"
    :options="options"
    :value="value"
    placeholder="请选择"
    @change="onChange"
  />
</template>
<script>
import cityData from '@/assets/city.json'
export default {
  data () {
    return {
      options: cityData,
      value: this.initValue || []
    }
  },
  watch: {
    initValue (val = []) {
      this.value = val
    }
  },
  props: {
    initValue: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    onChange (value, selectedOptions) {
      console.log(value, selectedOptions)
      this.value = value
      this.$emit('change', value)
    }
  }
}
</script>
<style scoped lang='less'>

</style>
