/**
 * 数字转中文
 * @param num
 * @returns {string|*}
 */
export const numberToChinese = function (num) {
  const chineseDigits = {
    0: '零',
    1: '一',
    2: '二',
    3: '三',
    4: '四',
    5: '五',
    6: '六',
    7: '七',
    8: '八',
    9: '九',
    10: '十'
  }
  const chineseUnits = ['', '十', '百']
  let result = ''
  if (num === 0) {
    return chineseDigits[0]
  }
  if (num < 10) {
    return chineseDigits[num]
  }
  const digits = num.toString().split('').reverse()
  for (let i = 0; i < digits.length; i++) {
    const digit = parseInt(digits[i])
    if (digit === 0) {
      result = chineseDigits[digit] + result
      continue
    }
    if (i === 1 && digit === 1) {
      result = chineseUnits[i] + result
    } else {
      result = chineseDigits[digit] + chineseUnits[i] + result
    }
  }
  return result.replace(/零(?=[^百]*$)/g, '')
}
