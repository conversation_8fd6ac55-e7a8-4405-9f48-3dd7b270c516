<template>
  <div class="label-item-wrap" v-bind="$attrs">
    <div class="item-form">
      <div class="label" :style="{ width: width + 'px' }">{{ title }}</div>
      <div class="content"><slot></slot></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'LabelItem',
  props: {
    title: {
      type: String,
      default: ''
    },
    width: {
      type: [Number, String],
      default: 30
    }
  }
}
</script>
<style lang='less' scoped>
.label-item-wrap{
  .item-form {
    display: flex;
    align-items:center;
    align-content:center;
    border: 1px solid #e0e0e0;
    border-radius:2px;
    .label {
      height: 32px;
      color: #666;
      text-align:center;
      line-height: 32px;
      background-color: #f5f5f5;
      min-width: 40px;
      padding: 0 4px;
    }
    .content {
      flex: 1;
    }
  }
  /deep/ .ant-select-selection{
    border: 0;
  }
}
</style>
