<template>
  <div class="top-box">
    <div class="top-box__step">
      <div class="arrows">
        <div class="arrows-item"><img src="../../../../assets/step.gif" alt="" class="img"></div>
        <div class="arrows-item"><img src="../../../../assets/step.gif" alt="" class="img"></div>
        <div class="arrows-item"><img src="../../../../assets/step.gif" alt="" class="img"></div>
      </div>
      <div class="item">
        <div class="pic">
          <div style="width: 108px; height: 140px;">
            <div :title="currentSelectedBook?.plan_name" class="cover-container" style="transform: scale(1.08);">
              <img src="../../../../assets/cover-list2.png" alt="" class="cover">
              <div class="title">
                <div class="title2">
                  <div class="title3">
                    <div class="title4 title4-5" style="transform: scale(0.63);">{{ currentSelectedBook?.plan_name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <a class="step el-link el-link--info"><span class="el-link--inner"> 第一步 </span></a>
        <div class="btn-v">
          <a-button @click="$emit('step', 1)">选择封面</a-button>
        </div>
      </div>
      <div class="item">
        <div class="pic">
          <div style="width: 108px; height: 140px;">
            <div class="cover-container" style="transform: scale(1.08);">
              <img src="../../../../assets/book2.png" alt="" class="cover">
            </div>
          </div>
        </div>
        <a class="step el-link el-link--info"><span class="el-link--inner"> 第二步 </span></a>
        <div class="btn-v">
          <a-button @click="$emit('step', 2)">编辑内容</a-button>
        </div>
      </div>
      <div class="item">
        <div class="pic">
          <div style="width: 108px; height: 140px;">
            <div class="cover-container" style="transform: scale(1.08);">
              <img src="../../../../assets/book3.png" alt="" class="cover">
            </div>
          </div>
        </div>
        <a class="step el-link el-link--info"><span class="el-link--inner"> 第三步 </span></a>
        <div class="btn-v">
          <a-button @click="$emit('step', 3)">设置版式</a-button>
        </div>
      </div>
      <div class="item">
        <div class="pic">
          <div style="width: 108px; height: 140px;">
            <div class="cover-container" style="transform: scale(1.08);">
              <img src="../../../../assets/book4.png" alt="" class="cover">
            </div>
          </div>
        </div>
        <a class="step el-link el-link--info"><span class="el-link--inner"> 第四步 </span></a>
        <div class="btn-v">
          <a-button type="primary" @click="$emit('step', 4)">制作谱书</a-button>
        </div>
      </div>
    </div>
    <div class="basic-info">
      <div class="title">谱书信息</div>
      <div class="info">
        <div class="row">
          <span class="label">页眉名称：</span>
          <span class="content">{{ currentSelectedBook?.header }}</span>
        </div>
        <div class="row">
          <span class="label">卷号：</span>
          <span class="content">{{ currentSelectedBook?.volume }}</span>
        </div>
        <div class="row">
          <span class="label">堂号：</span>
          <span class="content">{{ currentSelectedBook?.hall }}</span>
        </div>
        <div class="row">
          <span class="label">编者：</span>
          <span class="content">{{ currentSelectedBook?.editor }}</span>
        </div>
        <div class="row">
          <span class="label">编修日期：</span>
          <span class="content">{{ currentSelectedBook?.edit_date }}</span>
        </div>
        <div class="row">
          <span class="label">起始页：</span>
          <span class="content">{{ currentSelectedBook?.first_page }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
  name: 'StepContentForPs',
  data () {
    return {}
  },
  computed: {
    ...mapState({
      currentSelectedBook: state => state.ps.currentSelectedBook
    })
  }
}
</script>
<style lang="less" scoped>
.top-box {
  display: flex;
  justify-content: space-between;
  width: 1000px
}
.top-box__step {
  position: relative;
  display: flex;
  justify-content: space-between;
  flex: 1;
  max-width: 600px
}
.top-box__step .arrows {
  position: absolute;
  left: 55px;
  right: 55px;
  top: 152px;
  height: 15px;
  display: flex;
  justify-content: space-between
}
.top-box__step .arrows .arrows-item {
  flex: 1;
  margin: 0 30px
}
.top-box__step .arrows .arrows-item .img {
  width: 100%
}
.top-box__step .item {
  display: inline-flex;
  flex-direction: column;
  align-items: center
}
.top-box__step .item .pic {
  width: 108px;
  height: 142px
}
.top-box__step .item .pic .img {
  width: 100%
}
.top-box__step .item .step {
  margin: 10px auto
}
.cover-container {
  width: 100px;
  height: 130px;
  transform-origin: left top;
  position: relative;
  .cover {
    display: block;
    width: 100%;
    height: 100%;
    -o-object-fit: fill;
    object-fit: fill;
  }
  .title {
    position: absolute;
    left: 64px;
    top: 16px;
    width: 24px;
    height: 76px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    border: 2px solid #fff;
    background-color: #92958d;
    padding: 1px;
    .title2{
      width: 100%;
      height: 100%;
      border: 1px solid #eae2c9;
      background-color: #dab272;
      box-sizing: border-box;
      padding: 1px;
      .title3 {
        width: 100%;
        height: 100%;
        background-color: #fff6c3;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .title4 {
          width: 100%;
          writing-mode: vertical-lr;
          white-space: nowrap;
          text-align: center;
          font-size: 12px;
          line-height: 14px;
          box-sizing: border-box;
        }
      }
    }
  }
}
.basic-info{
  width: 320px;
  height: 238px;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100%;
  background-image: url(../../../../assets/print_info_bg.png);
  color: #974d00
}

.basic-info .title {
  padding: 0 20px;
  height: 48px;
  display: flex;
  align-items: center;
  font-size: 20px
}

.basic-info .info {
  padding: 14px 20px 0
}

.basic-info .info .row {
  padding-bottom: 8px;
  display: flex
}

.basic-info .info .row .label{
  width: 70px;
  text-align: right
}
</style>
