<template>
  <div class="wrap">
    <div class="item">
      <div class="img">
        <div class="inner">
          <img :src="type101_1" />
          <div class="info">
            <div class="title" style="font-size: 70px;">
              <div v-for="str in data?.title" :key="str">{{ str }}</div>
            </div>
            <div class="sub-title">
              <div v-for="str in data?.sub_title" :key="str">{{ str }}</div>
            </div>
            <div class="volume">
              <div class="volume-item">
                <div>
                  <div v-for="str in data?.edit_date" :key="str">{{ str }}</div>
                </div>
              </div>
              <div class="volume-item">
                <div>
                  <div v-for="str in data?.hall" :key="str">{{ str }}</div>
                </div>
                <div class="space"></div>
                <div>
                  <div v-for="str in data?.volume" :key="str">{{ str }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="name">封面</div>
    </div>
    <div class="item">
      <div class="img">
        <div class="inner">
          <img :src="type101_2" />
        </div>
      </div>
      <div class="name">封底</div>
    </div>
  </div>
</template>
<script>
import type101_1 from '@/assets/prints/type101-1.png'
import type101_2 from '@/assets/prints/type101-2.png'
export default {
  name: 'Cover2Preview',
  data () {
    return {
      type101_1,
      type101_2
    }
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  }
}
</script>
<style lang="less" scoped>
.wrap{
  display:flex;
  flex-wrap:wrap;
  .item {
    width: 380px;
    height: 600px;
    margin-left: 20px;
    .img {
      height: 540px;
      .inner {
        width: 760px;
        height: 1080px;
        position: relative;
        transform: scale(.5);
        transform-origin: 0 0;
        img {
          border: none;
          max-width: 100%;
        }
        .info {
          color: #f4d128;
          .title{
            position: absolute;
            left: 486px;
            top: 266px;
            width: 129px;
            height: 530px;
            text-align: center;
            font-size: 70px;
            line-height: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
          }
          .sub-title {
            position: absolute;
            left: 636px;
            top: 240px;
            width: 24px;
            height: 743px;
            text-align: center;
            font-size: 24px;
            line-height: 1.3;
          }
          .volume {
            position: absolute;
            left: 80px;
            bottom: 60px;
            font-size: 24px;
            line-height: 1.3;
            display: flex;
            .volume-item {
              width: 36px;
            }
          }
          .space {
            height: 20px;
          }
        }
      }
    }
    .name {
      font-size: 20px;
    }
  }
}
</style>
