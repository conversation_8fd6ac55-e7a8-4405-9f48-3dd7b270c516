<template>
  <div :class="toggleClass()">
    <div title="收起" class="toggle-btn" @click="handleToggle">
      <i>{{ visible ? '<' : '>' }}</i>
      <div class="hint">
        <div>点</div>
        <div>击</div>
        <div>{{ visible ? '收' : '展' }}</div>
        <div>{{ visible ? '起' : '开' }}</div>
      </div>
    </div>
    <div v-if="visible">
      <div class="operate">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarCDNjiedian" title="创建谱系/分支" @click="handleAddClan" />
        <i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai" title="编辑谱系" @click="handleEditSelected()"/>
        <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbartishi" title="谱系信息" @click="handleClanInfo()"/>-->
        <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1" title="删除谱系/分支" @click="handleDelete"/>
        <!--        <br/>-->
        <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou" title="上移" @click="handleMove('up')" />-->
        <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou1" title="下移" @click="handleMove('down')" />-->
        <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbardaoru" title="导出备份" @click="handleBackUp('export')" />-->
        <!--        <i class="cus-edit-toolbar-icon icon-edit-toolbardaochu" title="导入备份" @click="handleBackUp('import')" />-->
      </div>
      <a-divider style="margin: 12px 0"/>
      <div class="f18 ml10 mb4"><span class="line"/>谱系分支({{ pedigreeList?.length }}/5)</div>
      <div class="menu">
        <a-spin :spinning="loading">
          <div
            class="item"
            :class="item.id === pedigreeId ? 'selected':''"
            v-for="item in pedigreeList"
            :key="item.id"
            @click="handleSelected(item)"
          >
            <div class="pic">
              <div class="h-svg-icon-box">
                <i
                  class="cus-edit-toolbar-icon icon-edit-toolbarCDNjiedian"
                  style="font-size: 14px; color: white; height: 14px; width: 14px;"
                />
              </div>
            </div>
            <div class="txt-box">
              <div class="name line-word-2">{{ item.name }}</div>
              <i title="编辑" class="edit cus-edit-toolbar-icon icon-edit-toolbarxiugai" style="font-size: 16px" @click="(e) => handleEdit(item, e)"/>
            </div>
          </div>
          <a-empty v-show="pedigreeList?.length===0" />
        </a-spin>
        <div class="item add" @click="handleAddClan">+新建谱系</div>
      </div>
    </div>
    <edit-pedigree-modal
      v-if="showEditPedigreeModal"
      :item="editItem"
      @refresh="onRefresh"
      @close-modal="handleClose"
    />
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import EditPedigreeModal from './pedigree'
export default {
  components: {
    EditPedigreeModal
  },
  data () {
    return {
      visible: true,
      loading: false,
      pedigreeList: [],
      pedigreeId: '',
      editItem: {},
      showEditPedigreeModal: false
    }
  },
  computed: {
    ...mapState({
      list: state => state.px.list,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  watch: {
    list: {
      handler (val) {
        this.pedigreeList = val
      },
      immediate: true
    },
    currentSelectedPedigreeId: {
        handler (val) {
          this.pedigreeId = val
          if (val) {
            /**
             * 主动获取谱系成员列表的地方
             */
            this.handleGetPedigreeMemberList(val)
          }
        },
        immediate: true
      }
  },
  mounted () {
    this.onRefresh()
  },
  methods: {
    ...mapActions([
      'GetPedigreeList',
      'SetCurrentPedigreeId',
      'GetPedigreeMemberList',
      'DeletedPedigree'
    ]),
    handleAddClan () {
      if (this.list.length >= 5) {
        this.$message.info('谱系分支最多为5个')
        return
      }
      if (this.currentSelectedClanId) {
        this.showEditPedigreeModal = true
      } else {
        this.$message.info('请先添加家族')
      }
    },
    handleClose () {
      this.editItem = {}
      this.showEditPedigreeModal = false
    },
    async onRefresh () {
      if (!this.currentSelectedClanId) {
        return
      }
      this.loading = true
      await this.GetPedigreeList()
      this.loading = false
    },
    async handleGetPedigreeMemberList (currentSelectedPedigreeId) {
      await this.GetPedigreeMemberList({
        clan_id: this.currentSelectedClanId,
        pedigree_id: currentSelectedPedigreeId,
        generation: 3
      })
    },
    handleEdit (item, e) {
      e.stopPropagation()
      this.editItem = item
      this.showEditPedigreeModal = true
    },
    handleSelected (item) {
      this.SetCurrentPedigreeId(item.id)
    },
    handleToggle () {
      this.visible = !this.visible
    },
    toggleClass () {
      return this.visible ? 'left-content' : 'left-content close'
    },
    handleEditSelected () {
      if (!this.currentSelectedClanId) {
        this.$message.info('请先添加家族')
        return
      }
      const item = this.pedigreeList.find(item => item.id === this.currentSelectedPedigreeId)
      this.editItem = item
      this.showEditPedigreeModal = true
    },
    async handleDelete () {
      if (!this.currentSelectedPedigreeId) {
        this.$message.error('请选择要删除的谱系分支')
        return
      }
      const that = this
      const { DeletedPedigree } = this
      const params = {
        clan_id: this.currentSelectedClanId,
        id: this.currentSelectedPedigreeId
      }
      const currentPedigree = this.pedigreeList.find(item => item.id === this.currentSelectedPedigreeId)
      this.$confirm({
        title: '',
        content: `确定删除「${currentPedigree.name}」谱系分支吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const index = that.pedigreeList.findIndex(item => item.id === that.currentSelectedPedigreeId)
          const item = index === 0 ? that.pedigreeList[1] : that.pedigreeList[index - 1]
          await DeletedPedigree(params)
          if (that.pedigreeList.length > 1) {
            that.SetCurrentPedigreeId(item.id)
          } else {
            that.SetCurrentPedigreeId('')
          }
          await that.onRefresh()
        }
      })
    },
    handleClanInfo () {
      this.$message.info('谱系信息待完善')
    },
    async handleMove (type) {
      if (!this.currentSelectedPedigreeId) {
        this.$message.info('请选择要移动的谱系分支')
        return
      }
      this.$message.info('移动信息待完善')
      switch (type) {
        case 'up':
          break
        case 'down':
          break
      }
    },
    async handleBackUp (type) {
      switch (type) {
        case 'export':
          this.$message.info('导出信息待完善')
          break
        case 'import':
          this.$message.info('导入信息待完善')
          break
      }
    }
  }
}
</script>
<style lang='less' scoped>
.left-content {
  width: 240px;
  height: calc(100vh - 60px);
  background-color: #f5f5f5;
  position:relative;
  transition: width 300ms ease;
  .toggle-btn{
    position:absolute;
    top:50%;
    transform:translateY(-50%);
    width:18px;
    height:160px;
    display:flex;
    flex-direction:column;
    align-items:center;
    justify-content:center;
    background-color:#ebebeb;
    color:#666;
    cursor:pointer;
    transition:all .2s;
    z-index:10;
    -webkit-user-select:none;
    -moz-user-select:none;
    user-select:none;
    left:100%;
    border-radius:0 100px 100px 0;
    .hint{ display:none;}
    &:hover {
      background-color: #f86e04;
      color: #fff;
      .hint { display: block}
    }
  }
}
.left-content.close{
  width: 0;
  transition: width 300ms ease;
}
.operate {
  display:table;
}
.line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #f76d02;
  margin-right: 6px
}
.menu{
  flex: 1;
  overflow-x: hidden;
  padding: 0 10px;
  .item {
    position: relative;
    width: 180px;
    height: 60px;
    border-radius: 2px;
    box-shadow: inset 0 0 0 1px #e5e5e5;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    padding: 10px;
    box-sizing: border-box;
    cursor: pointer;
    transition: all .3s;
    .pic {
      margin-right: 5px;
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      background: #c7a27c;
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .txt-box {
      flex: 1
    }
    .edit {
      position: absolute;
      right: 5px;
      bottom: 5px;
      visibility: hidden;
      opacity: 0;
      transition: all .3s
    }
  }
  .item.selected {
    box-shadow: inset 0 0 0 2px #ff7926;
    color: #ff7926;
    background: linear-gradient(90deg,#fff7f0,#fff)
  }
  .item:hover {
    box-shadow: inset 0 0 0 2px #ff7926
  }
  .item:hover .edit {
    visibility: visible;
    opacity: 1
  }
  .add {
    align-items: center;
    justify-content: center;
    color: #f86e04;
    box-shadow: inset 0 0 0 0 #e5e5e5 !important;
    border: 1px dashed #f86e04;
  }
}
.cus-edit-toolbar-icon {
  font-size: 28px;
  margin: 0 10px;
}
.cus-edit-toolbar-icon:hover {
  color: #f86e04;
  cursor: pointer;
}

</style>
