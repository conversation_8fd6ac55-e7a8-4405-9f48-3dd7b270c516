<template>
  <div class="header flex align-center">
    <div class="inner">
      <div :class="['item',{'selected': activeNodeInfo.id === mainNodeInfo.id}]" @click="selectNode(mainNodeInfo)">
        <div class="generation">{{ mainNodeInfo.generation }}世</div>
        <div class="node">{{ mainNodeInfo.full_name }}</div>
      </div>
      <div :class="['item wife',{'selected': activeNodeInfo.id === wife.id}]" v-for="(wife, index) in mainNodeInfo.wife" :key="wife.id" @click="selectNode(wife)">
        {{ wife.relation }} : {{ wife.full_name }}
        <div class="exchange" v-if="index < mainNodeInfo?.wife?.length - 1"><i
          class="cus-edit-toolbar-icon icon-edit-toolbarjiaohuan" /></div>
        <div class="delete" v-if="selectedId === wife.id" @click.stop="handleDeleteWife(wife)"><i class="cus-edit-toolbar-icon icon-edit-toolbarcuo" /></div>
      </div>
      <div class="add" @click="handleAddWife">+添加配偶</div>
    </div>
  </div>
</template>
<script>

export default {
  name: 'DetailTabsHeader',
  data () {
    return {
      selectedId: null
    }
  },
  props: {
    mainNodeInfo: {
      type: Object,
      default: () => {
      }
    },
    activeNodeInfo: {
      type: Object,
      default: () => {
      }
    }
  },
  watch: {
    activeNodeInfo: {
      handler (val) {
        if (val && val.id) {
          this.selectedId = val.id
        }
      },
      immediate: true
    }
  },
  methods: {
    handleAddWife () {
      this.$emit('handleAddWife')
    },
    handleDeleteWife (wife) {
      this.$emit('handleDeleteWife', wife)
    },
    selectNode (node) {
      if (this.selectedId === node.id) {
        return
      }
      this.selectedId = node.id

      // 如果选择的是配偶，需要为配偶对象添加必要的属性
      let nodeToEmit = node
      if (node.husband_id || (this.mainNodeInfo.wife && this.mainNodeInfo.wife.some(w => w.id === node.id))) {
        // 这是一个配偶节点，确保它有必要的属性
        nodeToEmit = {
          ...node,
          husband_id: node.husband_id || this.mainNodeInfo.id,
          generation: node.generation || this.mainNodeInfo.generation,
          father_id: node.father_id || this.mainNodeInfo.id
        }
      }

      this.$emit('handleSelectedNode', nodeToEmit)
    }
  }
}
</script>
<style scoped lang='less'>
.header {
  background-color: #fff;
  padding: 0 0 10px 10px;

  .inner {
    display: flex;
    width: 100%;
    align-items: center;
    border-bottom: 1px solid #ccc;
    flex-wrap: wrap;

    .item {
      height: 40px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 0 20px;
      min-width: 120px;
      background-color: #f5f7fa;
      margin-right: 5px;
      margin-top: 10px;
      border-top: 1px solid #ccc;
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
      border-radius: 2px 2px 0 0;
      position: relative;
      flex-shrink: 0;
      cursor: pointer;

      &.selected {
        background-color: #f86e04;
        border-color: #f86e04;
        color: #fff;
      }

      &.wife {
        position: relative;

        .exchange {
          background-color: #fff;
          position: absolute;
          right: -14px;
          top: 8px;
          width: 22px;
          height: 22px;
          border-radius: 50%;
          color: #f86e04;
          z-index: 10;
          display: flex;
          justify-content: center;
          align-items: center;

          .icon-edit-toolbarjiaohuan {
            font-size: 20px;
            transform: rotate(90deg);
          }
        }
      }

      .generation {
        height: 40px;
        line-height: 40px;
        border-right: 1px dashed #fff;
        padding-right: 10px;
        margin-right: 10px;
      }

      .delete {
        position: absolute;
        right: -8px;
        top: -8px;
        background-color: #fff;
        border: 1px solid #f86e04;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #f86e04;
        z-index: 1;
      }
    }

    .add {
      border: 1px solid #f86e04;
      line-height: 1;
      padding: 8px 5px;
      color: #f86e04;
      margin-left: 15px;
      margin-top: 10px;
      border-radius: 2px;
      cursor: pointer;
    }
  }
}
</style>
