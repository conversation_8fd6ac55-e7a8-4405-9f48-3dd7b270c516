<template>
  <a-modal
    class="family-edit-modal-wrap"
    v-model="visible"
    :title="type === 'add' ? '新增家族':'编辑家族'"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <a-form :form="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }" @submit="handleSubmit">
      <a-form-item label="家族分组">
        <a-select
          v-decorator="['group_id',{ rules: [{ required: true, message: '请选择家族分组!' }] }]"
          placeholder="请选择家族分组"
        >
          <a-select-option :value="item.id" v-for="item in groups" :key="item.id">{{ item.group_name }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="家族名称">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: true, message: '请输入家族名称!' }] }]" />
      </a-form-item>
      <a-form-item label="家族姓氏">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['surname', { rules: [{ required: true, message: '请输入家族姓氏!' }] }]" />
      </a-form-item>
      <a-form-item label="始迁祖">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['ancestor', { rules: [{ required: true, message: '请输入始迁祖!' }] }]" />
      </a-form-item>
      <a-form-item label="堂号">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['hall_name', { rules: [{ required: false, message: '请输入堂号!' }] }]" />
      </a-form-item>
      <a-form-item label="所在地区">
        <area-cascader :init-value="initValue" v-decorator="['province', {rules: [{ required: true, validator: checkPrice }]}]" />
      </a-form-item>
      <a-form-item label="家族简介">
        <a-textarea :auto-size="{ minRows: 3, maxRows: 3 }" placeholder="请输入" v-decorator="['description']" />
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" html-type="submit">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import AreaCascader from '@/components/area-cascader'
export default {
  name: 'FamilyEditComponent',
  components: {
    AreaCascader
  },
  data () {
    return {
      submitting: false,
      visible: true,
      initValue: [],
      formLayout: 'horizontal',
      form: this.$form.createForm(this, { name: 'xxx' })
    }
  },
  computed: {
    ...mapState({
      groups: state => state.family.groups,
      groupClan: state => state.family.groupClan
    })
  },
  props: {
    id: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: 'edit'
    },
    groupId: {
      type: [String, Number],
      default: ''
    }
  },
  mounted () {
    this.form.getFieldDecorator('group_id', { initialValue: this.groupId })
    this.form.getFieldDecorator('province', { initialValue: undefined })
    if (this.type === 'edit') {
      this.getData()
    }
  },
  methods: {
    ...mapActions(['AddClan']),
    getData () {
      const groupClan = this.groupClan.find(item => item.id === this.groupId)
      if (groupClan) {
        const obj = groupClan.clan_list.find(item => `${item.id}` === `${this.id}`)
        if (obj) {
          const { name, surname, ancestor, province, city, district, description } = obj
          this.initValue = [province, city, district]
          this.form.getFieldDecorator('name', { initialValue: name })
          this.form.getFieldDecorator('surname', { initialValue: surname })
          this.form.getFieldDecorator('ancestor', { initialValue: ancestor })
          this.form.getFieldDecorator('hall_name', { initialValue: obj.hall_name })
          this.form.getFieldDecorator('province', { initialValue: [province, city, district] })
          this.form.getFieldDecorator('description', { initialValue: description || '这是简介' })
        }
      }
    },
    handleSubmit (e) {
      e.preventDefault()
      const { AddClan } = this
      this.form.validateFields(async (err, values) => {
        if (!err) {
          const params = { ...values }
          params.province = values.province[0]
          params.city = values.province[1]
          params.district = values.province[2] ? values.province[2] : ''
          params.group_id = parseInt(params.group_id)
          if (this.id) {
            params.id = this.id
          }
          this.submitting = true
          await AddClan(params)
          this.submitting = false
          this.$emit('refresh')
          this.$emit('close')
        }
      })
    },
    handleOk (e) {
      this.visible = false
    },
    checkPrice (rule, value, callback) {
      console.log('rule', rule, 'value', value)
      if (value && value.length > 0) {
        callback()
        return
      }
      const str = '请选择所在地区!'
      callback(str)
    }
  }
}
</script>
<style lang="less" scoped>
.family-edit-modal-wrap {
   /deep/ .ant-modal-footer { display:none;}
}
</style>
