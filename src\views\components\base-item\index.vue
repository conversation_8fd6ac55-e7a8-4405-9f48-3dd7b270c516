<template>
  <div class="row">
    <div class="label mr4">{{ label }}</div>
    <div class="txt">{{ txt }}</div>
    <i title="编辑" v-if="isEdit" class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-999 c-pointer-hover" style="font-size:16px;"/>
  </div>
</template>
<script>
export default {
  data () {
    return {
    }
  },
  props: {
    label: {
      type: String,
      default: ''
    },
    txt: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="less" scoped>
.row {
  display: flex;
  padding: 7px 0;
  .label {
    width: 80px;
    min-width: 80px;
    text-align: right;
    color: #666;
  }
  .txt {
  }
}
</style>
