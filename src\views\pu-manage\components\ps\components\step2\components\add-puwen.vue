<template>
  <a-modal
    class="add-puwen-modal-wrap"
    v-model="visible"
    title="添加谱文"
    width="800px"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <div class="flex flex-1 tab">
      <div class="item" :class="tab===1?'actived':''" @click="tab=1">引用谱文</div>
      <div class="item" :class="tab===2?'actived':''" @click="tab=2">新建谱文</div>
    </div>
    <div class="flex flex-1 tab-content flex-direction-column" v-if="tab===1">
      <div class="flex flex-direction-row" style="height: 250px">
        <div class="left">
          <div class="catalog-li">
            <div v-for="item in groupList" :key="item.id" class="catalog-item">
              <div class="title" @click="toggleCategory(item.id)">
                <div>
                  <i
                    class="cus-edit-toolbar-icon"
                    :class="openCategoryIdList.includes(item.id) ? 'icon-edit-toolbardakai':'icon-edit-toolbarwenjianjia'"
                    style="font-size:16px;margin:0"
                  />
                  {{ item.name }}
                </div>
                <div></div>
                <div class="article-item" v-show="openCategoryIdList.includes(item.id)">
                  <div
                    v-for="article in item.article_info"
                    :key="article.id"
                    class="c-pointer-hover mt4 flex justify-content-space-between"
                    :class="selectedArticleIdList.includes(article.id)?'selected': ''"
                    @click="(e) => handleArticleClick(article, item, e)"
                  >
                    <div><i class="cus-edit-toolbar-icon icon-edit-toolbarguangbiaodaohang mr4 guanbiao"/> {{ article.name }}</div>
                    <div><i class="cus-edit-toolbar-icon icon-edit-toolbarduigou" v-if="selectedArticleIdList.includes(article.id)"/> </div>
                  </div>
                </div>
              </div>
              <a-empty v-show="groupList?.length===0" />
            </div>
          </div>
        </div>
        <div class="right">
          <div>已选择谱文：</div>
          <div>
            <div class="flex mt4 mb4" v-for="article in selectedArticleList" :key="article.id">
              <div class="flex-1">
                <i class="cus-edit-toolbar-icon icon-edit-toolbarguangbiaodaohang mr4 guanbiao"/>{{ article.name }}
              </div>
              <div>
                <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou1 mr4 c-pointer-hover" @click="() => handleMove('down', article.id)"></i>
                <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou mr4 c-pointer-hover" @click="() => handleMove('up', article.id)"></i>
                <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover" @click="()=> handleDelete(article.id)"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" :loading="confirmLoading" html-type="submit">确定</a-button>
      </div>
    </div>
    <a-form :form="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }" @submit="handleSubmit" v-if="tab===2">
      <a-form-item label="谱文标题" class="form-wrap">
        <a-input
          placeholder="请输入谱文标题"
          :suffix="len+'/100'"
          :maxLength="100"
          @change="handleChange"
          v-decorator="['name', {
            rules: [{ required: true, message: '请输入谱文标题' }],
            normalize: (value) => value && value.trim()
          }]"
        />
      </a-form-item>
      <a-form-item label="谱文布局" class="form-wrap">
        <a-select
          placeholder="请选择谱文布局"
          v-decorator="['layout', {rules: [{ required: true, message: '请选择谱文布局' }]}]"
        >
          <a-select-option value="横向左开">横向左开</a-select-option>
          <a-select-option value="竖向右开">竖向右开</a-select-option>
          <a-select-option value="竖向左开">竖向左开</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="纸张" class="form-wrap">
        <a-select
          placeholder="请选择纸张"
          v-decorator="['print_id', {rules: [{ required: true, message: '请选择纸张' }]}]"
        >
          <a-select-option :value="1">默认纸张</a-select-option>
          <a-select-option :value="2">横向纸张</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="类目名称" class="form-wrap">
        <a-select
          placeholder="请选择类目名称"
          v-decorator="['category_id', {rules: [{ required: false, message: '请选择类目名称' }]}]"
        >
          <a-select-option :value="item.id" v-for="item in sysClanList" :key="item.id">{{ item.name }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" :loading="confirmLoading" html-type="submit">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import cloneDeep from 'lodash.clonedeep'
export default {
  name: 'Step3AddPuWen',
  data () {
    return {
      tab: 1,
      len: 0,
      visible: true,
      groupList: [],
      sysClanList: [],
      openCategoryIdList: [],
      selectedArticleIdList: [],
      selectedArticleList: [],
      confirmLoading: false,
      form: this.$form.createForm(this)
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId
    })
  },
  mounted () {
    this.getList()
    this.getCategoryList()
  },
  methods: {
    ...mapActions(['GetClanArticleList', 'GetCategoryList', 'AddClanArticle']),
    handleChange (e) {
      this.len = e.target.value.length
    },
    toggleCategory (id) {
      if (this.openCategoryIdList.includes(id)) {
        this.openCategoryIdList = this.openCategoryIdList.filter(item => item !== id)
      } else {
        this.openCategoryIdList.push(id)
      }
    },
    handleArticleClick (article, category, e) {
      e.stopPropagation()
      if (this.selectedArticleIdList.includes(article.id)) {
        this.selectedArticleList = this.selectedArticleList.filter(item => item.id !== article.id)
        this.selectedArticleIdList = this.selectedArticleIdList.filter(item => item !== article.id)
      } else {
        this.selectedArticleIdList.push(article.id)
        this.selectedArticleList.push(article)
      }
    },
    async getList () {
      const { GetClanArticleList, currentSelectedClanId } = this
      if (!currentSelectedClanId) {
        return
      }
      this.groupListLoading = true
      const res = await GetClanArticleList({ clan_id: currentSelectedClanId })
      this.groupListLoading = false
      if (res.code === 0) {
        const _data = res.data.list
        this.groupList = _data
      }
    },
    async getCategoryList () {
      const res = await this.GetCategoryList({ clanId: this.currentSelectedClanId })
      if (res.code === 0) {
        this.sysClanList = res.data.List.filter(item => item.is_system === 1)
      }
    },
    handleOk () {
      this.$message.info('功能暂未实现')
    },
    handleMove (type, id) {
      const index = this.selectedArticleList.findIndex(item => item.id === id)
      const list = this.selectedArticleList
      if (type === 'up') {
        if (index > 0) {
          [list[index - 1], list[index]] = [list[index], list[index - 1]]
        }
      } else {
        if (index < list.length - 1) {
          [list[index + 1], list[index]] = [list[index], list[index + 1]]
        }
      }
      this.selectedArticleList = cloneDeep(list)
    },
    handleDelete (id) {
      this.selectedArticleIdList = this.selectedArticleIdList.filter(item => item !== id)
      this.selectedArticleList = this.selectedArticleList.filter(item => item.id !== id)
    },
    handleSubmit (e) {
      e.preventDefault()
      const { form: { validateFields }, AddClanArticle } = this
      validateFields(async (errors, values) => {
        if (!errors) {
          const params = {
            clan_id: this.currentSelectedClanId,
            ...values
          }
          if (params.category_id) {
            const category = this.sysClanList.find(item => item.id === params.category_id)
            if (category) {
              params.category_name = category.name
            }
          }
          this.confirmLoading = true
          const result = await AddClanArticle(params)
          this.confirmLoading = false
          if (result.code === 0) {
            this.$emit('refresh')
            this.$emit('close')
          }
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
.add-puwen-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
  height: 600px;
  .tab {
    height: 45px;
    border-bottom: 1px solid #fff1e6;
    display: flex;
    align-items: center;
    .item {
      margin-right: 1px;
      width: 156px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      background-color: #f2f3f5;
      cursor: pointer;
      &.actived {
        background-color: #fff1e6;
        color: #f86e04;
      }
    }
  }
  .tab-content {
    display: flex;
    height: 325px;
    .left {
      width:400px;
      .catalog-li {
        display:flex;
        flex-direction:column;
        width:100%;
        font-size:12px;
        .catalog-item {
          .title {
            padding: 0 4px;
            cursor:pointer;
            font-size:16px;
            display:flex;
            flex-direction:column;
            .dynamic-icon {
              display:none;
            }
          }
          .title:hover {
            .dynamic-icon {
              display:inline-block;
            }
          }
          .title.selected { color: #ff7926;}
          .article-item {
            font-size: 14px;
            padding: 6px 4px 6px 20px;
            background-color:#f5f5f5;
            .selected { color: #ff7926;}
          }
        }
      }
    }
    .guanbiao {
      transform: rotate(90deg);
      display:inline-block;
    }
    .right {
      flex: 1;
    }
    .bottom {
      width: 100%;
      text-align:center;
    }
  }
}
</style>
