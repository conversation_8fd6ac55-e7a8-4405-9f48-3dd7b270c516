<template>
  <div class="generations">
    <div class="item">主世系</div>
    <div class="item" v-for="(item) in list" :class="item === selected ? 'current': ''" :key="item">{{ item }}世</div>
  </div>
</template>
<script>
export default {
   props: {
     list: {
       type: Array,
       default: () => []
     },
     selected: {
      type: Number,
      default: -999
    }
  }
}
</script>
<style lang='less' scoped>
.generations {
  background-color: #f9f5f1;
  width: fit-content;
  min-width: max-content;
  height: 32px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .item {
    width: 99px;
    flex-shrink: 0;
    text-align: center;
    line-height: 32px;
    border-left: 1px dashed #eaceac;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    color: #99908a
  }

  .item:first-child {
    border-left-color: transparent
  }

  .item.current {
    background-color: #c7a27c;
    color: #fff;
    border-radius: 2px;
    position: relative;
    border-left-color: transparent
  }
}
</style>
