<template>
  <div class="book">
    <div ref="bookPreview" id="flipbook-preview">
      <div v-for="(item, index) in pdfPicturePath" :key="bookId+'_'+index" class="page">
        <img style="width: 100%; height: 100%" :src="item" />
      </div>
    </div>
    <div class="prev" @click="next"><i class="cus-edit-toolbar-icon icon-edit-toolbaryoujiantou1"/></div>
    <div class="next" @click="previous"><i class="cus-edit-toolbar-icon icon-edit-toolbaryoujiantou1"/></div>
  </div>
</template>
<script>
import * as pdfjs from 'pdfjs-dist'
import cloneDeep from 'lodash.clonedeep'
import * as pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry'
pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker

export default {
  name: 'BookPreview',
  props: {
    bookId: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      pdfPicturePath: []
    }
  },
  watch: {
    data: {
      handler (val) {
        this.pdfPicturePath = cloneDeep(val)
      },
      immediate: true
    }
  },
  mounted () {
    // this.$refs.bookPreview
    window.$('#flipbook-preview').turn({
      autoCenter: true, // 自动居中, 默认false
      height: 600, // 高度
      width: 780, // 宽度
      display: 'double', // 单页显示/双页显示  single/double
      elevation: 50,
      duration: 500, // 翻页速度(毫秒), 默认600ms
      gradients: true, // 翻页时的阴影渐变, 默认true
      acceleration: true, // 硬件加速, 默认true, 如果是触摸设备设置为true
      page: 2 // 设置当前显示第几页
    })
  },
  methods: {
    next () {
      window.$('#flipbook-preview').turn('next')
    },
    previous () {
      window.$('#flipbook-preview').turn('previous')
    },
    handleClick (item) {
      this.bookId = item.id
      this.pdfPicturePath = cloneDeep(item.samples)
      window.$('#flipbook-preview').turn('page', 2)
      window.$('#flipbook-preview').turn('update')
    }
  }
}
</script>
<style lang='less' scoped>
.book {
  overflow: hidden;
  width: 780px;
  text-align: center;
  position:relative;
  #flipbook-preview{
    border: 3px solid black;
  }
  .prev,.next {
    position: absolute;
    top: 50%;
    background-color: rgba(0,0,0,.3);
    width: 40px;
    height:40px;
    line-height: 40px;
    border-radius:50%;
    i {
      color: #fff;
    }
  }
  .next {
    right: 0;
    transform:rotateY(180deg);
  }
}
</style>
