<template>
  <a-modal
    class="family-sort-modal-wrap"
    v-model="visible"
    title="家族排序"
    :comformLoading="submitting"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <div class="flex align-center justify-content-space-between" style="padding: 10px 20px;">
      <div class="flex align-center c-pointer-hover" @click="handleMove('first')"><i class="cus-edit-toolbar-icon icon-edit-toolbarzhidingxian" style="font-size:14px"/>置顶</div>
      <div class="flex align-center c-pointer-hover" @click="handleMove('up')"><i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou" style="font-size:14px"/>上移</div>
      <div class="flex align-center c-pointer-hover" @click="handleMove('down')"><i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou1" style="font-size:14px"/>下移</div>
      <div class="flex align-center c-pointer-hover" @click="handleMove('last')"><i class="cus-edit-toolbar-icon icon-edit-toolbarzhidixian" style="font-size:14px"/>置底</div>
    </div>
    <div
      v-for="item in clanList"
      :key="item.id"
      @click="selectId=item.id"
      class="item"
      :class="item.id === selectId ? 'active': ''"
    >{{ item.name }}</div>
  </a-modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import cloneDeep from 'lodash.clonedeep'

export default {
  name: 'FamilySortComponent',
  data () {
    return {
      visible: true,
      clanList: [],
      selectId: '',
      submitting: false
    }
  },
  computed: {
    ...mapState({
      groupClan: state => state.family.groupClan
    })
  },
  watch: {
    groupClan: {
      handler (val) {
        this.clanList = val.find(item => item.id === this.groupId)?.clan_list || []
      },
      immediate: true
    }
  },
  props: {
    groupId: {
      type: [Number, String],
      default: ''
    }
  },
  methods: {
    ...mapActions(['GroupClanList', 'SaveGroup']),
    async handleOk (e) {
      this.submitting = true
      // await this.SaveGroup(this.list)
      this.visible = false
      this.submitting = false
      this.$emit('close')
      this.$emit('refresh')
    },
    async handleMove (type) {
      if (!this.selectId) {
        this.$message.error('请选择要操作的项')
        return
      }
      const index = this.clanList.findIndex(item => item.id === this.selectId)
      const list = this.clanList
      const len = list.length
      switch (type) {
        case 'up':
          if (index > 0) {
            [list[index - 1], list[index]] = [list[index], list[index - 1]]
          }
          break
        case 'down':
          if (index < list.length - 1) {
            [list[index + 1], list[index]] = [list[index], list[index + 1]]
          }
            break
        case 'first':
          [list[0], list[index]] = [list[index], list[0]]
          break
        case 'last':
          [list[len - 1], list[index]] = [list[index], list[len - 1]]
          break
      }
      console.log(list)
      this.clanList = cloneDeep(list)
    }
  }
}
</script>
<style lang="less">
.family-sort-modal-wrap {
  .item {
    display:flex;
    height: 40px;
    align-items:center;
    background: #f2f2f2;
    margin-bottom: 4px;
    padding-left: 12px;
    border: 1px dashed #f2f2f2
  }
  .item.active {
    border: 1px dashed #f86e04;
    color: #f86e04;
  }
}
</style>
