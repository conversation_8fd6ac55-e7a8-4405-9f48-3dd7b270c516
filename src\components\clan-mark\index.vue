<template>
  <img v-if="type === 'down'" src="../../assets/header-mark.c8ec6717.svg" alt="" :class="classStr" />
  <img v-else src="../../assets/header-mark2.ec123bde.svg" alt="" :class="classStr" />
</template>
<script>
export default {
  name: 'ClanMarkImg',
  props: {
    classStr: {
      type: String,
      default: ''
    },
    // down or up
    type: {
      type: String,
      default: 'top'
    }
  }
}
</script>
<style lang="less" scoped>

</style>
