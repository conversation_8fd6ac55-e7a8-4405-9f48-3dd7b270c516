// [
//   {
//   'pageSetting': {
//     'padding': [0, 0, 0, 0]
//   },
//   'items': [
//     {
//     'item': {
//       'name': '文本框',
//       'type': 'Textbox',
//       'text': 'title',
//       'placeholder': '请输入标题',
//       'style': {
//         'fontWeight': 'bold',
//         'textDecoration': '',
//         'fontStyle': '',
//         'fontSize': '29px',
//         'fontFamily': '宋体',
//         'textAlign': 'center',
//         'lineHeight': '200%',
//         'textAlignLast': '',
//         'letterSpacing': '3px',
//         'textIndent': '',
//         'color': 'rgba(0, 0, 0, 1)',
//         'writingMode': 'vertical-rl',
//         'flexDirection': 'column',
//         'alignItems': 'center',
//         'direction': '',
//         'unicodeBidi': ''
//       }
//     },
//     'lock': false,
//     'autoPaging': false,
//     'style': {
//       'left': 476,
//       'top': 9,
//       'height': 810,
//       'width': 58,
//       'zIndex': 1,
//       'padding': [0, 0, 0, 0],
//       'borderColor': 'rgba(0, 0, 0, 1)',
//       'borderWidth': '1pt',
//       'borderStyle': 'solid',
//       'fill': 0,
//       'backgroundColor': '',
//       'border': 'none',
//       'rotate': 0,
//       'rotateX': 0,
//       'rotateY': 0,
//       'scaleX': 1,
//       'scaleY': 1
//     },
//     'itemType': 'title'
//   },
//     {
//       'item': {
//         'name': '文本框',
//         'type': 'Textbox',
//         'text': 'page1',
//         'placeholder': '请输入内容',
//         'style': {
//           'fontWeight': '',
//           'textDecoration': '',
//           'fontStyle': '',
//           'fontSize': '24px',
//           'fontFamily': '宋体',
//           'textAlign': 'justify',
//           'lineHeight': '150%',
//           'textAlignLast': 'left',
//           'letterSpacing': '3px',
//           'textIndent': '',
//           'color': 'rgba(0, 0, 0, 1)',
//           'writingMode': 'vertical-rl',
//           'flexDirection': 'column',
//           'alignItems': 'flex-end',
//           'direction': '',
//           'unicodeBidi': ''
//         }
//       },
//       'lock': false,
//       'autoPaging': true,
//       'style': {
//         'left': 8,
//         'top': 9,
//         'height': 810,
//         'width': 468,
//         'zIndex': 0,
//         'padding': [0, 0, 0, 0],
//         'borderColor': 'rgba(0, 0, 0, 1)',
//         'borderWidth': '1pt',
//         'borderStyle': 'solid',
//         'fill': 0,
//         'backgroundColor': '',
//         'border': 'none',
//         'rotate': 0,
//         'rotateX': 0,
//         'rotateY': 0,
//         'scaleX': 1,
//         'scaleY': 1
//       },
//       'itemType': 'content'
//     }]
// },
//   {
//     'pageSetting': {
//       'padding': [0, 0, 0, 0]
//     },
//     'items': [{
//       'item': {
//         'name': '文本框',
//         'type': 'Textbox',
//         'text': 'page2',
//         'placeholder': '请输入内容',
//         'style': {
//           'fontWeight': '',
//           'textDecoration': '',
//           'fontStyle': '',
//           'fontSize': '24px',
//           'fontFamily': '宋体',
//           'textAlign': 'justify',
//           'lineHeight': '150%',
//           'textAlignLast': 'left',
//           'letterSpacing': '3px',
//           'textIndent': '',
//           'color': 'rgba(0, 0, 0, 1)',
//           'writingMode': 'vertical-rl',
//           'flexDirection': 'column',
//           'alignItems': 'flex-end',
//           'direction': '',
//           'unicodeBidi': ''
//         }
//       },
//       'lock': false,
//       'autoPaging': true,
//       'style': {
//         'left': 19,
//         'top': 9,
//         'height': 810,
//         'width': 504,
//         'zIndex': 0,
//         'padding': [0, 0, 0, 0],
//         'borderColor': 'rgba(0, 0, 0, 1)',
//         'borderWidth': '1pt',
//         'borderStyle': 'solid',
//         'fill': 0,
//         'backgroundColor': '',
//         'border': 'none',
//         'rotate': 0,
//         'rotateX': 0,
//         'rotateY': 0,
//         'scaleX': 1,
//         'scaleY': 1
//       },
//       'itemType': 'content'
//     }]
//   }
//   ]
