<template>
  <a-modal
    title="节点统计"
    :visible="visible"
    :width="700"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="保存"
    cancelText="取消"
  >
    <div class="node">【该功能暂未实现，以下都为假数据】</div>
    <div>
      世数区间：<a-input style="width: 50px" placeholder="可选" size="small" />至<a-input style="width: 50px" placeholder="可选" size="small" />
      房派: <a-select style="width: 100px" placeholder="请选择" size="small" />
      在世: <a-select style="width: 100px" placeholder="请选择" size="small">
      </a-select>
      <a-button type="primary" style="margin-left: 10px" size="small">统计</a-button>
    </div>
    <div class="flex">
      <div class="item">
        <div>总人数</div>
        <div>253</div>
      </div>
      <div class="item">
        <div>出嫁女丁及后代</div>
        <div>0</div>
      </div>
      <div class="item">
        <div>男性人数</div>
        <div>253</div>
      </div>
      <div class="item">
        <div>出祧男丁及后代</div>
        <div>0</div>
      </div>
      <div class="item">
        <div>女性人数</div>
        <div>0</div>
      </div>
      <div class="item">
        <div>兼祧男丁及后代</div>
        <div>0</div>
      </div>
      <div class="item">
        <div>男族丁数</div>
        <div>253</div>
      </div>
      <div class="item">
        <div>出嗣男丁及后代</div>
        <div>0</div>
      </div>
      <div class="item">
        <div>女族丁数</div>
        <div>0</div>
      </div>
      <div class="item">
        <div>承嗣男丁及后代</div>
        <div>0</div>
      </div>
    </div>
  </a-modal>
</template>
<script>
export default {
  name: 'TongjiModal',
  data () {
    return {
      visible: true
      //
    }
  },
  methods: {
    handleOk () { this.$emit('close') },
    handleCancel () { this.$emit('close') }
  }
}
</script>
<style lang="less" scoped>
.node {
  font-size: 16px;
}
.flex{
  display: flex;
  flex-wrap: wrap;
  .item{
    width: 50%;
    display: flex;
    margin-bottom: 10px;
  }
}
</style>
