import request from '@/utils/request'

/**
 * @typedef {object} ClanGroupSaveItem
 * @property {number} [id] - 分组id，为0时新增分组，有id为更新
 * @property {string} group_name - 分组名
 * @property {number} sort_order - 排序权重（0-99）
 * @property {number} [status] - 状态（可不传），1启用，2禁用
 */

const groupApi = {
  SaveGroup: '/web/clan/saveGroup'
}

/**
 * 保存族谱分组
 * @param {object} parameter - 请求体
 * @param {Array<ClanGroupSaveItem>} parameter.list - 分组列表
 * @returns {Promise<object>}
 */
export async function SaveGroup (parameter) {
  return request({
    url: groupApi.SaveGroup,
    method: 'post',
    data: parameter
  })
}
