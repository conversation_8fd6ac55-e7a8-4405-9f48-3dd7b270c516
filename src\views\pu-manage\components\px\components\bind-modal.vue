<template>
  <a-modal
    width="420px"
    v-model="visible"
    class="add-binding-modal-wrap"
    title="绑定用户"
    @ok="handleOk"
    @cancel="$emit('close')"
  >
    <a-form :form="form" layout="vertical" @submit="handleSubmit">
      <a-form-item label="请输入对方的谱号(请在百家有谱手机版'我的'里查看)">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: true, message: '请输入对方的谱号' }] }]" />
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" html-type="submit" :loading="loading">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { getHandleName } from '@/utils/nameUtil'

export default {
  name: 'BindModal',
  data  () {
    return {
      visible: true,
      loading: false,
      form: this.$form.createForm(this)
    }
  },
  props: {},
  methods: {
    handleOk () {
    },
    handleSubmit (e) {
      e.preventDefault()
      const that = this
      this.form.validateFields(async (err, values) => {
        if (!err) {
          const { firstName, name, fullName } = getHandleName(values.name)
          const params = {
            'clan_id': this.currentSelectedClanId,
            'pedigree_id': this.currentSelectedPedigreeId,
            'generation': 1,
            'first_name': firstName,
            'name': name,
            'full_name': fullName,
            'ranking': 1,
            'ranking_text': '始祖',
            'relation': '生',
            'sex': '男'
          }
          this.loading = true
         this.$message.info('该功能暂未实现')
          console.log(params)
          this.loading = false
          that.$emit('close')
        }
      })
    }
  }
}
</script>
<style scoped lang='less'>
.add-binding-modal-wrap {
  /deep/ .ant-modal-footer { display:none;}
}
</style>
