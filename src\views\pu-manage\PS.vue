<template>
  <div class="pushu-page">
    <ps-header />
    <div class="flex">
      <left-content-for-p-s/>
      <right-content-for-ps />
    </div>
  </div>
</template>

<script>
import LeftContentForPS from '@/views/pu-manage/components/ps/left-content'
import PsHeader from '@/views/pu-manage/components/ps/top'
import RightContentForPs from '@/views/pu-manage/components/ps/right-content'
export default {
  name: 'PSPage',
  components: { RightContentForPs, PsHeader, LeftContentForPS },
  data () {
    return {
    }
  },
  mounted () {
  },
  methods: {
  }
}
</script>
<style lang="less" scoped>
.hard{
  text-align: center;
}
.pushu-page {
  overflow: hidden;
  width: 100%;
  text-align: center;
}
.page {
  background: white;
  text-align: center;

  img {
    visibility:hidden;
  }
}
.turn-page {
  img {
    visibility:visible;
  }
}
</style>
