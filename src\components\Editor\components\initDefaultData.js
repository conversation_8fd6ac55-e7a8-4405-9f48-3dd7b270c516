import * as types from '@/components/Editor/components/types'
import { v4 as uuidv4 } from 'uuid'

export const TextBoxData = {
    'pageSetting': { 'padding': [0, 0, 0, 0] },
    'pageNum': 1,
    'items': [
      {
        'id': `${uuidv4()}`,
        'name': '文本框',
        'type': types.TextBox,
        'content': '',
        'left': 0,
        'top': 0,
        'width': 0,
        'height': 0,
        'style': {
          'fontWeight': 'bold',
          'textDecoration': '',
          'fontStyle': '',
          'fontSize': '29px',
          'fontFamily': '宋体',
          'textAlign': 'center',
          'lineHeight': '200%',
          'textAlignLast': '',
          'letterSpacing': '3px',
          'textIndent': '',
          'color': 'rgba(0, 0, 0, 1)',
          'writingMode': 'vertical-rl',
          'flexDirection': 'column',
          'alignItems': 'center',
          'direction': '',
          'unicodeBidi': ''
        }
      }
    ]
}
